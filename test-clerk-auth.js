const https = require('https');

// Test if the Clerk JWT authentication is working on the deployed endpoint
async function testClerkAuth() {
  console.log('🧪 Testing Clerk JWT authentication on /api/events...\n');
  
  // Sample LLM completion data
  const testData = {
    type: 'LLM Completion',
    properties: {
      inputTokens: 11787,
      outputTokens: 153,
      cacheWriteTokens: 0,
      cacheReadTokens: 0,
      cost: 0.000929925,
      modelId: 'gemini-1.5-flash-002',
      provider: 'gemini'
    }
  };

  const postData = JSON.stringify(testData);

  // Test with a fake JWT token to see the error response
  const fakeJwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

  const options = {
    hostname: 'app.cubent.dev',
    port: 443,
    path: '/api/events',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'Authorization': `Bearer ${fakeJwtToken}`
    }
  };

  console.log('📡 Testing with fake JWT token...');
  console.log('Token:', fakeJwtToken.substring(0, 50) + '...');
  
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log(`\n📊 Response Status: ${res.statusCode}`);
      console.log('📋 Response Headers:');
      Object.entries(res.headers).forEach(([key, value]) => {
        if (key.toLowerCase().includes('clerk') || key.toLowerCase().includes('auth')) {
          console.log(`   ${key}: ${value}`);
        }
      });
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('\n📄 Response Body:', data);
        
        if (res.statusCode === 401) {
          console.log('\n❌ Got 401 Unauthorized');
          
          // Check if it's a Clerk-specific error
          if (res.headers['x-clerk-auth-reason']) {
            console.log(`🔍 Clerk Auth Reason: ${res.headers['x-clerk-auth-reason']}`);
            console.log(`🔍 Clerk Auth Status: ${res.headers['x-clerk-auth-status']}`);
            
            if (res.headers['x-clerk-auth-reason'] === 'session-token-and-uat-missing') {
              console.log('\n💡 This suggests the endpoint is expecting Clerk authentication');
              console.log('💡 But our Clerk JWT validation code might not be working');
            }
          }
          
          // Parse the response to see if it mentions API keys
          try {
            const responseData = JSON.parse(data);
            if (responseData.error && responseData.error.includes('API key')) {
              console.log('\n🚨 The endpoint is still looking for API keys instead of Clerk JWT!');
              console.log('🚨 This means our authentication fix was not deployed properly');
            }
          } catch (e) {
            // Response is not JSON
          }
        } else if (res.statusCode === 200) {
          console.log('\n✅ Success! The endpoint accepted the request');
        } else {
          console.log(`\n❓ Unexpected status code: ${res.statusCode}`);
        }
        
        resolve(data);
      });
    });

    req.on('error', (e) => {
      console.error('❌ Request error:', e);
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

// Run the test
testClerkAuth().catch(console.error);
