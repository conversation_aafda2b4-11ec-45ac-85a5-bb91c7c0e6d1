{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/bibtex.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"BibTeX\", \"name\": \"bibtex\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.bibtex\" } }, \"match\": \"@(?i:comment)(?=[\\\\s{(])\", \"name\": \"comment.block.at-sign.bibtex\" }, { \"begin\": \"((@)(?i:preamble))\\\\s*(\\\\{)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.preamble.bibtex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.bibtex\" }, \"3\": { \"name\": \"punctuation.section.preamble.begin.bibtex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.preamble.end.bibtex\" } }, \"name\": \"meta.preamble.braces.bibtex\", \"patterns\": [{ \"include\": \"#field_value\" }] }, { \"begin\": \"((@)(?i:preamble))\\\\s*(\\\\()\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.preamble.bibtex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.bibtex\" }, \"3\": { \"name\": \"punctuation.section.preamble.begin.bibtex\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.preamble.end.bibtex\" } }, \"name\": \"meta.preamble.parenthesis.bibtex\", \"patterns\": [{ \"include\": \"#field_value\" }] }, { \"begin\": \"((@)(?i:string))\\\\s*(\\\\{)\\\\s*([a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.string-constant.bibtex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.bibtex\" }, \"3\": { \"name\": \"punctuation.section.string-constant.begin.bibtex\" }, \"4\": { \"name\": \"variable.other.bibtex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.string-constant.end.bibtex\" } }, \"name\": \"meta.string-constant.braces.bibtex\", \"patterns\": [{ \"include\": \"#field_value\" }] }, { \"begin\": \"((@)(?i:string))\\\\s*(\\\\()\\\\s*([a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.string-constant.bibtex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.bibtex\" }, \"3\": { \"name\": \"punctuation.section.string-constant.begin.bibtex\" }, \"4\": { \"name\": \"variable.other.bibtex\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.string-constant.end.bibtex\" } }, \"name\": \"meta.string-constant.parenthesis.bibtex\", \"patterns\": [{ \"include\": \"#field_value\" }] }, { \"begin\": \"((@)[a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*)\\\\s*(\\\\{)\\\\s*([^\\\\s,}]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.entry-type.bibtex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.bibtex\" }, \"3\": { \"name\": \"punctuation.section.entry.begin.bibtex\" }, \"4\": { \"name\": \"entity.name.type.entry-key.bibtex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.entry.end.bibtex\" } }, \"name\": \"meta.entry.braces.bibtex\", \"patterns\": [{ \"begin\": \"([a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*)\\\\s*(=)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.key.bibtex\" }, \"2\": { \"name\": \"punctuation.separator.key-value.bibtex\" } }, \"end\": \"(?=[,}])\", \"name\": \"meta.key-assignment.bibtex\", \"patterns\": [{ \"include\": \"#field_value\" }] }] }, { \"begin\": \"((@)[a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*)\\\\s*(\\\\()\\\\s*([^\\\\s,]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.entry-type.bibtex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.bibtex\" }, \"3\": { \"name\": \"punctuation.section.entry.begin.bibtex\" }, \"4\": { \"name\": \"entity.name.type.entry-key.bibtex\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.entry.end.bibtex\" } }, \"name\": \"meta.entry.parenthesis.bibtex\", \"patterns\": [{ \"begin\": \"([a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*)\\\\s*(=)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.key.bibtex\" }, \"2\": { \"name\": \"punctuation.separator.key-value.bibtex\" } }, \"end\": \"(?=[,)])\", \"name\": \"meta.key-assignment.bibtex\", \"patterns\": [{ \"include\": \"#field_value\" }] }] }, { \"begin\": \"[^@\\\\n]\", \"end\": \"(?=@)\", \"name\": \"comment.block.bibtex\" }], \"repository\": { \"field_value\": { \"patterns\": [{ \"include\": \"#string_content\" }, { \"include\": \"#integer\" }, { \"include\": \"#string_var\" }, { \"match\": \"#\", \"name\": \"keyword.operator.bibtex\" }] }, \"integer\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.bibtex\" } }, \"match\": \"\\\\s*(\\\\d+)\\\\s*\" }, \"nested_braces\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.begin.bibtex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.end.bibtex\" } }, \"patterns\": [{ \"include\": \"#nested_braces\" }] }, \"string_content\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.bibtex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.bibtex\" } }, \"patterns\": [{ \"include\": \"#nested_braces\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.bibtex\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.bibtex\" } }, \"patterns\": [{ \"include\": \"#nested_braces\" }] }] }, \"string_var\": { \"captures\": { \"0\": { \"name\": \"support.variable.bibtex\" } }, \"match\": \"[a-zA-Z!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~][a-zA-Z0-9!$&*+\\\\-./:;<>?@\\\\[\\\\\\\\\\\\]^_`|~]*\" } }, \"scopeName\": \"text.bibtex\" });\nvar bibtex = [\n  lang\n];\n\nexport { bibtex as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAU,QAAQ;IAAU,YAAY;QAAC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;YAA4B,QAAQ;QAA+B;QAAG;YAAE,SAAS;YAAmC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAmC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAqH,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAqH,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,QAAQ;YAA2C,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAmH,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,SAAS;oBAA+F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAY,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAkH,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,SAAS;oBAA+F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAY,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;aAAE;QAAC;QAAG;YAAE,SAAS;YAAW,OAAO;YAAS,QAAQ;QAAuB;KAAE;IAAE,cAAc;QAAE,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA0B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;QAAiB;QAAG,iBAAiB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,SAAS;QAAqF;IAAE;IAAG,aAAa;AAAc;AAC1pK,IAAI,SAAS;IACX;CACD", "ignoreList": [0], "debugId": null}}]}