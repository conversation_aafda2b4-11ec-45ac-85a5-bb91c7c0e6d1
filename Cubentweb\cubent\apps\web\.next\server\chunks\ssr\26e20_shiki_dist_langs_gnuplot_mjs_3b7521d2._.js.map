{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/gnuplot.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Gnuplot\", \"fileTypes\": [\"gp\", \"plt\", \"plot\", \"gnuplot\"], \"name\": \"gnuplot\", \"patterns\": [{ \"match\": \"(\\\\\\\\(?!\\\\n).*)\", \"name\": \"invalid.illegal.backslash.gnuplot\" }, { \"match\": \"(;)\", \"name\": \"punctuation.separator.statement.gnuplot\" }, { \"include\": \"#LineComment\" }, { \"include\": \"#DataBlock\" }, { \"include\": \"#MacroExpansion\" }, { \"include\": \"#VariableDecl\" }, { \"include\": \"#ArrayDecl\" }, { \"include\": \"#FunctionDecl\" }, { \"include\": \"#ShellCommand\" }, { \"include\": \"#Command\" }], \"repository\": { \"ArrayDecl\": { \"begin\": \"\\\\b(?:(array)\\\\s+([A-Za-z_]\\\\w*)?)\", \"beginCaptures\": { \"1\": { \"name\": \"support.type.array.gnuplot\" }, \"2\": { \"name\": \"entity.name.variable.gnuplot\", \"patterns\": [{ \"include\": \"#InvalidVariableDecl\" }, { \"include\": \"#BuiltinVariable\" }] } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"name\": \"meta.variable.gnuplot\", \"patterns\": [{ \"include\": \"#Expression\" }] }, \"BuiltinFunction\": { \"patterns\": [{ \"match\": \"\\\\b(?:defined)\\\\b\", \"name\": \"invalid.deprecated.function.gnuplot\" }, { \"match\": \"\\\\b(?:abs|acos|acosh|airy|arg|asin|asinh|atan|atan2|atanh|EllipticK|EllipticE|EllipticPi|besj0|besj1|besy0|besy1|ceil|cos|cosh|erf|erfc|exp|expint|floor|gamma|ibeta|inverf|igamma|imag|invnorm|int|lambertw|lgamma|log|log10|norm|rand|real|sgn|sin|sinh|sqrt|tan|tanh|voigt|cerf|cdawson|faddeeva|erfi|VP)\\\\b\", \"name\": \"support.function.math.gnuplot\" }, { \"match\": \"\\\\b(?:gprintf|sprintf|strlen|strstrt|substr|strftime|strptime|system|word|words)\\\\b\", \"name\": \"support.function.string.gnuplot\" }, { \"match\": \"\\\\b(?:column|columnhead|exists|hsv2rgb|stringcolumn|timecolumn|tm_hour|tm_mday|tm_min|tm_mon|tm_sec|tm_wday|tm_yday|tm_year|time|valid|value)\\\\b\", \"name\": \"support.function.other.gnuplot\" }] }, \"BuiltinOperator\": { \"patterns\": [{ \"match\": \"(&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.gnuplot\" }, { \"match\": \"(<<|>>|&|\\\\||\\\\^)\", \"name\": \"keyword.operator.bitwise.gnuplot\" }, { \"match\": \"(==|!=|<=|<|>=|>)\", \"name\": \"keyword.operator.comparison.gnuplot\" }, { \"match\": \"(=)\", \"name\": \"keyword.operator.assignment.gnuplot\" }, { \"match\": \"(\\\\+|-|~|!)\", \"name\": \"keyword.operator.arithmetic.gnuplot\" }, { \"match\": \"(\\\\*\\\\*|\\\\+|-|\\\\*|/|%)\", \"name\": \"keyword.operator.arithmetic.gnuplot\" }, { \"captures\": { \"2\": { \"name\": \"keyword.operator.word.gnuplot\" } }, \"match\": \"(\\\\.|\\\\b(eq|ne)\\\\b)\", \"name\": \"keyword.operator.strings.gnuplot\" }] }, \"BuiltinVariable\": { \"patterns\": [{ \"match\": \"\\\\b(?:FIT_LIMIT|FIT_MAXITER|FIT_START_LAMBDA|FIT_LAMBDA_FACTOR|FIT_SKIP|FIT_INDEX)\\\\b\", \"name\": \"invalid.deprecated.variable.gnuplot\" }, { \"match\": \"\\\\b(GPVAL_\\\\w*|MOUSE_\\\\w*)\\\\b\", \"name\": \"support.constant.gnuplot\" }, { \"match\": \"\\\\b(ARG[0-9C]|GPFUN_\\\\w*|FIT_\\\\w*|STATS_\\\\w*|pi|NaN)\\\\b\", \"name\": \"support.variable.gnuplot\" }] }, \"ColumnIndexLiteral\": { \"match\": \"([$]\\\\d+)\\\\b\", \"name\": \"support.constant.columnindex.gnuplot\" }, \"Command\": { \"patterns\": [{ \"begin\": \"\\\\b(?:update)\\\\b\", \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"name\": \"invalid.deprecated.command.gnuplot\" }, { \"begin\": \"\\\\b(?:break|clear|continue|pwd|refresh|replot|reread|shell)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#InvalidWord\" }] }, { \"begin\": \"\\\\b(?:cd|call|eval|exit|help|history|load|lower|pause|print|printerr|quit|raise|save|stats|system|test|toggle)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#Expression\" }] }, { \"begin\": \"\\\\b(import)\\\\s(.+)\\\\s(from)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.import.gnuplot\" }, \"2\": { \"patterns\": [{ \"include\": \"#FunctionDecl\" }] }, \"3\": { \"name\": \"keyword.control.import.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#SingleQuotedStringLiteral\" }, { \"include\": \"#DoubleQuotedStringLiteral\" }, { \"include\": \"#InvalidWord\" }] }, { \"begin\": \"\\\\b(reset)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"match\": \"\\\\b(bind|error(state)?|session)\\\\b\", \"name\": \"support.class.reset.gnuplot\" }, { \"include\": \"#InvalidWord\" }] }, { \"begin\": \"\\\\b(undefine)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#BuiltinVariable\" }, { \"include\": \"#BuiltinFunction\" }, { \"match\": \"(?<=\\\\s)([$]?[A-Za-z_]\\\\w*\\\\*?)(?=\\\\s)\", \"name\": \"source.gnuplot\" }, { \"include\": \"#InvalidWord\" }] }, { \"begin\": \"\\\\b(if|while)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.gnuplot\" } }, \"end\": \"(?=(\\\\{|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#Expression\" }] }, { \"begin\": \"\\\\b(else)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.gnuplot\" } }, \"end\": \"(?=(\\\\{|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\" }, { \"begin\": \"\\\\b(do)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.gnuplot\" } }, \"end\": \"(?=(\\\\{|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#ForIterationExpr\" }] }, { \"begin\": \"\\\\b(set)(?=\\\\s+pm3d)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"match\": \"\\\\b(hidden3d|map|transparent|solid)\\\\b\", \"name\": \"invalid.deprecated.options.gnuplot\" }, { \"include\": \"#SetUnsetOptions\" }, { \"include\": \"#ForIterationExpr\" }, { \"include\": \"#Expression\" }] }, { \"begin\": \"\\\\b((un)?set)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#SetUnsetOptions\" }, { \"include\": \"#ForIterationExpr\" }, { \"include\": \"#Expression\" }] }, { \"begin\": \"\\\\b(show)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#ExtraShowOptions\" }, { \"include\": \"#SetUnsetOptions\" }, { \"include\": \"#Expression\" }] }, { \"begin\": \"\\\\b(fit|(s)?plot)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.command.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#ColumnIndexLiteral\" }, { \"include\": \"#PlotModifiers\" }, { \"include\": \"#ForIterationExpr\" }, { \"include\": \"#Expression\" }] }] }, \"DataBlock\": { \"begin\": \"(?:([$][A-Za-z_]\\\\w*)\\\\s*(<<)\\\\s*([A-Za-z_]\\\\w*)\\\\s*(?=(\\\\#|$)))\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#SpecialVariable\" }] }, \"3\": { \"name\": \"constant.language.datablock.gnuplot\" } }, \"end\": \"^(\\\\3)\\\\b(.*)\", \"endCaptures\": { \"1\": { \"name\": \"constant.language.datablock.gnuplot\" }, \"2\": { \"name\": \"invalid.illegal.datablock.gnuplot\" } }, \"name\": \"meta.datablock.gnuplot\", \"patterns\": [{ \"include\": \"#LineComment\" }, { \"include\": \"#NumberLiteral\" }, { \"include\": \"#DoubleQuotedStringLiteral\" }] }, \"DeprecatedScriptArgsLiteral\": { \"match\": \"([$][0-9#])\", \"name\": \"invalid.illegal.scriptargs.gnuplot\" }, \"DoubleQuotedStringLiteral\": { \"begin\": '(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.gnuplot\" } }, \"end\": '((\")|(?=(?<!\\\\\\\\)\\\\n$))', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.gnuplot\" } }, \"name\": \"string.quoted.double.gnuplot\", \"patterns\": [{ \"include\": \"#EscapedChar\" }, { \"include\": \"#RGBColorSpec\" }, { \"include\": \"#DeprecatedScriptArgsLiteral\" }, { \"include\": \"#InterpolatedStringLiteral\" }] }, \"EscapedChar\": { \"match\": \"(\\\\\\\\.)\", \"name\": \"constant.character.escape.gnuplot\" }, \"Expression\": { \"patterns\": [{ \"include\": \"#Literal\" }, { \"include\": \"#SpecialVariable\" }, { \"include\": \"#BuiltinVariable\" }, { \"include\": \"#BuiltinOperator\" }, { \"include\": \"#TernaryExpr\" }, { \"include\": \"#FunctionCallExpr\" }, { \"include\": \"#SummationExpr\" }] }, \"ExtraShowOptions\": { \"match\": \"\\\\b(?:all|bind|colornames|functions|plot|variables|version)\\\\b\", \"name\": \"support.class.options.gnuplot\" }, \"ForIterationExpr\": { \"begin\": \"\\\\b(?:(for)\\\\s*(\\\\[)\\\\s*(?:([A-Za-z_]\\\\w*)\\\\s+(in)\\\\b)?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.gnuplot\" }, \"2\": { \"patterns\": [{ \"include\": \"#RangeSeparators\" }] }, \"3\": { \"name\": \"variable.other.iterator.gnuplot\" }, \"4\": { \"name\": \"keyword.control.flow.gnuplot\" } }, \"end\": \"((\\\\])|(?=(#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$)))\", \"endCaptures\": { \"2\": { \"patterns\": [{ \"include\": \"#RangeSeparators\" }] } }, \"patterns\": [{ \"include\": \"#Expression\" }, { \"include\": \"#RangeSeparators\" }] }, \"FunctionCallExpr\": { \"begin\": \"\\\\b([A-Za-z_]\\\\w*)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.function.gnuplot\", \"patterns\": [{ \"include\": \"#BuiltinFunction\" }] }, \"2\": { \"name\": \"punctuation.definition.arguments.begin.gnuplot\" } }, \"end\": \"((\\\\))|(?=(#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$)))\", \"endCaptures\": { \"2\": { \"name\": \"punctuation.definition.arguments.end.gnuplot\" } }, \"name\": \"meta.function-call.gnuplot\", \"patterns\": [{ \"include\": \"#Expression\" }] }, \"FunctionDecl\": { \"begin\": \"\\\\b(?:([A-Za-z_]\\\\w*)\\\\s*((\\\\()\\\\s*([A-Za-z_]\\\\w*)\\\\s*(?:(,)\\\\s*([A-Za-z_]\\\\w*)\\\\s*)*(\\\\))))\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.gnuplot\", \"patterns\": [{ \"include\": \"#BuiltinFunction\" }] }, \"2\": { \"name\": \"meta.function.parameters.gnuplot\" }, \"3\": { \"name\": \"punctuation.definition.parameters.begin.gnuplot\" }, \"4\": { \"name\": \"variable.parameter.function.language.gnuplot\" }, \"5\": { \"name\": \"punctuation.separator.parameters.gnuplot\" }, \"6\": { \"name\": \"variable.parameter.function.language.gnuplot\" }, \"7\": { \"name\": \"punctuation.definition.parameters.end.gnuplot\" } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"name\": \"meta.function.gnuplot\", \"patterns\": [{ \"include\": \"#Expression\" }] }, \"InterpolatedStringLiteral\": { \"begin\": \"(`)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.gnuplot\" } }, \"end\": \"((`)|(?=(?<!\\\\\\\\)\\\\n$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.gnuplot\" } }, \"name\": \"string.interpolated.gnuplot\", \"patterns\": [{ \"include\": \"#EscapedChar\" }] }, \"InvalidVariableDecl\": { \"match\": \"\\\\b(GPVAL_\\\\w*|MOUSE_\\\\w*)\\\\b\", \"name\": \"invalid.illegal.variable.gnuplot\" }, \"InvalidWord\": { \"match\": \"([^;#\\\\\\\\\\\\s]+)\", \"name\": \"invalid.illegal.gnuplot\" }, \"LineComment\": { \"begin\": \"(#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.begin.gnuplot\" } }, \"end\": \"(?=(?<!\\\\\\\\)\\\\n$)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.gnuplot\" } }, \"name\": \"comment.line.number-sign.gnuplot\" }, \"Literal\": { \"patterns\": [{ \"include\": \"#NumberLiteral\" }, { \"include\": \"#DeprecatedScriptArgsLiteral\" }, { \"include\": \"#SingleQuotedStringLiteral\" }, { \"include\": \"#DoubleQuotedStringLiteral\" }, { \"include\": \"#InterpolatedStringLiteral\" }] }, \"MacroExpansion\": { \"begin\": \"([@][A-Za-z_]\\\\w*)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#SpecialVariable\" }] } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"include\": \"#Expression\" }] }, \"NumberLiteral\": { \"patterns\": [{ \"match\": \"(?:(((\\\\b\\\\d+)|(?<!\\\\d)))([.]\\\\d+)([Ee][+-]?\\\\d+)?)(cm|in)?\\\\b\", \"name\": \"constant.numeric.float.gnuplot\" }, { \"match\": \"(?:(\\\\b\\\\d+)((([Ee][+-]?\\\\d+\\\\b))|([.]([Ee][+-]?\\\\d+\\\\b)?)))(cm\\\\b|in\\\\b)?\", \"name\": \"constant.numeric.float.gnuplot\" }, { \"match\": \"\\\\b(0[Xx][0-9a-fA-F]+)(cm|in)?\\\\b\", \"name\": \"constant.numeric.hex.gnuplot\" }, { \"match\": \"\\\\b(0+)(cm|in)?\\\\b\", \"name\": \"constant.numeric.dec.gnuplot\" }, { \"match\": \"\\\\b(0[0-7]+)(cm|in)?\\\\b\", \"name\": \"constant.numeric.oct.gnuplot\" }, { \"match\": \"\\\\b(0\\\\d+)(cm|in)?\\\\b\", \"name\": \"invalid.illegal.oct.gnuplot\" }, { \"match\": \"\\\\b(\\\\d+)(cm|in)?\\\\b\", \"name\": \"constant.numeric.dec.gnuplot\" }] }, \"PlotModifiers\": { \"patterns\": [{ \"match\": \"\\\\b(thru)\\\\b\", \"name\": \"invalid.deprecated.plot.gnuplot\" }, { \"match\": \"\\\\b(?:in(dex)?|every|us(ing)?|wi(th)?|via)\\\\b\", \"name\": \"storage.type.plot.gnuplot\" }, { \"match\": \"\\\\b(newhist(ogram)?)\\\\b\", \"name\": \"storage.type.plot.gnuplot\" }] }, \"RGBColorSpec\": { \"match\": \"\\\\G(0x|#)(([0-9a-fA-F]{6})|([0-9a-fA-F]{8}))\\\\b\", \"name\": \"constant.other.placeholder.gnuplot\" }, \"RangeSeparators\": { \"patterns\": [{ \"match\": \"(\\\\[)\", \"name\": \"punctuation.section.brackets.begin.gnuplot\" }, { \"match\": \"(:)\", \"name\": \"punctuation.separator.range.gnuplot\" }, { \"match\": \"(\\\\])\", \"name\": \"punctuation.section.brackets.end.gnuplot\" }] }, \"SetUnsetOptions\": { \"patterns\": [{ \"match\": \"\\\\G\\\\s*\\\\b(?:clabel|data|function|historysize|macros|ticslevel|ticscale|(style\\\\s+increment\\\\s+\\\\w+))\\\\b\", \"name\": \"invalid.deprecated.options.gnuplot\" }, { \"match\": \"\\\\G\\\\s*\\\\b(?:angles|arrow|autoscale|border|boxwidth|clip|cntr(label|param)|color(box|sequence)?|contour|(dash|line)type|datafile|decimal(sign)?|dgrid3d|dummy|encoding|(error)?bars|fit|fontpath|format|grid|hidden3d|history|(iso)?samples|jitter|key|label|link|loadpath|locale|logscale|mapping|[lrtb]margin|margins|micro|minus(sign)?|mono(chrome)?|mouse|multiplot|nonlinear|object|offsets|origin|output|parametric|(p|r)axis|pm3d|palette|pointintervalbox|pointsize|polar|print|psdir|size|style|surface|table|terminal|termoption|theta|tics|timestamp|timefmt|title|view|xyplane|zero|(no)?(m)?(x|x2|y|y2|z|cb|r|t)tics|(x|x2|y|y2|z|cb)data|(x|x2|y|y2|z|cb|r)label|(x|x2|y|y2|z|cb)dtics|(x|x2|y|y2|z|cb)mtics|(x|x2|y|y2|z|cb|[rtuv])range|(x|x2|y|y2|z)?zeroaxis)\\\\b\", \"name\": \"support.class.options.gnuplot\" }] }, \"ShellCommand\": { \"begin\": \"(!)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.shell.gnuplot\" } }, \"end\": \"(?=(#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"patterns\": [{ \"match\": \"([^#]|\\\\\\\\(?=\\\\n))\", \"name\": \"string.unquoted\" }] }, \"SingleQuotedStringLiteral\": { \"begin\": \"(')\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.gnuplot\" } }, \"end\": \"((')(?!')|(?=(?<!\\\\\\\\)\\\\n$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.gnuplot\" } }, \"name\": \"string.quoted.single.gnuplot\", \"patterns\": [{ \"include\": \"#RGBColorSpec\" }, { \"match\": \"('')\", \"name\": \"constant.character.escape.gnuplot\" }] }, \"SpecialVariable\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.language.wildcard.gnuplot\" } }, \"match\": \"(?<=[\\\\[:=])\\\\s*(\\\\*)\\\\s*(?=[:\\\\]])\" }, { \"captures\": { \"2\": { \"name\": \"punctuation.definition.variable.gnuplot\" } }, \"match\": \"(([@$])[A-Za-z_]\\\\w*)\\\\b\", \"name\": \"constant.language.special.gnuplot\" }] }, \"SummationExpr\": { \"begin\": \"\\\\b(sum)\\\\s*(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.sum.gnuplot\" }, \"2\": { \"patterns\": [{ \"include\": \"#RangeSeparators\" }] } }, \"end\": \"((\\\\])|(?=(#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$)))\", \"endCaptures\": { \"2\": { \"patterns\": [{ \"include\": \"#RangeSeparators\" }] } }, \"patterns\": [{ \"include\": \"#Expression\" }, { \"include\": \"#RangeSeparators\" }] }, \"TernaryExpr\": { \"begin\": \"(?<!\\\\?)(\\\\?)(?!\\\\?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.gnuplot\" } }, \"end\": \"((?<!:)(:)(?!:)|(?=(#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$)))\", \"endCaptures\": { \"2\": { \"name\": \"keyword.operator.ternary.gnuplot\" } }, \"patterns\": [{ \"include\": \"#Expression\" }] }, \"VariableDecl\": { \"begin\": \"\\\\b(?:([A-Za-z_]\\\\w*)\\\\s*(?:(\\\\[)\\\\s*(.*)\\\\s*(\\\\])\\\\s*)?(?=(=)(?!\\\\s*=)))\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.variable.gnuplot\", \"patterns\": [{ \"include\": \"#InvalidVariableDecl\" }, { \"include\": \"#BuiltinVariable\" }] }, \"3\": { \"patterns\": [{ \"include\": \"#Expression\" }] } }, \"end\": \"(?=(;|#|\\\\\\\\(?!\\\\n)|(?<!\\\\\\\\)\\\\n$))\", \"name\": \"meta.variable.gnuplot\", \"patterns\": [{ \"include\": \"#Expression\" }] } }, \"scopeName\": \"source.gnuplot\" });\nvar gnuplot = [\n  lang\n];\n\nexport { gnuplot as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAM;QAAO;QAAQ;KAAU;IAAE,QAAQ;IAAW,YAAY;QAAC;YAAE,SAAS;YAAmB,QAAQ;QAAoC;QAAG;YAAE,SAAS;YAAO,QAAQ;QAA0C;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,aAAa;YAAE,SAAS;YAAsC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAuC,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAmT,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAuF,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAoJ,QAAQ;gBAAiC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;oBAAuB,QAAQ;gBAAmC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyF,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAA2D,QAAQ;gBAA2B;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAgB,QAAQ;QAAuC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,OAAO;oBAAuC,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAkE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqH,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAA6B;wBAAG;4BAAE,WAAW;wBAA6B;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,SAAS;4BAAsC,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,SAAS;4BAA0C,QAAQ;wBAAiB;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAyC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;gBAAwC;gBAAG;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAyC,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,SAAS;4BAA0C,QAAQ;wBAAqC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAoE,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAiB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAAe,QAAQ;QAAqC;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAW,QAAQ;QAAoC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAkE,QAAQ;QAAgC;QAAG,oBAAoB;YAAE,SAAS;YAA4D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAA6C,eAAe;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA+B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAA6C,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAgG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,OAAO;YAAuC,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAiC,QAAQ;QAAmC;QAAG,eAAe;YAAE,SAAS;YAAmB,QAAQ;QAA0B;QAAG,eAAe;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAqB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;QAAmC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkE,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA8E,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAA+B;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAiD,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA4B;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAmD,QAAQ;QAAqC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAA6C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA2C;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4G,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAuvB,QAAQ;gBAAgC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAqC,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,QAAQ;gBAAkB;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAgC,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAoC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;oBAA4B,QAAQ;gBAAoC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAA6C,eAAe;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAwB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAsD,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAA6E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAuC,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;IAAE;IAAG,aAAa;AAAiB;AACtke,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}