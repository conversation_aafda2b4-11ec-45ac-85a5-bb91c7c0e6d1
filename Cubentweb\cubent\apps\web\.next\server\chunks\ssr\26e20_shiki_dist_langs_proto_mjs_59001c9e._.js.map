{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/proto.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Protocol Buffer 3\", \"fileTypes\": [\"proto\"], \"name\": \"proto\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#syntax\" }, { \"include\": \"#package\" }, { \"include\": \"#import\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#message\" }, { \"include\": \"#enum\" }, { \"include\": \"#service\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.proto\" }, { \"begin\": \"//\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.double-slash.proto\" }] }, \"constants\": { \"match\": \"\\\\b(true|false|max|[A-Z_]+)\\\\b\", \"name\": \"constant.language.proto\" }, \"enum\": { \"begin\": \"(enum)(\\\\s+)([A-Za-z][A-Za-z0-9_]*)(\\\\s*)(\\\\{)?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"3\": { \"name\": \"entity.name.class.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#reserved\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#comments\" }, { \"begin\": \"([A-Za-z][A-Za-z0-9_]*)\\\\s*(=)\\\\s*(0[xX][0-9a-fA-F]+|\\\\d+)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.proto\" }, \"2\": { \"name\": \"keyword.operator.assignment.proto\" }, \"3\": { \"name\": \"constant.numeric.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#fieldOptions\" }] }] }, \"field\": { \"begin\": \"\\\\s*(optional|repeated|required)?\\\\s*\\\\b([\\\\w.]+)\\\\s+(\\\\w+)\\\\s*(=)\\\\s*(0[xX][0-9a-fA-F]+|\\\\d+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.proto\" }, \"2\": { \"name\": \"storage.type.proto\" }, \"3\": { \"name\": \"variable.other.proto\" }, \"4\": { \"name\": \"keyword.operator.assignment.proto\" }, \"5\": { \"name\": \"constant.numeric.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#fieldOptions\" }] }, \"fieldOptions\": { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#subMsgOption\" }, { \"include\": \"#optionName\" }] }, \"ident\": { \"match\": \"[A-Za-z][A-Za-z0-9_]*\", \"name\": \"entity.name.class.proto\" }, \"import\": { \"captures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"keyword.other.proto\" }, \"3\": { \"name\": \"string.quoted.double.proto.import\" }, \"4\": { \"name\": \"punctuation.terminator.proto\" } }, \"match\": '\\\\s*(import)\\\\s+(weak|public)?\\\\s*(\"[^\"]+\")\\\\s*(;)' }, \"kv\": { \"begin\": \"(\\\\w+)\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"punctuation.separator.key-value.proto\" } }, \"end\": \"(;)|,|(?=[}/_a-zA-Z])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#subMsgOption\" }] }, \"mapfield\": { \"begin\": \"\\\\s*(map)\\\\s*(<)\\\\s*([\\\\w.]+)\\\\s*,\\\\s*([\\\\w.]+)\\\\s*(>)\\\\s+(\\\\w+)\\\\s*(=)\\\\s*(\\\\d+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.proto\" }, \"2\": { \"name\": \"punctuation.definition.typeparameters.begin.proto\" }, \"3\": { \"name\": \"storage.type.proto\" }, \"4\": { \"name\": \"storage.type.proto\" }, \"5\": { \"name\": \"punctuation.definition.typeparameters.end.proto\" }, \"6\": { \"name\": \"variable.other.proto\" }, \"7\": { \"name\": \"keyword.operator.assignment.proto\" }, \"8\": { \"name\": \"constant.numeric.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#fieldOptions\" }] }, \"message\": { \"begin\": \"(message|extend)(\\\\s+)([A-Za-z_][A-Za-z0-9_.]*)(\\\\s*)(\\\\{)?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"3\": { \"name\": \"entity.name.class.message.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#reserved\" }, { \"include\": \"$self\" }, { \"include\": \"#enum\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#comments\" }, { \"include\": \"#oneof\" }, { \"include\": \"#field\" }, { \"include\": \"#mapfield\" }] }, \"method\": { \"begin\": \"(rpc)\\\\s+([A-Za-z][A-Za-z0-9_]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"entity.name.function\" } }, \"end\": \"\\\\}|(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#rpcKeywords\" }, { \"include\": \"#ident\" }] }, \"number\": { \"match\": \"\\\\b((0(x|X)[0-9a-fA-F]*)|((\\\\d+\\\\.?\\\\d*)|(\\\\.\\\\d+))((e|E)(\\\\+|-)?\\\\d+)?)\\\\b\", \"name\": \"constant.numeric.proto\" }, \"oneof\": { \"begin\": \"(oneof)\\\\s+([A-Za-z][A-Za-z0-9_]*)\\\\s*\\\\{?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"variable.other.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#optionStmt\" }, { \"include\": \"#comments\" }, { \"include\": \"#field\" }] }, \"optionName\": { \"captures\": { \"1\": { \"name\": \"support.other.proto\" }, \"2\": { \"name\": \"support.other.proto\" }, \"3\": { \"name\": \"support.other.proto\" } }, \"match\": \"(\\\\w+|\\\\(\\\\w+(\\\\.\\\\w+)*\\\\))(\\\\.\\\\w+)*\" }, \"optionStmt\": { \"begin\": \"(option)\\\\s+(\\\\w+|\\\\(\\\\w+(\\\\.\\\\w+)*\\\\))(\\\\.\\\\w+)*\\\\s*(=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"support.other.proto\" }, \"3\": { \"name\": \"support.other.proto\" }, \"4\": { \"name\": \"support.other.proto\" }, \"5\": { \"name\": \"keyword.operator.assignment.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#subMsgOption\" }] }, \"package\": { \"captures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"string.unquoted.proto.package\" }, \"3\": { \"name\": \"punctuation.terminator.proto\" } }, \"match\": \"\\\\s*(package)\\\\s+([\\\\w.]+)\\\\s*(;)\" }, \"reserved\": { \"begin\": \"(reserved)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.proto\" }, \"3\": { \"name\": \"keyword.other.proto\" }, \"4\": { \"name\": \"constant.numeric.proto\" } }, \"match\": \"(\\\\d+)(\\\\s+(to)\\\\s+(\\\\d+))?\" }, { \"include\": \"#string\" }] }, \"rpcKeywords\": { \"match\": \"\\\\b(stream|returns)\\\\b\", \"name\": \"keyword.other.proto\" }, \"service\": { \"begin\": \"(service)\\\\s+([A-Za-z][A-Za-z0-9_.]*)\\\\s*\\\\{?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"entity.name.class.message.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#method\" }] }, \"storagetypes\": { \"match\": \"\\\\b(double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes)\\\\b\", \"name\": \"storage.type.proto\" }, \"string\": { \"match\": `('([^']|\\\\')*')|(\"([^\"]|\\\\\")*\")`, \"name\": \"string.quoted.double.proto\" }, \"subMsgOption\": { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#kv\" }, { \"include\": \"#comments\" }] }, \"syntax\": { \"captures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"keyword.operator.assignment.proto\" }, \"3\": { \"name\": \"string.quoted.double.proto.syntax\" }, \"4\": { \"name\": \"punctuation.terminator.proto\" } }, \"match\": '\\\\s*(syntax)\\\\s*(=)\\\\s*(\"proto[23]\")\\\\s*(;)' } }, \"scopeName\": \"source.proto\", \"aliases\": [\"protobuf\"] });\nvar proto = [\n  lang\n];\n\nexport { proto as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAqB,aAAa;QAAC;KAAQ;IAAE,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,OAAO;oBAAQ,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAM,OAAO;oBAAS,QAAQ;gBAAkC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAkC,QAAQ;QAA0B;QAAG,QAAQ;YAAE,SAAS;YAAmD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAkG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAO,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAyB,QAAQ;QAA0B;QAAG,UAAU;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAAqD;QAAG,MAAM;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAqF,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoD;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAA+D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAA+E,QAAQ;QAAyB;QAAG,SAAS;YAAE,SAAS;YAA8C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;QAAwC;QAAG,cAAc;YAAE,SAAS;YAA4D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAAoC;QAAG,YAAY;YAAE,SAAS;YAAkB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;gBAA8B;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAA0B,QAAQ;QAAsB;QAAG,WAAW;YAAE,SAAS;YAAiD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAoH,QAAQ;QAAqB;QAAG,UAAU;YAAE,SAAS,CAAC,+BAA+B,CAAC;YAAE,QAAQ;QAA6B;QAAG,gBAAgB;YAAE,SAAS;YAAO,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAM;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA8C;IAAE;IAAG,aAAa;IAAgB,WAAW;QAAC;KAAW;AAAC;AACv5N,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}