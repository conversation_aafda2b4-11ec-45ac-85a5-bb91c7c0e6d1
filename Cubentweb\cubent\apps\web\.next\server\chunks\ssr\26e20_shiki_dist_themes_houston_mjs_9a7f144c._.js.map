{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/themes/houston.mjs"], "sourcesContent": ["var houston = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#343841\",\n    \"activityBar.background\": \"#17191e\",\n    \"activityBar.border\": \"#343841\",\n    \"activityBar.foreground\": \"#eef0f9\",\n    \"activityBar.inactiveForeground\": \"#858b98\",\n    \"activityBarBadge.background\": \"#4bf3c8\",\n    \"activityBarBadge.foreground\": \"#000000\",\n    \"badge.background\": \"#bfc1c9\",\n    \"badge.foreground\": \"#17191e\",\n    \"breadcrumb.activeSelectionForeground\": \"#eef0f9\",\n    \"breadcrumb.background\": \"#17191e\",\n    \"breadcrumb.focusForeground\": \"#eef0f9\",\n    \"breadcrumb.foreground\": \"#858b98\",\n    \"button.background\": \"#4bf3c8\",\n    \"button.foreground\": \"#17191e\",\n    \"button.hoverBackground\": \"#31c19c\",\n    \"button.secondaryBackground\": \"#545864\",\n    \"button.secondaryForeground\": \"#eef0f9\",\n    \"button.secondaryHoverBackground\": \"#858b98\",\n    \"checkbox.background\": \"#23262d\",\n    \"checkbox.border\": \"#00000000\",\n    \"checkbox.foreground\": \"#eef0f9\",\n    \"debugExceptionWidget.background\": \"#23262d\",\n    \"debugExceptionWidget.border\": \"#8996d5\",\n    \"debugToolBar.background\": \"#000\",\n    \"debugToolBar.border\": \"#ffffff00\",\n    \"diffEditor.border\": \"#ffffff00\",\n    \"diffEditor.insertedTextBackground\": \"#4bf3c824\",\n    \"diffEditor.removedTextBackground\": \"#dc365724\",\n    \"dropdown.background\": \"#23262d\",\n    \"dropdown.border\": \"#00000000\",\n    \"dropdown.foreground\": \"#eef0f9\",\n    \"editor.background\": \"#17191e\",\n    \"editor.findMatchBackground\": \"#515c6a\",\n    \"editor.findMatchBorder\": \"#74879f\",\n    \"editor.findMatchHighlightBackground\": \"#ea5c0055\",\n    \"editor.findMatchHighlightBorder\": \"#ffffff00\",\n    \"editor.findRangeHighlightBackground\": \"#23262d\",\n    \"editor.findRangeHighlightBorder\": \"#b2434300\",\n    \"editor.foldBackground\": \"#ad5dca26\",\n    \"editor.foreground\": \"#eef0f9\",\n    \"editor.hoverHighlightBackground\": \"#5495d740\",\n    \"editor.inactiveSelectionBackground\": \"#2a2d34\",\n    \"editor.lineHighlightBackground\": \"#23262d\",\n    \"editor.lineHighlightBorder\": \"#ffffff00\",\n    \"editor.rangeHighlightBackground\": \"#ffffff0b\",\n    \"editor.rangeHighlightBorder\": \"#ffffff00\",\n    \"editor.selectionBackground\": \"#ad5dca44\",\n    \"editor.selectionHighlightBackground\": \"#add6ff34\",\n    \"editor.selectionHighlightBorder\": \"#495f77\",\n    \"editor.wordHighlightBackground\": \"#494949b8\",\n    \"editor.wordHighlightStrongBackground\": \"#004972b8\",\n    \"editorBracketMatch.background\": \"#545864\",\n    \"editorBracketMatch.border\": \"#ffffff00\",\n    \"editorCodeLens.foreground\": \"#bfc1c9\",\n    \"editorCursor.background\": \"#000000\",\n    \"editorCursor.foreground\": \"#aeafad\",\n    \"editorError.background\": \"#ffffff00\",\n    \"editorError.border\": \"#ffffff00\",\n    \"editorError.foreground\": \"#f4587e\",\n    \"editorGroup.border\": \"#343841\",\n    \"editorGroup.emptyBackground\": \"#17191e\",\n    \"editorGroupHeader.border\": \"#ffffff00\",\n    \"editorGroupHeader.tabsBackground\": \"#23262d\",\n    \"editorGroupHeader.tabsBorder\": \"#ffffff00\",\n    \"editorGutter.addedBackground\": \"#4bf3c8\",\n    \"editorGutter.background\": \"#17191e\",\n    \"editorGutter.commentRangeForeground\": \"#545864\",\n    \"editorGutter.deletedBackground\": \"#f06788\",\n    \"editorGutter.foldingControlForeground\": \"#545864\",\n    \"editorGutter.modifiedBackground\": \"#54b9ff\",\n    \"editorHoverWidget.background\": \"#252526\",\n    \"editorHoverWidget.border\": \"#454545\",\n    \"editorHoverWidget.foreground\": \"#cccccc\",\n    \"editorIndentGuide.activeBackground\": \"#858b98\",\n    \"editorIndentGuide.background\": \"#343841\",\n    \"editorInfo.background\": \"#4490bf00\",\n    \"editorInfo.border\": \"#4490bf00\",\n    \"editorInfo.foreground\": \"#54b9ff\",\n    \"editorLineNumber.activeForeground\": \"#858b98\",\n    \"editorLineNumber.foreground\": \"#545864\",\n    \"editorLink.activeForeground\": \"#54b9ff\",\n    \"editorMarkerNavigation.background\": \"#23262d\",\n    \"editorMarkerNavigationError.background\": \"#dc3657\",\n    \"editorMarkerNavigationInfo.background\": \"#54b9ff\",\n    \"editorMarkerNavigationWarning.background\": \"#ffd493\",\n    \"editorOverviewRuler.background\": \"#ffffff00\",\n    \"editorOverviewRuler.border\": \"#ffffff00\",\n    \"editorRuler.foreground\": \"#545864\",\n    \"editorSuggestWidget.background\": \"#252526\",\n    \"editorSuggestWidget.border\": \"#454545\",\n    \"editorSuggestWidget.foreground\": \"#d4d4d4\",\n    \"editorSuggestWidget.highlightForeground\": \"#0097fb\",\n    \"editorSuggestWidget.selectedBackground\": \"#062f4a\",\n    \"editorWarning.background\": \"#a9904000\",\n    \"editorWarning.border\": \"#ffffff00\",\n    \"editorWarning.foreground\": \"#fbc23b\",\n    \"editorWhitespace.foreground\": \"#cc75f450\",\n    \"editorWidget.background\": \"#343841\",\n    \"editorWidget.foreground\": \"#ffffff\",\n    \"editorWidget.resizeBorder\": \"#cc75f4\",\n    \"focusBorder\": \"#00daef\",\n    \"foreground\": \"#cccccc\",\n    \"gitDecoration.addedResourceForeground\": \"#4bf3c8\",\n    \"gitDecoration.conflictingResourceForeground\": \"#00daef\",\n    \"gitDecoration.deletedResourceForeground\": \"#f4587e\",\n    \"gitDecoration.ignoredResourceForeground\": \"#858b98\",\n    \"gitDecoration.modifiedResourceForeground\": \"#ffd493\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#c74e39\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#ffd493\",\n    \"gitDecoration.submoduleResourceForeground\": \"#54b9ff\",\n    \"gitDecoration.untrackedResourceForeground\": \"#4bf3c8\",\n    \"icon.foreground\": \"#cccccc\",\n    \"input.background\": \"#23262d\",\n    \"input.border\": \"#bfc1c9\",\n    \"input.foreground\": \"#eef0f9\",\n    \"input.placeholderForeground\": \"#858b98\",\n    \"inputOption.activeBackground\": \"#54b9ff\",\n    \"inputOption.activeBorder\": \"#007acc00\",\n    \"inputOption.activeForeground\": \"#17191e\",\n    \"list.activeSelectionBackground\": \"#2d4860\",\n    \"list.activeSelectionForeground\": \"#ffffff\",\n    \"list.dropBackground\": \"#17191e\",\n    \"list.focusBackground\": \"#54b9ff\",\n    \"list.focusForeground\": \"#ffffff\",\n    \"list.highlightForeground\": \"#ffffff\",\n    \"list.hoverBackground\": \"#343841\",\n    \"list.hoverForeground\": \"#eef0f9\",\n    \"list.inactiveSelectionBackground\": \"#17191e\",\n    \"list.inactiveSelectionForeground\": \"#eef0f9\",\n    \"listFilterWidget.background\": \"#2d4860\",\n    \"listFilterWidget.noMatchesOutline\": \"#dc3657\",\n    \"listFilterWidget.outline\": \"#54b9ff\",\n    \"menu.background\": \"#252526\",\n    \"menu.border\": \"#00000085\",\n    \"menu.foreground\": \"#cccccc\",\n    \"menu.selectionBackground\": \"#094771\",\n    \"menu.selectionBorder\": \"#00000000\",\n    \"menu.selectionForeground\": \"#4bf3c8\",\n    \"menu.separatorBackground\": \"#bbbbbb\",\n    \"menubar.selectionBackground\": \"#ffffff1a\",\n    \"menubar.selectionForeground\": \"#cccccc\",\n    \"merge.commonContentBackground\": \"#282828\",\n    \"merge.commonHeaderBackground\": \"#383838\",\n    \"merge.currentContentBackground\": \"#27403b\",\n    \"merge.currentHeaderBackground\": \"#367366\",\n    \"merge.incomingContentBackground\": \"#28384b\",\n    \"merge.incomingHeaderBackground\": \"#395f8f\",\n    \"minimap.background\": \"#17191e\",\n    \"minimap.errorHighlight\": \"#dc3657\",\n    \"minimap.findMatchHighlight\": \"#515c6a\",\n    \"minimap.selectionHighlight\": \"#3757b942\",\n    \"minimap.warningHighlight\": \"#fbc23b\",\n    \"minimapGutter.addedBackground\": \"#4bf3c8\",\n    \"minimapGutter.deletedBackground\": \"#f06788\",\n    \"minimapGutter.modifiedBackground\": \"#54b9ff\",\n    \"notificationCenter.border\": \"#ffffff00\",\n    \"notificationCenterHeader.background\": \"#343841\",\n    \"notificationCenterHeader.foreground\": \"#17191e\",\n    \"notificationToast.border\": \"#ffffff00\",\n    \"notifications.background\": \"#343841\",\n    \"notifications.border\": \"#bfc1c9\",\n    \"notifications.foreground\": \"#ffffff\",\n    \"notificationsErrorIcon.foreground\": \"#f4587e\",\n    \"notificationsInfoIcon.foreground\": \"#54b9ff\",\n    \"notificationsWarningIcon.foreground\": \"#ff8551\",\n    \"panel.background\": \"#23262d\",\n    \"panel.border\": \"#17191e\",\n    \"panelSection.border\": \"#17191e\",\n    \"panelTitle.activeBorder\": \"#e7e7e7\",\n    \"panelTitle.activeForeground\": \"#eef0f9\",\n    \"panelTitle.inactiveForeground\": \"#bfc1c9\",\n    \"peekView.border\": \"#007acc\",\n    \"peekViewEditor.background\": \"#001f33\",\n    \"peekViewEditor.matchHighlightBackground\": \"#ff8f0099\",\n    \"peekViewEditor.matchHighlightBorder\": \"#ee931e\",\n    \"peekViewEditorGutter.background\": \"#001f33\",\n    \"peekViewResult.background\": \"#252526\",\n    \"peekViewResult.fileForeground\": \"#ffffff\",\n    \"peekViewResult.lineForeground\": \"#bbbbbb\",\n    \"peekViewResult.matchHighlightBackground\": \"#f00\",\n    \"peekViewResult.selectionBackground\": \"#3399ff33\",\n    \"peekViewResult.selectionForeground\": \"#ffffff\",\n    \"peekViewTitle.background\": \"#1e1e1e\",\n    \"peekViewTitleDescription.foreground\": \"#ccccccb3\",\n    \"peekViewTitleLabel.foreground\": \"#ffffff\",\n    \"pickerGroup.border\": \"#ffffff00\",\n    \"pickerGroup.foreground\": \"#eef0f9\",\n    \"progressBar.background\": \"#4bf3c8\",\n    \"scrollbar.shadow\": \"#000000\",\n    \"scrollbarSlider.activeBackground\": \"#54b9ff66\",\n    \"scrollbarSlider.background\": \"#54586466\",\n    \"scrollbarSlider.hoverBackground\": \"#545864B3\",\n    \"selection.background\": \"#00daef56\",\n    \"settings.focusedRowBackground\": \"#ffffff07\",\n    \"settings.headerForeground\": \"#cccccc\",\n    \"sideBar.background\": \"#23262d\",\n    \"sideBar.border\": \"#17191e\",\n    \"sideBar.dropBackground\": \"#17191e\",\n    \"sideBar.foreground\": \"#bfc1c9\",\n    \"sideBarSectionHeader.background\": \"#343841\",\n    \"sideBarSectionHeader.border\": \"#17191e\",\n    \"sideBarSectionHeader.foreground\": \"#eef0f9\",\n    \"sideBarTitle.foreground\": \"#eef0f9\",\n    \"statusBar.background\": \"#17548b\",\n    \"statusBar.debuggingBackground\": \"#cc75f4\",\n    \"statusBar.debuggingForeground\": \"#eef0f9\",\n    \"statusBar.foreground\": \"#eef0f9\",\n    \"statusBar.noFolderBackground\": \"#6c3c7d\",\n    \"statusBar.noFolderForeground\": \"#eef0f9\",\n    \"statusBarItem.activeBackground\": \"#ffffff25\",\n    \"statusBarItem.hoverBackground\": \"#ffffff1f\",\n    \"statusBarItem.remoteBackground\": \"#297763\",\n    \"statusBarItem.remoteForeground\": \"#eef0f9\",\n    \"tab.activeBackground\": \"#17191e\",\n    \"tab.activeBorder\": \"#ffffff00\",\n    \"tab.activeBorderTop\": \"#eef0f9\",\n    \"tab.activeForeground\": \"#eef0f9\",\n    \"tab.border\": \"#17191e\",\n    \"tab.hoverBackground\": \"#343841\",\n    \"tab.hoverForeground\": \"#eef0f9\",\n    \"tab.inactiveBackground\": \"#23262d\",\n    \"tab.inactiveForeground\": \"#858b98\",\n    \"terminal.ansiBlack\": \"#17191e\",\n    \"terminal.ansiBlue\": \"#2b7eca\",\n    \"terminal.ansiBrightBlack\": \"#545864\",\n    \"terminal.ansiBrightBlue\": \"#54b9ff\",\n    \"terminal.ansiBrightCyan\": \"#00daef\",\n    \"terminal.ansiBrightGreen\": \"#4bf3c8\",\n    \"terminal.ansiBrightMagenta\": \"#cc75f4\",\n    \"terminal.ansiBrightRed\": \"#f4587e\",\n    \"terminal.ansiBrightWhite\": \"#fafafa\",\n    \"terminal.ansiBrightYellow\": \"#ffd493\",\n    \"terminal.ansiCyan\": \"#24c0cf\",\n    \"terminal.ansiGreen\": \"#23d18b\",\n    \"terminal.ansiMagenta\": \"#ad5dca\",\n    \"terminal.ansiRed\": \"#dc3657\",\n    \"terminal.ansiWhite\": \"#eef0f9\",\n    \"terminal.ansiYellow\": \"#ffc368\",\n    \"terminal.border\": \"#80808059\",\n    \"terminal.foreground\": \"#cccccc\",\n    \"terminal.selectionBackground\": \"#ffffff40\",\n    \"terminalCursor.background\": \"#0087ff\",\n    \"terminalCursor.foreground\": \"#ffffff\",\n    \"textLink.foreground\": \"#54b9ff\",\n    \"titleBar.activeBackground\": \"#17191e\",\n    \"titleBar.activeForeground\": \"#cccccc\",\n    \"titleBar.border\": \"#00000000\",\n    \"titleBar.inactiveBackground\": \"#3c3c3c99\",\n    \"titleBar.inactiveForeground\": \"#cccccc99\",\n    \"tree.indentGuidesStroke\": \"#545864\",\n    \"walkThrough.embeddedEditorBackground\": \"#00000050\",\n    \"widget.shadow\": \"#ffffff00\"\n  },\n  \"displayName\": \"Houston\",\n  \"name\": \"houston\",\n  \"semanticHighlighting\": true,\n  \"semanticTokenColors\": {\n    \"enumMember\": {\n      \"foreground\": \"#eef0f9\"\n    },\n    \"variable.constant\": {\n      \"foreground\": \"#ffd493\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#acafff\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": \"punctuation.definition.delayed.unison,punctuation.definition.list.begin.unison,punctuation.definition.list.end.unison,punctuation.definition.ability.begin.unison,punctuation.definition.ability.end.unison,punctuation.operator.assignment.as.unison,punctuation.separator.pipe.unison,punctuation.separator.delimiter.unison,punctuation.definition.hash.unison\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.generic-type.haskell\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.haskell\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.magic.python\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.period.python,punctuation.separator.element.python,punctuation.parenthesis.begin.python,punctuation.parenthesis.end.python\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.language.special.self.python\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.lifetime.rust\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.function.std.rust\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.lifetime.rust\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"variable.language.rust\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.edge\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.character-class.regexp\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string.begin,punctuation.definition.string.end\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"comment markup.link\",\n      \"settings\": {\n        \"foreground\": \"#545864\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed.diff\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"meta.diff.header.from-file,meta.diff.header.to-file,punctuation.definition.from-file.diff,punctuation.definition.to-file.diff\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.c,meta.function.cpp\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.block.begin.bracket.curly.cpp,punctuation.section.block.end.bracket.curly.cpp,punctuation.terminator.statement.c,punctuation.section.block.begin.bracket.curly.c,punctuation.section.block.end.bracket.curly.c,punctuation.section.parens.begin.bracket.round.c,punctuation.section.parens.end.bracket.round.c,punctuation.section.parameters.begin.bracket.round.c,punctuation.section.parameters.end.bracket.round.c\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.key-value\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.expression.import\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.math\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property.math\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.constant\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.annotation.java\",\n        \"storage.type.object.array.java\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"source.java\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.block.begin.java,punctuation.section.block.end.java,punctuation.definition.method-parameters.begin.java,punctuation.definition.method-parameters.end.java,meta.method.identifier.java,punctuation.section.method.begin.java,punctuation.section.method.end.java,punctuation.terminator.java,punctuation.section.class.begin.java,punctuation.section.class.end.java,punctuation.section.inner-class.begin.java,punctuation.section.inner-class.end.java,meta.method-call.java,punctuation.section.class.begin.bracket.curly.java,punctuation.section.class.end.bracket.curly.java,punctuation.section.method.begin.bracket.curly.java,punctuation.section.method.end.bracket.curly.java,punctuation.separator.period.java,punctuation.bracket.angle.java,punctuation.definition.annotation.java,meta.method.body.java\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"meta.method.java\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"storage.modifier.import.java,storage.type.java,storage.type.generic.java\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.instanceof.java\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.definition.variable.name.java\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.bitwise\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.channel\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property-value.scss,support.constant.property-value.css\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.css,keyword.operator.scss,keyword.operator.less\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.color.w3c-standard-color-name.css,support.constant.color.w3c-standard-color-name.scss\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.list.comma.css\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.color.w3c-standard-color-name.css\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"support.type.vendored.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.module.node,support.type.object.module,support.module.node\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.module\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite,meta.object-literal.key,support.variable.property,support.variable.object.process,support.variable.object.node\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.json\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.expression.instanceof\",\n        \"keyword.operator.new\",\n        \"keyword.operator.ternary\",\n        \"keyword.operator.optional\",\n        \"keyword.operator.expression.keyof\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.console\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.property.process\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function,support.function.console\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.misc.rust\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.sigil.rust\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.delete\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.object.dom\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.dom,support.variable.property.dom\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.arithmetic,keyword.operator.comparison,keyword.operator.decrement,keyword.operator.increment,keyword.operator.relational\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment.c,keyword.operator.comparison.c,keyword.operator.c,keyword.operator.increment.c,keyword.operator.decrement.c,keyword.operator.bitwise.shift.c,keyword.operator.assignment.cpp,keyword.operator.comparison.cpp,keyword.operator.cpp,keyword.operator.increment.cpp,keyword.operator.decrement.cpp,keyword.operator.bitwise.shift.cpp\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.delimiter\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.separator.c,punctuation.separator.cpp\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.posix-reserved.c,support.type.posix-reserved.cpp\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.sizeof.c,keyword.operator.sizeof.cpp\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.language.python\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"support.type.python\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical.python\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.python\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.arguments.begin.python,punctuation.definition.arguments.end.python,punctuation.separator.arguments.python,punctuation.definition.list.begin.python,punctuation.definition.list.end.python\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.generic.python\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.format.placeholder.other.python\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment.compound\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment.compound.js,keyword.operator.assignment.compound.ts\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.namespace\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"variable.c\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"token.variable.parameter.java\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"import.storage.java\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"token.package.keyword\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"token.package\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.require\",\n        \"support.function.any-method\",\n        \"variable.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.namespace\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"support.class, entity.name.type.class\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.class.identifier.namespace.type\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"variable.other.class.js\",\n        \"variable.other.class.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.class.php\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"control.elements, keyword.operator.less\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.special-method\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"token.storage\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.expression.delete,keyword.operator.expression.in,keyword.operator.expression.of,keyword.operator.expression.instanceof,keyword.operator.new,keyword.operator.expression.typeof,keyword.operator.expression.void\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"token.storage.type.java\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property-value\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.font-name\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.symbol\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"constant\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.constant\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.html\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"source.astro.meta.attribute.client:idle.html\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.html,string.quoted.single.html,string.template.html,punctuation.definition.string.begin.html,punctuation.definition.string.end.html\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class.css\",\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"meta.selector\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading punctuation.definition.heading, entity.name.section\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold,todo.bold\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic, punctuation.definition.italic,todo.emphasis\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"emphasis md\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.heading.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading.setext\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.bold.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\",\n        \"punctuation.definition.metadata.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.metadata.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"markup.underline.link.markdown,markup.underline.link.image.markdown\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"string.other.link.title.markdown,string.other.link.description.markdown\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.embedded, variable.interpolation\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.embedded.begin,punctuation.section.embedded.end\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal.bad-ampersand.html\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > string.quoted.json\",\n      \"settings\": {\n        \"foreground\": \"#cc75f4\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > value.json > string.quoted.json,source.json meta.structure.array.json > value.json > string.quoted.json,source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation,source.json meta.structure.array.json > value.json > string.quoted.json > punctuation\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"source.json meta.structure.dictionary.json > constant.language.json,source.json meta.structure.array.json > constant.language.json\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json punctuation\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"text.html.laravel-blade source.php.embedded.line.html entity.name.tag.laravel-blade\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"text.html.laravel-blade source.php.embedded.line.html support.constant.laravel-blade\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"support.other.namespace.use.php,support.other.namespace.use-as.php,support.other.namespace.php,entity.other.alias.php,meta.interface.php\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.error-control.php\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.type.php\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.array.begin.php\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.array.end.php\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal.non-null-typehinted.php\",\n      \"settings\": {\n        \"foreground\": \"#f44747\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.php,meta.other.type.phpdoc.php,keyword.other.type.php,keyword.other.array.phpdoc.php\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.php,meta.function-call.object.php,meta.function-call.static.php\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.parameters.begin.bracket.round.php,punctuation.definition.parameters.end.bracket.round.php,punctuation.separator.delimiter.php,punctuation.section.scope.begin.php,punctuation.section.scope.end.php,punctuation.terminator.expression.php,punctuation.definition.arguments.begin.bracket.round.php,punctuation.definition.arguments.end.bracket.round.php,punctuation.definition.storage-type.begin.bracket.round.php,punctuation.definition.storage-type.end.bracket.round.php,punctuation.definition.array.begin.bracket.round.php,punctuation.definition.array.end.bracket.round.php,punctuation.definition.begin.bracket.round.php,punctuation.definition.end.bracket.round.php,punctuation.definition.begin.bracket.curly.php,punctuation.definition.end.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php,punctuation.definition.section.switch-block.start.bracket.curly.php,punctuation.definition.section.switch-block.begin.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.core.rust\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.ext.php,support.constant.std.php,support.constant.core.php,support.constant.parser-token.php\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.goto-label.php,support.other.php\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical.php,keyword.operator.bitwise.php,keyword.operator.arithmetic.php\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.regexp.php\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.comparison.php\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.heredoc.php,keyword.operator.nowdoc.php\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.function.decorator.python\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"support.token.decorator.python,meta.function.decorator.identifier.python\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"function.parameter\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"function.brace\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"function.parameter.ruby, function.parameter.cs\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.symbol.ruby\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"inline-color-decoration rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"less rgb-value\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"selector.sass\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"support.type.primitive.ts,support.type.builtin.ts,support.type.primitive.tsx,support.type.builtin.tsx\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"block.scope.end,block.scope.begin\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.cs\",\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.variable.local.cs\",\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#f44747\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression.begin\",\n        \"punctuation.definition.template-expression.end\",\n        \"punctuation.section.embedded\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.template.expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.module\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.type.flowtype\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.primitive\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.template.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.template.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.substitution.begin\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.substitution.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.assignment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.assignment.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.arithmetic.go\",\n        \"keyword.operator.address.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.package.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.prelude.elm\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.elm\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.quasi.element\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.entity\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.pseudo-element\",\n        \"entity.other.attribute-name.pseudo-class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.global.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.symbol.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.keyword.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.arguments.coffee\",\n        \"variable.parameter.function.coffee\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.ini\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.scope.prerequisites.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier.import.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.method.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.definition.variable.name.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.definition.class.inherited.classes.groovy\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.variable.semantic.hlsl\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.texture.hlsl\",\n        \"support.type.sampler.hlsl\",\n        \"support.type.object.hlsl\",\n        \"support.type.object.rw.hlsl\",\n        \"support.type.fx.hlsl\",\n        \"support.type.object.hlsl\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"text.variable\",\n        \"text.bracketed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.swift\",\n        \"support.type.vb.asp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.character-class.regexp.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.regexp.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#54b9ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.quote.markdown.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f98f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"accent.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#00daef\"\n      }\n    },\n    {\n      \"scope\": [\n        \"wikiword.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffd493\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.color.rgb-value.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag.xi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#545864\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.label.cs\",\n        \"entity.name.scope-resolution.function.call\",\n        \"entity.name.scope-resolution.function.definition\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#acafff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.label.cs\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.setext.2.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#4bf3c8\"\n      }\n    },\n    {\n      \"scope\": [\n        \" meta.brace.square\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"comment, punctuation.definition.comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eef0f98f\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote.markdown\",\n      \"settings\": {\n        \"foreground\": \"#eef0f98f\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.block.sequence.item.yaml\",\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language.symbol.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eef0f9\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.js,entity.other.attribute-name.ts,entity.other.attribute-name.jsx,entity.other.attribute-name.tsx,variable.parameter,variable.language.super\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-slash,comment.block.documentation\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.import.python,keyword.control.flow.python\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { houston as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,OAAO,MAAM,CAAC;IAC1B,UAAU;QACR,gCAAgC;QAChC,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,kCAAkC;QAClC,+BAA+B;QAC/B,+BAA+B;QAC/B,oBAAoB;QACpB,oBAAoB;QACpB,wCAAwC;QACxC,yBAAyB;QACzB,8BAA8B;QAC9B,yBAAyB;QACzB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,mCAAmC;QACnC,+BAA+B;QAC/B,2BAA2B;QAC3B,uBAAuB;QACvB,qBAAqB;QACrB,qCAAqC;QACrC,oCAAoC;QACpC,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,qBAAqB;QACrB,8BAA8B;QAC9B,0BAA0B;QAC1B,uCAAuC;QACvC,mCAAmC;QACnC,uCAAuC;QACvC,mCAAmC;QACnC,yBAAyB;QACzB,qBAAqB;QACrB,mCAAmC;QACnC,sCAAsC;QACtC,kCAAkC;QAClC,8BAA8B;QAC9B,mCAAmC;QACnC,+BAA+B;QAC/B,8BAA8B;QAC9B,uCAAuC;QACvC,mCAAmC;QACnC,kCAAkC;QAClC,wCAAwC;QACxC,iCAAiC;QACjC,6BAA6B;QAC7B,6BAA6B;QAC7B,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,sBAAsB;QACtB,+BAA+B;QAC/B,4BAA4B;QAC5B,oCAAoC;QACpC,gCAAgC;QAChC,gCAAgC;QAChC,2BAA2B;QAC3B,uCAAuC;QACvC,kCAAkC;QAClC,yCAAyC;QACzC,mCAAmC;QACnC,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,sCAAsC;QACtC,gCAAgC;QAChC,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,qCAAqC;QACrC,+BAA+B;QAC/B,+BAA+B;QAC/B,qCAAqC;QACrC,0CAA0C;QAC1C,yCAAyC;QACzC,4CAA4C;QAC5C,kCAAkC;QAClC,8BAA8B;QAC9B,0BAA0B;QAC1B,kCAAkC;QAClC,8BAA8B;QAC9B,kCAAkC;QAClC,2CAA2C;QAC3C,0CAA0C;QAC1C,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,+BAA+B;QAC/B,2BAA2B;QAC3B,2BAA2B;QAC3B,6BAA6B;QAC7B,eAAe;QACf,cAAc;QACd,yCAAyC;QACzC,+CAA+C;QAC/C,2CAA2C;QAC3C,2CAA2C;QAC3C,4CAA4C;QAC5C,gDAAgD;QAChD,iDAAiD;QACjD,6CAA6C;QAC7C,6CAA6C;QAC7C,mBAAmB;QACnB,oBAAoB;QACpB,gBAAgB;QAChB,oBAAoB;QACpB,+BAA+B;QAC/B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,kCAAkC;QAClC,kCAAkC;QAClC,uBAAuB;QACvB,wBAAwB;QACxB,wBAAwB;QACxB,4BAA4B;QAC5B,wBAAwB;QACxB,wBAAwB;QACxB,oCAAoC;QACpC,oCAAoC;QACpC,+BAA+B;QAC/B,qCAAqC;QACrC,4BAA4B;QAC5B,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,sBAAsB;QACtB,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,4BAA4B;QAC5B,iCAAiC;QACjC,mCAAmC;QACnC,oCAAoC;QACpC,6BAA6B;QAC7B,uCAAuC;QACvC,uCAAuC;QACvC,4BAA4B;QAC5B,4BAA4B;QAC5B,wBAAwB;QACxB,4BAA4B;QAC5B,qCAAqC;QACrC,oCAAoC;QACpC,uCAAuC;QACvC,oBAAoB;QACpB,gBAAgB;QAChB,uBAAuB;QACvB,2BAA2B;QAC3B,+BAA+B;QAC/B,iCAAiC;QACjC,mBAAmB;QACnB,6BAA6B;QAC7B,2CAA2C;QAC3C,uCAAuC;QACvC,mCAAmC;QACnC,6BAA6B;QAC7B,iCAAiC;QACjC,iCAAiC;QACjC,2CAA2C;QAC3C,sCAAsC;QACtC,sCAAsC;QACtC,4BAA4B;QAC5B,uCAAuC;QACvC,iCAAiC;QACjC,sBAAsB;QACtB,0BAA0B;QAC1B,0BAA0B;QAC1B,oBAAoB;QACpB,oCAAoC;QACpC,8BAA8B;QAC9B,mCAAmC;QACnC,wBAAwB;QACxB,iCAAiC;QACjC,6BAA6B;QAC7B,sBAAsB;QACtB,kBAAkB;QAClB,0BAA0B;QAC1B,sBAAsB;QACtB,mCAAmC;QACnC,+BAA+B;QAC/B,mCAAmC;QACnC,2BAA2B;QAC3B,wBAAwB;QACxB,iCAAiC;QACjC,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,wBAAwB;QACxB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,cAAc;QACd,uBAAuB;QACvB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,4BAA4B;QAC5B,2BAA2B;QAC3B,2BAA2B;QAC3B,4BAA4B;QAC5B,8BAA8B;QAC9B,0BAA0B;QAC1B,4BAA4B;QAC5B,6BAA6B;QAC7B,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;QACvB,mBAAmB;QACnB,uBAAuB;QACvB,gCAAgC;QAChC,6BAA6B;QAC7B,6BAA6B;QAC7B,uBAAuB;QACvB,6BAA6B;QAC7B,6BAA6B;QAC7B,mBAAmB;QACnB,+BAA+B;QAC/B,+BAA+B;QAC/B,2BAA2B;QAC3B,wCAAwC;QACxC,iBAAiB;IACnB;IACA,eAAe;IACf,QAAQ;IACR,wBAAwB;IACxB,uBAAuB;QACrB,cAAc;YACZ,cAAc;QAChB;QACA,qBAAqB;YACnB,cAAc;QAChB;QACA,2BAA2B;YACzB,cAAc;QAChB;IACF;IACA,eAAe;QACb;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;gBACA;gBACA;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;gBACP;aACD;YACD,YAAY;gBACV,cAAc;YAChB;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;QACA;YACE,SAAS;YACT,YAAY;gBACV,aAAa;YACf;QACF;KACD;IACD,QAAQ;AACV", "ignoreList": [0], "debugId": null}}]}