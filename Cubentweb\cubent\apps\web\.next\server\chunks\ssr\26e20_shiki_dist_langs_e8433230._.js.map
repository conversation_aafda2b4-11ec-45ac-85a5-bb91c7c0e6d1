{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/javascript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JavaScript\", \"name\": \"javascript\", \"patterns\": [{ \"include\": \"#directives\" }, { \"include\": \"#statements\" }, { \"include\": \"#shebang\" }], \"repository\": { \"access-modifier\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(abstract|declare|override|public|protected|private|readonly|static)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.js\" }, \"after-operator-block-as-object-literal\": { \"begin\": \"(?<!\\\\+\\\\+|--)(?<=[:=(,\\\\[?+!>]|^await|[^\\\\._$0-9A-Za-z]await|^return|[^\\\\._$0-9A-Za-z]return|^yield|[^\\\\._$0-9A-Za-z]yield|^throw|[^\\\\._$0-9A-Za-z]throw|^in|[^\\\\._$0-9A-Za-z]in|^of|[^\\\\._$0-9A-Za-z]of|^typeof|[^\\\\._$0-9A-Za-z]typeof|&&|\\\\|\\\\||\\\\*)\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"name\": \"meta.objectliteral.js\", \"patterns\": [{ \"include\": \"#object-member\" }] }, \"array-binding-pattern\": { \"begin\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"patterns\": [{ \"include\": \"#binding-element\" }, { \"include\": \"#punctuation-comma\" }] }, \"array-binding-pattern-const\": { \"begin\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"patterns\": [{ \"include\": \"#binding-element-const\" }, { \"include\": \"#punctuation-comma\" }] }, \"array-literal\": { \"begin\": \"\\\\s*(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.js\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.js\" } }, \"name\": \"meta.array.literal.js\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"arrow-function\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.modifier.async.js\" }, \"2\": { \"name\": \"variable.parameter.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(\\\\basync)\\\\s+)?([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?==>)\", \"name\": \"meta.arrow.js\" }, { \"begin\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(\\\\basync))?((?<![})!\\\\]])\\\\s*(?=((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" } }, \"end\": \"(?==>|\\\\{|(^\\\\s*(export|function|class|interface|let|var|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|const|import|enum|namespace|module|type|abstract|declare)\\\\s+))\", \"name\": \"meta.arrow.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#function-parameters\" }, { \"include\": \"#arrow-return-type\" }, { \"include\": \"#possibly-arrow-return-type\" }] }, { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.function.arrow.js\" } }, \"end\": \"((?<=\\\\}|\\\\S)(?<!=>)|((?!\\\\{)(?=\\\\S)))(?!\\\\/[\\\\/\\\\*])\", \"name\": \"meta.arrow.js\", \"patterns\": [{ \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#expression\" }] }] }, \"arrow-return-type\": { \"begin\": \"(?<=\\\\))\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.js\" } }, \"end\": \"(?==>|\\\\{|(^\\\\s*(export|function|class|interface|let|var|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|const|import|enum|namespace|module|type|abstract|declare)\\\\s+))\", \"name\": \"meta.return.type.arrow.js\", \"patterns\": [{ \"include\": \"#arrow-return-type-body\" }] }, \"arrow-return-type-body\": { \"patterns\": [{ \"begin\": \"(?<=[:])(?=\\\\s*\\\\{)\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#type-object\" }] }, { \"include\": \"#type-predicate-operator\" }, { \"include\": \"#type\" }] }, \"async-modifier\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(async)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.async.js\" }, \"binding-element\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#regex\" }, { \"include\": \"#object-binding-pattern\" }, { \"include\": \"#array-binding-pattern\" }, { \"include\": \"#destructuring-variable-rest\" }, { \"include\": \"#variable-initializer\" }] }, \"binding-element-const\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#regex\" }, { \"include\": \"#object-binding-pattern-const\" }, { \"include\": \"#array-binding-pattern-const\" }, { \"include\": \"#destructuring-variable-rest-const\" }, { \"include\": \"#variable-initializer\" }] }, \"boolean-literal\": { \"patterns\": [{ \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))true(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.boolean.true.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))false(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.boolean.false.js\" }] }, \"brackets\": { \"patterns\": [{ \"begin\": \"{\", \"end\": \"}|(?=\\\\*/)\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]|(?=\\\\*/)\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"cast\": { \"patterns\": [{ \"include\": \"#jsx\" }] }, \"class-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(?:(abstract)\\\\s+)?\\\\b(class)\\\\b(?=\\\\s+|/[/*])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.js\" }, \"4\": { \"name\": \"storage.type.class.js\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.class.js\", \"patterns\": [{ \"include\": \"#class-declaration-or-expression-patterns\" }] }, \"class-declaration-or-expression-patterns\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#class-or-interface-heritage\" }, { \"captures\": { \"0\": { \"name\": \"entity.name.type.class.js\" } }, \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#class-or-interface-body\" }] }, \"class-expression\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(abstract)\\\\s+)?(class)\\\\b(?=\\\\s+|[<{]|\\\\/[\\\\/*])\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"storage.type.class.js\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.class.js\", \"patterns\": [{ \"include\": \"#class-declaration-or-expression-patterns\" }] }, \"class-or-interface-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#decorator\" }, { \"begin\": \"(?<=:)\\\\s*\", \"end\": \"(?=\\\\s|[;),}\\\\]:\\\\-+]|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"include\": \"#method-declaration\" }, { \"include\": \"#indexer-declaration\" }, { \"include\": \"#field-declaration\" }, { \"include\": \"#string\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#access-modifier\" }, { \"include\": \"#property-accessor\" }, { \"include\": \"#async-modifier\" }, { \"include\": \"#after-operator-block-as-object-literal\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"class-or-interface-heritage\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:\\\\b(extends|implements)\\\\b)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#class-or-interface-heritage\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#expressionWithoutIdentifiers\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.module.js\" }, \"2\": { \"name\": \"punctuation.accessor.js\" }, \"3\": { \"name\": \"punctuation.accessor.optional.js\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))(?=\\\\s*[_$A-Za-z][_$0-9A-Za-z]*(\\\\s*\\\\??\\\\.\\\\s*[_$A-Za-z][_$0-9A-Za-z]*)*\\\\s*)\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.inherited-class.js\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\" }, { \"include\": \"#expressionPunctuations\" }] }, \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"name\": \"comment.block.documentation.js\", \"patterns\": [{ \"include\": \"#docblock\" }] }, { \"begin\": \"(/\\\\*)(?:\\\\s*((@)internal)(?=\\\\s|(\\\\*/)))?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.js\" }, \"2\": { \"name\": \"storage.type.internaldeclaration.js\" }, \"3\": { \"name\": \"punctuation.decorator.internaldeclaration.js\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"name\": \"comment.block.js\" }, { \"begin\": \"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.js\" }, \"2\": { \"name\": \"comment.line.double-slash.js\" }, \"3\": { \"name\": \"punctuation.definition.comment.js\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.js\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.js\" } }, \"contentName\": \"comment.line.double-slash.js\", \"end\": \"(?=$)\" }] }, \"control-statement\": { \"patterns\": [{ \"include\": \"#switch-statement\" }, { \"include\": \"#for-loop\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(catch|finally|throw|try)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.trycatch.js\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.loop.js\" }, \"2\": { \"name\": \"entity.name.label.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(break|continue|goto)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(break|continue|do|goto|while)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.loop.js\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(return)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.flow.js\" } }, \"end\": \"(?=[;}]|$|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(case|default|switch)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.switch.js\" }, { \"include\": \"#if-statement\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(else|if)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.conditional.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(with)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.with.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(package)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(debugger)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.other.debugger.js\" }] }, \"decl-block\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"name\": \"meta.block.js\", \"patterns\": [{ \"include\": \"#statements\" }] }, \"declaration\": { \"patterns\": [{ \"include\": \"#decorator\" }, { \"include\": \"#var-expr\" }, { \"include\": \"#function-declaration\" }, { \"include\": \"#class-declaration\" }, { \"include\": \"#interface-declaration\" }, { \"include\": \"#enum-declaration\" }, { \"include\": \"#namespace-declaration\" }, { \"include\": \"#type-alias-declaration\" }, { \"include\": \"#import-equals-declaration\" }, { \"include\": \"#import-declaration\" }, { \"include\": \"#export-declaration\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(declare|export)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.js\" }] }, \"decorator\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))\\\\@\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.decorator.js\" } }, \"end\": \"(?=\\\\s)\", \"name\": \"meta.decorator.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"destructuring-const\": { \"patterns\": [{ \"begin\": \"(?<!=|:|^of|[^\\\\._$0-9A-Za-z]of|^in|[^\\\\._$0-9A-Za-z]in)\\\\s*(?=\\\\{)\", \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+))\", \"name\": \"meta.object-binding-pattern-variable.js\", \"patterns\": [{ \"include\": \"#object-binding-pattern-const\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#comment\" }] }, { \"begin\": \"(?<!=|:|^of|[^\\\\._$0-9A-Za-z]of|^in|[^\\\\._$0-9A-Za-z]in)\\\\s*(?=\\\\[)\", \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+))\", \"name\": \"meta.array-binding-pattern-variable.js\", \"patterns\": [{ \"include\": \"#array-binding-pattern-const\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#comment\" }] }] }, \"destructuring-parameter\": { \"patterns\": [{ \"begin\": \"(?<!=|:)\\\\s*(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"name\": \"meta.parameter.object-binding-pattern.js\", \"patterns\": [{ \"include\": \"#parameter-object-binding-element\" }] }, { \"begin\": \"(?<!=|:)\\\\s*(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"name\": \"meta.paramter.array-binding-pattern.js\", \"patterns\": [{ \"include\": \"#parameter-binding-element\" }, { \"include\": \"#punctuation-comma\" }] }] }, \"destructuring-parameter-rest\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"variable.parameter.js\" } }, \"match\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)\" }, \"destructuring-variable\": { \"patterns\": [{ \"begin\": \"(?<!=|:|^of|[^\\\\._$0-9A-Za-z]of|^in|[^\\\\._$0-9A-Za-z]in)\\\\s*(?=\\\\{)\", \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+))\", \"name\": \"meta.object-binding-pattern-variable.js\", \"patterns\": [{ \"include\": \"#object-binding-pattern\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#comment\" }] }, { \"begin\": \"(?<!=|:|^of|[^\\\\._$0-9A-Za-z]of|^in|[^\\\\._$0-9A-Za-z]in)\\\\s*(?=\\\\[)\", \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+))\", \"name\": \"meta.array-binding-pattern-variable.js\", \"patterns\": [{ \"include\": \"#array-binding-pattern\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#comment\" }] }] }, \"destructuring-variable-rest\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"meta.definition.variable.js variable.other.readwrite.js\" } }, \"match\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)\" }, \"destructuring-variable-rest-const\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"meta.definition.variable.js variable.other.constant.js\" } }, \"match\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)\" }, \"directives\": { \"begin\": \"^(///)\\\\s*(?=<(reference|amd-dependency|amd-module)(\\\\s+(path|types|no-default-lib|lib|name|resolution-mode)\\\\s*=\\\\s*((\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)))+\\\\s*/>\\\\s*$)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"(?=$)\", \"name\": \"comment.line.triple-slash.directive.js\", \"patterns\": [{ \"begin\": \"(<)(reference|amd-dependency|amd-module)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.directive.js\" }, \"2\": { \"name\": \"entity.name.tag.directive.js\" } }, \"end\": \"/>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.directive.js\" } }, \"name\": \"meta.tag.js\", \"patterns\": [{ \"match\": \"path|types|no-default-lib|lib|name|resolution-mode\", \"name\": \"entity.other.attribute-name.directive.js\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.js\" }, { \"include\": \"#string\" }] }] }, \"docblock\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"constant.language.access-type.jsdoc\" } }, \"match\": \"((@)(?:access|api))\\\\s+(private|protected|public)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"4\": { \"name\": \"punctuation.definition.bracket.angle.begin.jsdoc\" }, \"5\": { \"name\": \"constant.other.email.link.underline.jsdoc\" }, \"6\": { \"name\": \"punctuation.definition.bracket.angle.end.jsdoc\" } }, \"match\": \"((@)author)\\\\s+([^@\\\\s<>*/](?:[^@<>*/]|\\\\*[^/])*)(?:\\\\s*(<)([^>\\\\s]+)(>))?\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"4\": { \"name\": \"keyword.operator.control.jsdoc\" }, \"5\": { \"name\": \"entity.name.type.instance.jsdoc\" } }, \"match\": \"((@)borrows)\\\\s+((?:[^@\\\\s*/]|\\\\*[^/])+)\\\\s+(as)\\\\s+((?:[^@\\\\s*/]|\\\\*[^/])+)\" }, { \"begin\": \"((@)example)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=@|\\\\*/)\", \"name\": \"meta.example.jsdoc\", \"patterns\": [{ \"match\": \"^\\\\s\\\\*\\\\s+\" }, { \"begin\": \"\\\\G(<)caption(>)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.tag.inline.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.bracket.angle.begin.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.angle.end.jsdoc\" } }, \"contentName\": \"constant.other.description.jsdoc\", \"end\": \"(</)caption(>)|(?=\\\\*/)\", \"endCaptures\": { \"0\": { \"name\": \"entity.name.tag.inline.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.bracket.angle.begin.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.angle.end.jsdoc\" } } }, { \"captures\": { \"0\": { \"name\": \"source.embedded.js\" } }, \"match\": \"[^\\\\s@*](?:[^*]|\\\\*[^/])*\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"constant.language.symbol-type.jsdoc\" } }, \"match\": \"((@)kind)\\\\s+(class|constant|event|external|file|function|member|mixin|module|namespace|typedef)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.link.underline.jsdoc\" }, \"4\": { \"name\": \"entity.name.type.instance.jsdoc\" } }, \"match\": \"((@)see)\\\\s+(?:((?=https?://)(?:[^\\\\s*]|\\\\*[^/])+)|((?!https?://|(?:\\\\[[^\\\\[\\\\]]*\\\\])?{@(?:link|linkcode|linkplain|tutorial)\\\\b)(?:[^@\\\\s*/]|\\\\*[^/])+))\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" } }, \"match\": \"((@)template)\\\\s+([A-Za-z_$][\\\\w$.\\\\[\\\\]]*(?:\\\\s*,\\\\s*[A-Za-z_$][\\\\w$.\\\\[\\\\]]*)*)\" }, { \"begin\": \"((@)template)\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }, { \"match\": \"([A-Za-z_$][\\\\w$.\\\\[\\\\]]*)\", \"name\": \"variable.other.jsdoc\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" } }, \"match\": \"((@)(?:arg|argument|const|constant|member|namespace|param|var))\\\\s+([A-Za-z_$][\\\\w$.\\\\[\\\\]]*)\" }, { \"begin\": \"((@)typedef)\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }, { \"match\": \"(?:[^@\\\\s*/]|\\\\*[^/])+\", \"name\": \"entity.name.type.instance.jsdoc\" }] }, { \"begin\": \"((@)(?:arg|argument|const|constant|member|namespace|param|prop|property|var))\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }, { \"match\": \"([A-Za-z_$][\\\\w$.\\\\[\\\\]]*)\", \"name\": \"variable.other.jsdoc\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.optional-value.begin.bracket.square.jsdoc\" }, \"2\": { \"name\": \"keyword.operator.assignment.jsdoc\" }, \"3\": { \"name\": \"source.embedded.js\" }, \"4\": { \"name\": \"punctuation.definition.optional-value.end.bracket.square.jsdoc\" }, \"5\": { \"name\": \"invalid.illegal.syntax.jsdoc\" } }, \"match\": `(\\\\[)\\\\s*[\\\\w$]+(?:(?:\\\\[\\\\])?\\\\.[\\\\w$]+)*(?:\\\\s*(=)\\\\s*((?>\"(?:(?:\\\\*(?!/))|(?:\\\\\\\\(?!\"))|[^*\\\\\\\\])*?\"|'(?:(?:\\\\*(?!/))|(?:\\\\\\\\(?!'))|[^*\\\\\\\\])*?'|\\\\[(?:(?:\\\\*(?!/))|[^*])*?\\\\]|(?:(?:\\\\*(?!/))|\\\\s(?!\\\\s*\\\\])|\\\\[.*?(?:\\\\]|(?=\\\\*/))|[^*\\\\s\\\\[\\\\]])*)*))?\\\\s*(?:(\\\\])((?:[^*\\\\s]|\\\\*[^\\\\s/])+)?|(?=\\\\*/))`, \"name\": \"variable.other.jsdoc\" }] }, { \"begin\": \"((@)(?:define|enum|exception|export|extends|lends|implements|modifies|namespace|private|protected|returns?|satisfies|suppress|this|throws|type|yields?))\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"entity.name.type.instance.jsdoc\" } }, \"match\": \"((@)(?:alias|augments|callback|constructs|emits|event|fires|exports?|extends|external|function|func|host|lends|listens|interface|memberof!?|method|module|mixes|mixin|name|requires|see|this|typedef|uses))\\\\s+((?:[^{}@\\\\s*]|\\\\*[^/])+)\" }, { \"begin\": `((@)(?:default(?:value)?|license|version))\\\\s+(([''\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" }, \"4\": { \"name\": \"punctuation.definition.string.begin.jsdoc\" } }, \"contentName\": \"variable.other.jsdoc\", \"end\": \"(\\\\3)|(?=$|\\\\*/)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.string.end.jsdoc\" } } }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" } }, \"match\": \"((@)(?:default(?:value)?|license|tutorial|variation|version))\\\\s+([^\\\\s*]+)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"match\": \"(@)(?:abstract|access|alias|api|arg|argument|async|attribute|augments|author|beta|borrows|bubbles|callback|chainable|class|classdesc|code|config|const|constant|constructor|constructs|copyright|default|defaultvalue|define|deprecated|desc|description|dict|emits|enum|event|example|exception|exports?|extends|extension(?:_?for)?|external|externs|file|fileoverview|final|fires|for|func|function|generator|global|hideconstructor|host|ignore|implements|implicitCast|inherit[Dd]oc|inner|instance|interface|internal|kind|lends|license|listens|main|member|memberof!?|method|mixes|mixins?|modifies|module|name|namespace|noalias|nocollapse|nocompile|nosideeffects|override|overview|package|param|polymer(?:Behavior)?|preserve|private|prop|property|protected|public|read[Oo]nly|record|require[ds]|returns?|see|since|static|struct|submodule|summary|suppress|template|this|throws|todo|tutorial|type|typedef|unrestricted|uses|var|variation|version|virtual|writeOnce|yields?)\\\\b\", \"name\": \"storage.type.class.jsdoc\" }, { \"include\": \"#inline-tags\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"match\": \"((@)(?:[_$A-Za-z][_$0-9A-Za-z]*))(?=\\\\s+)\" }] }, \"enum-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?(?:\\\\b(const)\\\\s+)?\\\\b(enum)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.js\" }, \"4\": { \"name\": \"storage.type.enum.js\" }, \"5\": { \"name\": \"entity.name.type.enum.js\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.enum.declaration.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.enummember.js\" } }, \"end\": \"(?=,|\\\\}|$)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, { \"begin\": \"(?=((\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\])))\", \"end\": \"(?=,|\\\\}|$)\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, { \"include\": \"#punctuation-comma\" }] }] }, \"export-declaration\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"keyword.control.as.js\" }, \"3\": { \"name\": \"storage.type.namespace.js\" }, \"4\": { \"name\": \"entity.name.type.module.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(export)\\\\s+(as)\\\\s+(namespace)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(export)(?:\\\\s+(type))?(?:(?:\\\\s*(=))|(?:\\\\s+(default)(?=\\\\s+)))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"keyword.control.type.js\" }, \"3\": { \"name\": \"keyword.operator.assignment.js\" }, \"4\": { \"name\": \"keyword.control.default.js\" } }, \"end\": \"(?=$|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"name\": \"meta.export.default.js\", \"patterns\": [{ \"include\": \"#interface-declaration\" }, { \"include\": \"#expression\" }] }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(export)(?:\\\\s+(type))?\\\\b(?!(\\\\$)|(\\\\s*:))((?=\\\\s*[{*])|((?=\\\\s*[_$A-Za-z][_$0-9A-Za-z]*(\\\\s|,))(?!\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b)))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"keyword.control.type.js\" } }, \"end\": \"(?=$|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"name\": \"meta.export.js\", \"patterns\": [{ \"include\": \"#import-export-declaration\" }] }] }, \"expression\": { \"patterns\": [{ \"include\": \"#expressionWithoutIdentifiers\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#expressionPunctuations\" }] }, \"expression-inside-possibly-arrow-parens\": { \"patterns\": [{ \"include\": \"#expressionWithoutIdentifiers\" }, { \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#decorator\" }, { \"include\": \"#destructuring-parameter\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(override|public|protected|private|readonly)\\\\s+(?=(override|public|protected|private|readonly)\\\\s+)\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.operator.rest.js\" }, \"3\": { \"name\": \"entity.name.function.js variable.language.this.js\" }, \"4\": { \"name\": \"entity.name.function.js\" }, \"5\": { \"name\": \"keyword.operator.optional.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(override|public|private|protected|readonly)\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*(\\\\??)(?=\\\\s*(=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))|(:\\\\s*((<)|([(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>)))))))|(:\\\\s*(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Function(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(:\\\\s*((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))))|(:\\\\s*(=>|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.operator.rest.js\" }, \"3\": { \"name\": \"variable.parameter.js variable.language.this.js\" }, \"4\": { \"name\": \"variable.parameter.js\" }, \"5\": { \"name\": \"keyword.operator.optional.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(override|public|private|protected|readonly)\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*(\\\\??)(?=\\\\s*[:,]|$)\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#variable-initializer\" }, { \"match\": \",\", \"name\": \"punctuation.separator.parameter.js\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#expressionPunctuations\" }] }, \"expression-operators\": { \"patterns\": [{ \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(await)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.flow.js\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(yield)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))(?=\\\\s*\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*\\\\*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.js\" } }, \"end\": \"\\\\*\", \"endCaptures\": { \"0\": { \"name\": \"keyword.generator.asterisk.js\" } }, \"patterns\": [{ \"include\": \"#comment\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.flow.js\" }, \"2\": { \"name\": \"keyword.generator.asterisk.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(yield)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))(?:\\\\s*(\\\\*))?\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))delete(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.expression.delete.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))in(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))(?!\\\\()\", \"name\": \"keyword.operator.expression.in.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))of(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))(?!\\\\()\", \"name\": \"keyword.operator.expression.of.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))instanceof(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.expression.instanceof.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))new(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.new.js\" }, { \"include\": \"#typeof-operator\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))void(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.expression.void.js\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.as.js\" }, \"2\": { \"name\": \"storage.modifier.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as)\\\\s+(const)(?=\\\\s*($|[;,:})\\\\]]))\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(as)|(satisfies))\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.as.js\" }, \"2\": { \"name\": \"keyword.control.satisfies.js\" } }, \"end\": \"(?=^|[;),}\\\\]:?\\\\-+>]|\\\\|\\\\||\\\\&\\\\&|!==|$|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as|satisfies)\\\\s+)|(\\\\s+<))\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.spread.js\" }, { \"match\": \"\\\\*=|(?<!\\\\()/=|%=|\\\\+=|-=\", \"name\": \"keyword.operator.assignment.compound.js\" }, { \"match\": \"\\\\&=|\\\\^=|<<=|>>=|>>>=|\\\\|=\", \"name\": \"keyword.operator.assignment.compound.bitwise.js\" }, { \"match\": \"<<|>>>|>>\", \"name\": \"keyword.operator.bitwise.shift.js\" }, { \"match\": \"===|!==|==|!=\", \"name\": \"keyword.operator.comparison.js\" }, { \"match\": \"<=|>=|<>|<|>\", \"name\": \"keyword.operator.relational.js\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.js\" }, \"2\": { \"name\": \"keyword.operator.assignment.compound.js\" }, \"3\": { \"name\": \"keyword.operator.arithmetic.js\" } }, \"match\": \"(?<=[_$0-9A-Za-z])(!)\\\\s*(?:(/=)|(?:(/)(?![/*])))\" }, { \"match\": \"!|&&|\\\\|\\\\||\\\\?\\\\?\", \"name\": \"keyword.operator.logical.js\" }, { \"match\": \"\\\\&|~|\\\\^|\\\\|\", \"name\": \"keyword.operator.bitwise.js\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.js\" }, { \"match\": \"--\", \"name\": \"keyword.operator.decrement.js\" }, { \"match\": \"\\\\+\\\\+\", \"name\": \"keyword.operator.increment.js\" }, { \"match\": \"%|\\\\*|/|-|\\\\+\", \"name\": \"keyword.operator.arithmetic.js\" }, { \"begin\": \"(?<=[_$0-9A-Za-z)\\\\]])\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)+(?:(/=)|(?:(/)(?![/*]))))\", \"end\": \"(?:(/=)|(?:(/)(?!\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/)))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.compound.js\" }, \"2\": { \"name\": \"keyword.operator.arithmetic.js\" } }, \"patterns\": [{ \"include\": \"#comment\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.assignment.compound.js\" }, \"2\": { \"name\": \"keyword.operator.arithmetic.js\" } }, \"match\": \"(?<=[_$0-9A-Za-z)\\\\]])\\\\s*(?:(/=)|(?:(/)(?![/*])))\" }] }, \"expressionPunctuations\": { \"patterns\": [{ \"include\": \"#punctuation-comma\" }, { \"include\": \"#punctuation-accessor\" }] }, \"expressionWithoutIdentifiers\": { \"patterns\": [{ \"include\": \"#jsx\" }, { \"include\": \"#string\" }, { \"include\": \"#regex\" }, { \"include\": \"#comment\" }, { \"include\": \"#function-expression\" }, { \"include\": \"#class-expression\" }, { \"include\": \"#arrow-function\" }, { \"include\": \"#paren-expression-possibly-arrow\" }, { \"include\": \"#cast\" }, { \"include\": \"#ternary-expression\" }, { \"include\": \"#new-expr\" }, { \"include\": \"#instanceof-expr\" }, { \"include\": \"#object-literal\" }, { \"include\": \"#expression-operators\" }, { \"include\": \"#function-call\" }, { \"include\": \"#literal\" }, { \"include\": \"#support-objects\" }, { \"include\": \"#paren-expression\" }] }, \"field-declaration\": { \"begin\": \"(?<!\\\\()(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(readonly)\\\\s+)?(?=\\\\s*((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(?:(?:(\\\\?)|(!))\\\\s*)?(=|:|;|,|\\\\}|$))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" } }, \"end\": \"(?=\\\\}|;|,|$|(^(?!\\\\s*((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(?:(?:(\\\\?)|(!))\\\\s*)?(=|:|;|,|$))))|(?<=\\\\})\", \"name\": \"meta.field.declaration.js\", \"patterns\": [{ \"include\": \"#variable-initializer\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#string\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"meta.definition.property.js entity.name.function.js\" }, \"2\": { \"name\": \"keyword.operator.optional.js\" }, \"3\": { \"name\": \"keyword.operator.definiteassignment.js\" } }, \"match\": \"(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)(?:(\\\\?)|(!))?(?=\\\\s*\\\\s*(=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))|(:\\\\s*((<)|([(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>)))))))|(:\\\\s*(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Function(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(:\\\\s*((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))))|(:\\\\s*(=>|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\" }, { \"match\": \"\\\\#?[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"meta.definition.property.js variable.object.property.js\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.optional.js\" }, { \"match\": \"!\", \"name\": \"keyword.operator.definiteassignment.js\" }] }, \"for-loop\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))for(?=((\\\\s+|(\\\\s*\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*))await)?\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)?(\\\\())\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.loop.js\" } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"await\", \"name\": \"keyword.control.loop.js\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#var-expr\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-semicolon\" }] }] }, \"function-body\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#function-parameters\" }, { \"include\": \"#return-type\" }, { \"include\": \"#type-function-return-type\" }, { \"include\": \"#decl-block\" }, { \"match\": \"\\\\*\", \"name\": \"keyword.generator.asterisk.js\" }] }, \"function-call\": { \"patterns\": [{ \"begin\": \"(?=(((([_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*\\\\??\\\\.\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))\\\\s*(?:(\\\\?\\\\.\\\\s*)|(!))?((<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))(([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\\\s*)?\\\\())\", \"end\": \"(?<=\\\\))(?!(((([_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*\\\\??\\\\.\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))\\\\s*(?:(\\\\?\\\\.\\\\s*)|(!))?((<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))(([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\\\s*)?\\\\())\", \"patterns\": [{ \"begin\": \"(?=(([_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*\\\\??\\\\.\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))\", \"end\": \"(?=\\\\s*(?:(\\\\?\\\\.\\\\s*)|(!))?((<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))(([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\\\s*)?\\\\())\", \"name\": \"meta.function-call.js\", \"patterns\": [{ \"include\": \"#function-call-target\" }] }, { \"include\": \"#comment\" }, { \"include\": \"#function-call-optionals\" }, { \"include\": \"#type-arguments\" }, { \"include\": \"#paren-expression\" }] }, { \"begin\": \"(?=(((([_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*\\\\??\\\\.\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))(<\\\\s*[{\\\\[(]\\\\s*$))\", \"end\": \"(?<=>)(?!(((([_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*\\\\??\\\\.\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))|(?<=[)]))(<\\\\s*[{\\\\[(]\\\\s*$))\", \"patterns\": [{ \"begin\": \"(?=(([_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*\\\\??\\\\.\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))*)|(\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))\", \"end\": \"(?=(<\\\\s*[{\\\\[(]\\\\s*$))\", \"name\": \"meta.function-call.js\", \"patterns\": [{ \"include\": \"#function-call-target\" }] }, { \"include\": \"#comment\" }, { \"include\": \"#function-call-optionals\" }, { \"include\": \"#type-arguments\" }] }] }, \"function-call-optionals\": { \"patterns\": [{ \"match\": \"\\\\?\\\\.\", \"name\": \"meta.function-call.js punctuation.accessor.optional.js\" }, { \"match\": \"!\", \"name\": \"meta.function-call.js keyword.operator.definiteassignment.js\" }] }, \"function-call-target\": { \"patterns\": [{ \"include\": \"#support-function-call-identifiers\" }, { \"match\": \"(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"entity.name.function.js\" }] }, \"function-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?(?:(async)\\\\s+)?(function\\\\b)(?:\\\\s*(\\\\*))?(?:(?:\\\\s+|(?<=\\\\*))([_$A-Za-z][_$0-9A-Za-z]*))?\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.async.js\" }, \"4\": { \"name\": \"storage.type.function.js\" }, \"5\": { \"name\": \"keyword.generator.asterisk.js\" }, \"6\": { \"name\": \"meta.definition.function.js entity.name.function.js\" } }, \"end\": \"(?=;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))|(?<=\\\\})\", \"name\": \"meta.function.js\", \"patterns\": [{ \"include\": \"#function-name\" }, { \"include\": \"#function-body\" }] }, \"function-expression\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(async)\\\\s+)?(function\\\\b)(?:\\\\s*(\\\\*))?(?:(?:\\\\s+|(?<=\\\\*))([_$A-Za-z][_$0-9A-Za-z]*))?\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" }, \"2\": { \"name\": \"storage.type.function.js\" }, \"3\": { \"name\": \"keyword.generator.asterisk.js\" }, \"4\": { \"name\": \"meta.definition.function.js entity.name.function.js\" } }, \"end\": \"(?=;)|(?<=\\\\})\", \"name\": \"meta.function.expression.js\", \"patterns\": [{ \"include\": \"#function-name\" }, { \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#function-body\" }] }, \"function-name\": { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"meta.definition.function.js entity.name.function.js\" }, \"function-parameters\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.js\" } }, \"name\": \"meta.parameters.js\", \"patterns\": [{ \"include\": \"#function-parameters-body\" }] }, \"function-parameters-body\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#decorator\" }, { \"include\": \"#destructuring-parameter\" }, { \"include\": \"#parameter-name\" }, { \"include\": \"#parameter-type-annotation\" }, { \"include\": \"#variable-initializer\" }, { \"match\": \",\", \"name\": \"punctuation.separator.parameter.js\" }] }, \"identifiers\": { \"patterns\": [{ \"include\": \"#object-identifiers\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.js\" }, \"2\": { \"name\": \"punctuation.accessor.optional.js\" }, \"3\": { \"name\": \"entity.name.function.js\" } }, \"match\": \"(?:(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)(?=\\\\s*=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.js\" }, \"2\": { \"name\": \"punctuation.accessor.optional.js\" }, \"3\": { \"name\": \"variable.other.constant.property.js\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(\\\\#?[A-Z][_$\\\\dA-Z]*)(?![_$0-9A-Za-z])\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.js\" }, \"2\": { \"name\": \"punctuation.accessor.optional.js\" }, \"3\": { \"name\": \"variable.other.property.js\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)\" }, { \"match\": \"([A-Z][_$\\\\dA-Z]*)(?![_$0-9A-Za-z])\", \"name\": \"variable.other.constant.js\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"variable.other.readwrite.js\" }] }, \"if-statement\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?=\\\\bif\\\\s*(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))\\\\s*(?!\\\\{))\", \"end\": \"(?=;|$|\\\\})\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(if)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.js\" }, \"2\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(?<=\\\\))\\\\s*\\\\/(?![\\\\/*])(?=(?:[^\\\\/\\\\\\\\\\\\[]|\\\\\\\\.|\\\\[([^\\\\]\\\\\\\\]|\\\\\\\\.)*\\\\])+\\\\/([dgimsuy]+|(?![\\\\/\\\\*])|(?=\\\\/\\\\*))(?!\\\\s*[a-zA-Z0-9_$]))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": \"(/)([dgimsuy]*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.js\" }, \"2\": { \"name\": \"keyword.other.js\" } }, \"name\": \"string.regexp.js\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"include\": \"#statements\" }] }] }, \"import-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(import)(?:\\\\s+(type)(?!\\\\s+from))?(?!\\\\s*[:(])(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"keyword.control.import.js\" }, \"4\": { \"name\": \"keyword.control.type.js\" } }, \"end\": \"(?<!^import|[^\\\\._$0-9A-Za-z]import)(?=;|$|^)\", \"name\": \"meta.import.js\", \"patterns\": [{ \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"begin\": `(?<=^import|[^\\\\._$0-9A-Za-z]import)(?!\\\\s*[\"'])`, \"end\": \"\\\\bfrom\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.from.js\" } }, \"patterns\": [{ \"include\": \"#import-export-declaration\" }] }, { \"include\": \"#import-export-declaration\" }] }, \"import-equals-declaration\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(import)(?:\\\\s+(type))?\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(=)\\\\s*(require)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"keyword.control.import.js\" }, \"4\": { \"name\": \"keyword.control.type.js\" }, \"5\": { \"name\": \"variable.other.readwrite.alias.js\" }, \"6\": { \"name\": \"keyword.operator.assignment.js\" }, \"7\": { \"name\": \"keyword.control.require.js\" }, \"8\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"name\": \"meta.import-equals.external.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(import)(?:\\\\s+(type))?\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(=)\\\\s*(?!require\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"keyword.control.import.js\" }, \"4\": { \"name\": \"keyword.control.type.js\" }, \"5\": { \"name\": \"variable.other.readwrite.alias.js\" }, \"6\": { \"name\": \"keyword.operator.assignment.js\" } }, \"end\": \"(?=;|$|^)\", \"name\": \"meta.import-equals.internal.js\", \"patterns\": [{ \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.module.js\" }, \"2\": { \"name\": \"punctuation.accessor.js\" }, \"3\": { \"name\": \"punctuation.accessor.optional.js\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\" }, { \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"variable.other.readwrite.js\" }] }] }, \"import-export-assert-clause\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(with)|(assert))\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.with.js\" }, \"2\": { \"name\": \"keyword.control.assert.js\" }, \"3\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"match\": \"(?:[_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:)\", \"name\": \"meta.object-literal.key.js\" }, { \"match\": \":\", \"name\": \"punctuation.separator.key-value.js\" }] }, \"import-export-block\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"name\": \"meta.block.js\", \"patterns\": [{ \"include\": \"#import-export-clause\" }] }, \"import-export-clause\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.type.js\" }, \"2\": { \"name\": \"keyword.control.default.js\" }, \"3\": { \"name\": \"constant.language.import-export-all.js\" }, \"4\": { \"name\": \"variable.other.readwrite.js\" }, \"5\": { \"name\": \"keyword.control.as.js\" }, \"6\": { \"name\": \"keyword.control.default.js\" }, \"7\": { \"name\": \"variable.other.readwrite.alias.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(?:(\\\\btype)\\\\s+)?(?:(\\\\bdefault)|(\\\\*)|(\\\\b[_$A-Za-z][_$0-9A-Za-z]*)))\\\\s+(as)\\\\s+(?:(default(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|([_$A-Za-z][_$0-9A-Za-z]*))\" }, { \"include\": \"#punctuation-comma\" }, { \"match\": \"\\\\*\", \"name\": \"constant.language.import-export-all.js\" }, { \"match\": \"\\\\b(default)\\\\b\", \"name\": \"keyword.control.default.js\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.type.js\" }, \"2\": { \"name\": \"variable.other.readwrite.alias.js\" } }, \"match\": \"(?:(\\\\btype)\\\\s+)?([_$A-Za-z][_$0-9A-Za-z]*)\" }] }, \"import-export-declaration\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#import-export-block\" }, { \"match\": \"\\\\bfrom\\\\b\", \"name\": \"keyword.control.from.js\" }, { \"include\": \"#import-export-assert-clause\" }, { \"include\": \"#import-export-clause\" }] }, \"indexer-declaration\": { \"begin\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(readonly)\\\\s*)?\\\\s*(\\\\[)\\\\s*([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=:)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"meta.brace.square.js\" }, \"3\": { \"name\": \"variable.parameter.js\" } }, \"end\": \"(\\\\])\\\\s*(\\\\?\\\\s*)?|$\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.square.js\" }, \"2\": { \"name\": \"keyword.operator.optional.js\" } }, \"name\": \"meta.indexer.declaration.js\", \"patterns\": [{ \"include\": \"#type-annotation\" }] }, \"indexer-mapped-type-declaration\": { \"begin\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))([+-])?(readonly)\\\\s*)?\\\\s*(\\\\[)\\\\s*([_$A-Za-z][_$0-9A-Za-z]*)\\\\s+(in)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.modifier.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"meta.brace.square.js\" }, \"4\": { \"name\": \"entity.name.type.js\" }, \"5\": { \"name\": \"keyword.operator.expression.in.js\" } }, \"end\": \"(\\\\])([+-])?\\\\s*(\\\\?\\\\s*)?|$\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.square.js\" }, \"2\": { \"name\": \"keyword.operator.type.modifier.js\" }, \"3\": { \"name\": \"keyword.operator.optional.js\" } }, \"name\": \"meta.indexer.mappedtype.declaration.js\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.as.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as)\\\\s+\" }, { \"include\": \"#type\" }] }, \"inline-tags\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.bracket.square.begin.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.square.end.jsdoc\" } }, \"match\": \"(\\\\[)[^\\\\]]+(\\\\])(?={@(?:link|linkcode|linkplain|tutorial))\", \"name\": \"constant.other.description.jsdoc\" }, { \"begin\": \"({)((@)(?:link(?:code|plain)?|tutorial))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.bracket.curly.begin.jsdoc\" }, \"2\": { \"name\": \"storage.type.class.jsdoc\" }, \"3\": { \"name\": \"punctuation.definition.inline.tag.jsdoc\" } }, \"end\": \"}|(?=\\\\*/)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.curly.end.jsdoc\" } }, \"name\": \"entity.name.type.instance.jsdoc\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.link.underline.jsdoc\" }, \"2\": { \"name\": \"punctuation.separator.pipe.jsdoc\" } }, \"match\": \"\\\\G((?=https?://)(?:[^|}\\\\s*]|\\\\*[/])+)(\\\\|)?\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.description.jsdoc\" }, \"2\": { \"name\": \"punctuation.separator.pipe.jsdoc\" } }, \"match\": \"\\\\G((?:[^{}@\\\\s|*]|\\\\*[^/])+)(\\\\|)?\" }] }] }, \"instanceof-expr\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(instanceof)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.expression.instanceof.js\" } }, \"end\": \"(?<=\\\\))|(?=[;),}\\\\]:?\\\\-+>]|\\\\|\\\\||\\\\&\\\\&|!==|$|(===|!==|==|!=)|(([\\\\&\\\\~\\\\^\\\\|]\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s+instanceof(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))function((\\\\s+[_$A-Za-z][_$0-9A-Za-z]*)|(\\\\s*[(]))))\", \"patterns\": [{ \"include\": \"#type\" }] }, \"interface-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(?:(abstract)\\\\s+)?\\\\b(interface)\\\\b(?=\\\\s+|/[/*])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.js\" }, \"4\": { \"name\": \"storage.type.interface.js\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.interface.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#class-or-interface-heritage\" }, { \"captures\": { \"0\": { \"name\": \"entity.name.type.interface.js\" } }, \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#class-or-interface-body\" }] }, \"jsdoctype\": { \"patterns\": [{ \"begin\": \"\\\\G({)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.bracket.curly.begin.jsdoc\" } }, \"contentName\": \"entity.name.type.instance.jsdoc\", \"end\": \"((}))\\\\s*|(?=\\\\*/)\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.curly.end.jsdoc\" } }, \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"jsx\": { \"patterns\": [{ \"include\": \"#jsx-tag-without-attributes-in-expression\" }, { \"include\": \"#jsx-tag-in-expression\" }] }, \"jsx-children\": { \"patterns\": [{ \"include\": \"#jsx-tag-without-attributes\" }, { \"include\": \"#jsx-tag\" }, { \"include\": \"#jsx-evaluated-code\" }, { \"include\": \"#jsx-entities\" }] }, \"jsx-entities\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.js\" }, \"3\": { \"name\": \"punctuation.definition.entity.js\" } }, \"match\": \"(&)([a-zA-Z0-9]+|#\\\\d+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.js\" }] }, \"jsx-evaluated-code\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.js\" } }, \"contentName\": \"meta.embedded.expression.js\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.js\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"jsx-string-double-quoted\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.js\" } }, \"name\": \"string.quoted.double.js\", \"patterns\": [{ \"include\": \"#jsx-entities\" }] }, \"jsx-string-single-quoted\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.js\" } }, \"name\": \"string.quoted.single.js\", \"patterns\": [{ \"include\": \"#jsx-entities\" }] }, \"jsx-tag\": { \"begin\": \"(?=(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))(?=((<\\\\s*)|(\\\\s+))(?!\\\\?)|\\\\/?>))\", \"end\": \"(/>)|(?:(</)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))?\\\\s*(>))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.js\" }, \"2\": { \"name\": \"punctuation.definition.tag.begin.js\" }, \"3\": { \"name\": \"entity.name.tag.namespace.js\" }, \"4\": { \"name\": \"punctuation.separator.namespace.js\" }, \"5\": { \"name\": \"entity.name.tag.js\" }, \"6\": { \"name\": \"support.class.component.js\" }, \"7\": { \"name\": \"punctuation.definition.tag.end.js\" } }, \"name\": \"meta.tag.js\", \"patterns\": [{ \"begin\": \"(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))(?=((<\\\\s*)|(\\\\s+))(?!\\\\?)|\\\\/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.js\" }, \"2\": { \"name\": \"entity.name.tag.namespace.js\" }, \"3\": { \"name\": \"punctuation.separator.namespace.js\" }, \"4\": { \"name\": \"entity.name.tag.js\" }, \"5\": { \"name\": \"support.class.component.js\" } }, \"end\": \"(?=[/]?>)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-arguments\" }, { \"include\": \"#jsx-tag-attributes\" }] }, { \"begin\": \"(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.js\" } }, \"contentName\": \"meta.jsx.children.js\", \"end\": \"(?=</)\", \"patterns\": [{ \"include\": \"#jsx-children\" }] }] }, \"jsx-tag-attribute-assignment\": { \"match\": `=(?=\\\\s*(?:'|\"|{|/\\\\*|//|\\\\n))`, \"name\": \"keyword.operator.assignment.js\" }, \"jsx-tag-attribute-name\": { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.js\" }, \"2\": { \"name\": \"punctuation.separator.namespace.js\" }, \"3\": { \"name\": \"entity.other.attribute-name.js\" } }, \"match\": \"\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(:))?([_$A-Za-z][-_$0-9A-Za-z]*)(?=\\\\s|=|/?>|/\\\\*|//)\" }, \"jsx-tag-attributes\": { \"begin\": \"\\\\s+\", \"end\": \"(?=[/]?>)\", \"name\": \"meta.tag.attributes.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#jsx-tag-attribute-name\" }, { \"include\": \"#jsx-tag-attribute-assignment\" }, { \"include\": \"#jsx-string-double-quoted\" }, { \"include\": \"#jsx-string-single-quoted\" }, { \"include\": \"#jsx-evaluated-code\" }, { \"include\": \"#jsx-tag-attributes-illegal\" }] }, \"jsx-tag-attributes-illegal\": { \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.attribute.js\" }, \"jsx-tag-in-expression\": { \"begin\": \"(?<!\\\\+\\\\+|--)(?<=[({\\\\[,?=>:*]|&&|\\\\|\\\\||\\\\?|\\\\*\\\\/|^await|[^\\\\._$0-9A-Za-z]await|^return|[^\\\\._$0-9A-Za-z]return|^default|[^\\\\._$0-9A-Za-z]default|^yield|[^\\\\._$0-9A-Za-z]yield|^)\\\\s*(?!<\\\\s*[_$A-Za-z][_$0-9A-Za-z]*((\\\\s+extends\\\\s+[^=>])|,))(?=(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))(?=((<\\\\s*)|(\\\\s+))(?!\\\\?)|\\\\/?>))\", \"end\": \"(?!(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))(?=((<\\\\s*)|(\\\\s+))(?!\\\\?)|\\\\/?>))\", \"patterns\": [{ \"include\": \"#jsx-tag\" }] }, \"jsx-tag-without-attributes\": { \"begin\": \"(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))?\\\\s*(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.js\" }, \"2\": { \"name\": \"entity.name.tag.namespace.js\" }, \"3\": { \"name\": \"punctuation.separator.namespace.js\" }, \"4\": { \"name\": \"entity.name.tag.js\" }, \"5\": { \"name\": \"support.class.component.js\" }, \"6\": { \"name\": \"punctuation.definition.tag.end.js\" } }, \"contentName\": \"meta.jsx.children.js\", \"end\": \"(</)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))?\\\\s*(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.js\" }, \"2\": { \"name\": \"entity.name.tag.namespace.js\" }, \"3\": { \"name\": \"punctuation.separator.namespace.js\" }, \"4\": { \"name\": \"entity.name.tag.js\" }, \"5\": { \"name\": \"support.class.component.js\" }, \"6\": { \"name\": \"punctuation.definition.tag.end.js\" } }, \"name\": \"meta.tag.without-attributes.js\", \"patterns\": [{ \"include\": \"#jsx-children\" }] }, \"jsx-tag-without-attributes-in-expression\": { \"begin\": \"(?<!\\\\+\\\\+|--)(?<=[({\\\\[,?=>:*]|&&|\\\\|\\\\||\\\\?|\\\\*\\\\/|^await|[^\\\\._$0-9A-Za-z]await|^return|[^\\\\._$0-9A-Za-z]return|^default|[^\\\\._$0-9A-Za-z]default|^yield|[^\\\\._$0-9A-Za-z]yield|^)\\\\s*(?=(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))?\\\\s*(>))\", \"end\": \"(?!(<)\\\\s*(?:([_$A-Za-z][-_$0-9A-Za-z.]*)(?<!\\\\.|-)(:))?((?:[a-z][a-z0-9]*|([_$A-Za-z][-_$0-9A-Za-z.]*))(?<!\\\\.|-))?\\\\s*(>))\", \"patterns\": [{ \"include\": \"#jsx-tag-without-attributes\" }] }, \"label\": { \"patterns\": [{ \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(:)(?=\\\\s*\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.label.js\" }, \"2\": { \"name\": \"punctuation.separator.label.js\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#decl-block\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.name.label.js\" }, \"2\": { \"name\": \"punctuation.separator.label.js\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(:)\" }] }, \"literal\": { \"patterns\": [{ \"include\": \"#numeric-literal\" }, { \"include\": \"#boolean-literal\" }, { \"include\": \"#null-literal\" }, { \"include\": \"#undefined-literal\" }, { \"include\": \"#numericConstant-literal\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#this-literal\" }, { \"include\": \"#super-literal\" }] }, \"method-declaration\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:\\\\b(override)\\\\s+)?(?:\\\\b(public|private|protected)\\\\s+)?(?:\\\\b(abstract)\\\\s+)?(?:\\\\b(async)\\\\s+)?\\\\s*\\\\b(constructor)\\\\b(?!:)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.js\" }, \"4\": { \"name\": \"storage.modifier.async.js\" }, \"5\": { \"name\": \"storage.type.js\" } }, \"end\": \"(?=\\\\}|;|,|$)|(?<=\\\\})\", \"name\": \"meta.method.declaration.js\", \"patterns\": [{ \"include\": \"#method-declaration-name\" }, { \"include\": \"#function-body\" }] }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:\\\\b(override)\\\\s+)?(?:\\\\b(public|private|protected)\\\\s+)?(?:\\\\b(abstract)\\\\s+)?(?:\\\\b(async)\\\\s+)?(?:(?:\\\\s*\\\\b(new)\\\\b(?!:)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(?:(\\\\*)\\\\s*)?)(?=\\\\s*((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*))?[(])\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.js\" }, \"4\": { \"name\": \"storage.modifier.async.js\" }, \"5\": { \"name\": \"keyword.operator.new.js\" }, \"6\": { \"name\": \"keyword.generator.asterisk.js\" } }, \"end\": \"(?=\\\\}|;|,|$)|(?<=\\\\})\", \"name\": \"meta.method.declaration.js\", \"patterns\": [{ \"include\": \"#method-declaration-name\" }, { \"include\": \"#function-body\" }] }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:\\\\b(override)\\\\s+)?(?:\\\\b(public|private|protected)\\\\s+)?(?:\\\\b(abstract)\\\\s+)?(?:\\\\b(async)\\\\s+)?(?:\\\\b(get|set)\\\\s+)?(?:(\\\\*)\\\\s*)?(?=\\\\s*(((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(\\\\??))\\\\s*((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*))?[(])\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.modifier.js\" }, \"4\": { \"name\": \"storage.modifier.async.js\" }, \"5\": { \"name\": \"storage.type.property.js\" }, \"6\": { \"name\": \"keyword.generator.asterisk.js\" } }, \"end\": \"(?=\\\\}|;|,|$)|(?<=\\\\})\", \"name\": \"meta.method.declaration.js\", \"patterns\": [{ \"include\": \"#method-declaration-name\" }, { \"include\": \"#function-body\" }] }] }, \"method-declaration-name\": { \"begin\": \"(?=((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(\\\\??)\\\\s*[(<])\", \"end\": \"(?=\\\\(|<)\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#numeric-literal\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"meta.definition.method.js entity.name.function.js\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.optional.js\" }] }, \"namespace-declaration\": { \"begin\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(namespace|module)\\\\s+(?=[_$A-Za-z\\\"'`]))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.namespace.js\" } }, \"end\": \"(?<=\\\\})|(?=;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"name\": \"meta.namespace.declaration.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"entity.name.type.module.js\" }, { \"include\": \"#punctuation-accessor\" }, { \"include\": \"#decl-block\" }] }, \"new-expr\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(new)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.new.js\" } }, \"end\": \"(?<=\\\\))|(?=[;),}\\\\]:?\\\\-+>]|\\\\|\\\\||\\\\&\\\\&|!==|$|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))new(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))function((\\\\s+[_$A-Za-z][_$0-9A-Za-z]*)|(\\\\s*[(]))))\", \"name\": \"new.expr.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"null-literal\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))null(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.null.js\" }, \"numeric-literal\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.numeric.bigint.js\" } }, \"match\": \"\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.js\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.numeric.bigint.js\" } }, \"match\": \"\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.binary.js\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.numeric.bigint.js\" } }, \"match\": \"\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.octal.js\" }, { \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.js\" }, \"1\": { \"name\": \"meta.delimiter.decimal.period.js\" }, \"2\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"3\": { \"name\": \"meta.delimiter.decimal.period.js\" }, \"4\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"5\": { \"name\": \"meta.delimiter.decimal.period.js\" }, \"6\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"7\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"8\": { \"name\": \"meta.delimiter.decimal.period.js\" }, \"9\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"10\": { \"name\": \"meta.delimiter.decimal.period.js\" }, \"11\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"12\": { \"name\": \"meta.delimiter.decimal.period.js\" }, \"13\": { \"name\": \"storage.type.numeric.bigint.js\" }, \"14\": { \"name\": \"storage.type.numeric.bigint.js\" } }, \"match\": \"(?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$)\" }] }, \"numericConstant-literal\": { \"patterns\": [{ \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))NaN(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.nan.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Infinity(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.infinity.js\" }] }, \"object-binding-element\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?=((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(:))\", \"end\": \"(?=,|\\\\})\", \"patterns\": [{ \"include\": \"#object-binding-element-propertyName\" }, { \"include\": \"#binding-element\" }] }, { \"include\": \"#object-binding-pattern\" }, { \"include\": \"#destructuring-variable-rest\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#punctuation-comma\" }] }, \"object-binding-element-const\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?=((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(:))\", \"end\": \"(?=,|\\\\})\", \"patterns\": [{ \"include\": \"#object-binding-element-propertyName\" }, { \"include\": \"#binding-element-const\" }] }, { \"include\": \"#object-binding-pattern-const\" }, { \"include\": \"#destructuring-variable-rest-const\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#punctuation-comma\" }] }, \"object-binding-element-propertyName\": { \"begin\": \"(?=((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(:))\", \"end\": \"(:)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.destructuring.js\" } }, \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#numeric-literal\" }, { \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"variable.object.property.js\" }] }, \"object-binding-pattern\": { \"begin\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"patterns\": [{ \"include\": \"#object-binding-element\" }] }, \"object-binding-pattern-const\": { \"begin\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"patterns\": [{ \"include\": \"#object-binding-element-const\" }] }, \"object-identifiers\": { \"patterns\": [{ \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)(?=\\\\s*\\\\??\\\\.\\\\s*prototype\\\\b(?!\\\\$))\", \"name\": \"support.class.js\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.js\" }, \"2\": { \"name\": \"punctuation.accessor.optional.js\" }, \"3\": { \"name\": \"variable.other.constant.object.property.js\" }, \"4\": { \"name\": \"variable.other.object.property.js\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(?:(\\\\#?[A-Z][_$\\\\dA-Z]*)|(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*))(?=\\\\s*\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.constant.object.js\" }, \"2\": { \"name\": \"variable.other.object.js\" } }, \"match\": \"(?:([A-Z][_$\\\\dA-Z]*)|([_$A-Za-z][_$0-9A-Za-z]*))(?=\\\\s*\\\\??\\\\.\\\\s*\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)\" }] }, \"object-literal\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"name\": \"meta.objectliteral.js\", \"patterns\": [{ \"include\": \"#object-member\" }] }, \"object-literal-method-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:\\\\b(async)\\\\s+)?(?:\\\\b(get|set)\\\\s+)?(?:(\\\\*)\\\\s*)?(?=\\\\s*(((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(\\\\??))\\\\s*((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*))?[(])\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" }, \"2\": { \"name\": \"storage.type.property.js\" }, \"3\": { \"name\": \"keyword.generator.asterisk.js\" } }, \"end\": \"(?=\\\\}|;|,)|(?<=\\\\})\", \"name\": \"meta.method.declaration.js\", \"patterns\": [{ \"include\": \"#method-declaration-name\" }, { \"include\": \"#function-body\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:\\\\b(async)\\\\s+)?(?:\\\\b(get|set)\\\\s+)?(?:(\\\\*)\\\\s*)?(?=\\\\s*(((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(\\\\??))\\\\s*((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*))?[(])\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" }, \"2\": { \"name\": \"storage.type.property.js\" }, \"3\": { \"name\": \"keyword.generator.asterisk.js\" } }, \"end\": \"(?=\\\\(|<)\", \"patterns\": [{ \"include\": \"#method-declaration-name\" }] }] }, \"object-member\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#object-literal-method-declaration\" }, { \"begin\": \"(?=\\\\[)\", \"end\": \"(?=:)|((?<=[\\\\]])(?=\\\\s*[(<]))\", \"name\": \"meta.object.member.js meta.object-literal.key.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#array-literal\" }] }, { \"begin\": \"(?=[\\\\'\\\\\\\"\\\\`])\", \"end\": \"(?=:)|((?<=[\\\\'\\\\\\\"\\\\`])(?=((\\\\s*[(<,}])|(\\\\s+(as|satisifies)\\\\s+))))\", \"name\": \"meta.object.member.js meta.object-literal.key.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?=(\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$)))\", \"end\": \"(?=:)|(?=\\\\s*([(<,}])|(\\\\s+as|satisifies\\\\s+))\", \"name\": \"meta.object.member.js meta.object-literal.key.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#numeric-literal\" }] }, { \"begin\": \"(?<=[\\\\]\\\\'\\\\\\\"\\\\`])(?=\\\\s*[(<])\", \"end\": \"(?=\\\\}|;|,)|(?<=\\\\})\", \"name\": \"meta.method.declaration.js\", \"patterns\": [{ \"include\": \"#function-body\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.object-literal.key.js\" }, \"1\": { \"name\": \"constant.numeric.decimal.js\" } }, \"match\": \"(?![_$A-Za-z])([\\\\d]+)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:)\", \"name\": \"meta.object.member.js\" }, { \"captures\": { \"0\": { \"name\": \"meta.object-literal.key.js\" }, \"1\": { \"name\": \"entity.name.function.js\" } }, \"match\": \"(?:([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:(\\\\s*\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/)*\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\", \"name\": \"meta.object.member.js\" }, { \"captures\": { \"0\": { \"name\": \"meta.object-literal.key.js\" } }, \"match\": \"(?:[_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:)\", \"name\": \"meta.object.member.js\" }, { \"begin\": \"\\\\.\\\\.\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.spread.js\" } }, \"end\": \"(?=,|\\\\})\", \"name\": \"meta.object.member.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"captures\": { \"1\": { \"name\": \"variable.other.readwrite.js\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=,|\\\\}|$|\\\\/\\\\/|\\\\/\\\\*)\", \"name\": \"meta.object.member.js\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.as.js\" }, \"2\": { \"name\": \"storage.modifier.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as)\\\\s+(const)(?=\\\\s*([,}]|$))\", \"name\": \"meta.object.member.js\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(as)|(satisfies))\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.as.js\" }, \"2\": { \"name\": \"keyword.control.satisfies.js\" } }, \"end\": \"(?=[;),}\\\\]:?\\\\-+>]|\\\\|\\\\||\\\\&\\\\&|!==|$|^|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as|satisifies)\\\\s+))\", \"name\": \"meta.object.member.js\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?=[_$A-Za-z][_$0-9A-Za-z]*\\\\s*=)\", \"end\": \"(?=,|\\\\}|$|\\\\/\\\\/|\\\\/\\\\*)\", \"name\": \"meta.object.member.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"meta.object-literal.key.js punctuation.separator.key-value.js\" } }, \"end\": \"(?=,|\\\\})\", \"name\": \"meta.object.member.js\", \"patterns\": [{ \"begin\": \"(?<=:)\\\\s*(async)?(?=\\\\s*(<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)\\\\(\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#type-parameters\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#expression-inside-possibly-arrow-parens\" }] }] }, { \"begin\": \"(?<=:)\\\\s*(async)?\\\\s*(\\\\()(?=\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" }, \"2\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#expression-inside-possibly-arrow-parens\" }] }, { \"begin\": \"(?<=:)\\\\s*(async)?\\\\s*(?=<\\\\s*$)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" } }, \"end\": \"(?<=>)\", \"patterns\": [{ \"include\": \"#type-parameters\" }] }, { \"begin\": \"(?<=>)\\\\s*(\\\\()(?=\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#expression-inside-possibly-arrow-parens\" }] }, { \"include\": \"#possibly-arrow-return-type\" }, { \"include\": \"#expression\" }] }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#decl-block\" }] }, \"parameter-array-binding-pattern\": { \"begin\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.array.js\" } }, \"patterns\": [{ \"include\": \"#parameter-binding-element\" }, { \"include\": \"#punctuation-comma\" }] }, \"parameter-binding-element\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#regex\" }, { \"include\": \"#parameter-object-binding-pattern\" }, { \"include\": \"#parameter-array-binding-pattern\" }, { \"include\": \"#destructuring-parameter-rest\" }, { \"include\": \"#variable-initializer\" }] }, \"parameter-name\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(override|public|protected|private|readonly)\\\\s+(?=(override|public|protected|private|readonly)\\\\s+)\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.operator.rest.js\" }, \"3\": { \"name\": \"entity.name.function.js variable.language.this.js\" }, \"4\": { \"name\": \"entity.name.function.js\" }, \"5\": { \"name\": \"keyword.operator.optional.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(override|public|private|protected|readonly)\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*(\\\\??)(?=\\\\s*(=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))|(:\\\\s*((<)|([(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>)))))))|(:\\\\s*(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Function(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(:\\\\s*((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))))|(:\\\\s*(=>|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.operator.rest.js\" }, \"3\": { \"name\": \"variable.parameter.js variable.language.this.js\" }, \"4\": { \"name\": \"variable.parameter.js\" }, \"5\": { \"name\": \"keyword.operator.optional.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(override|public|private|protected|readonly)\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*(\\\\??)\" }] }, \"parameter-object-binding-element\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?=((\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$))|([_$A-Za-z][_$0-9A-Za-z]*)|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\]))\\\\s*(:))\", \"end\": \"(?=,|\\\\})\", \"patterns\": [{ \"include\": \"#object-binding-element-propertyName\" }, { \"include\": \"#parameter-binding-element\" }, { \"include\": \"#paren-expression\" }] }, { \"include\": \"#parameter-object-binding-pattern\" }, { \"include\": \"#destructuring-parameter-rest\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#punctuation-comma\" }] }, \"parameter-object-binding-pattern\": { \"begin\": \"(?:(\\\\.\\\\.\\\\.)\\\\s*)?(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.rest.js\" }, \"2\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.binding-pattern.object.js\" } }, \"patterns\": [{ \"include\": \"#parameter-object-binding-element\" }] }, \"parameter-type-annotation\": { \"patterns\": [{ \"begin\": \"(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.js\" } }, \"end\": \"(?=[,)])|(?==[^>])\", \"name\": \"meta.type.annotation.js\", \"patterns\": [{ \"include\": \"#type\" }] }] }, \"paren-expression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"paren-expression-possibly-arrow\": { \"patterns\": [{ \"begin\": \"(?<=[(=,])\\\\s*(async)?(?=\\\\s*((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*))?\\\\(\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#paren-expression-possibly-arrow-with-typeparameters\" }] }, { \"begin\": \"(?<=[(=,]|=>|^return|[^\\\\._$0-9A-Za-z]return)\\\\s*(async)?(?=\\\\s*((((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*))?\\\\()|(<)|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)))\\\\s*$)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.async.js\" } }, \"end\": \"(?<=\\\\))\", \"patterns\": [{ \"include\": \"#paren-expression-possibly-arrow-with-typeparameters\" }] }, { \"include\": \"#possibly-arrow-return-type\" }] }, \"paren-expression-possibly-arrow-with-typeparameters\": { \"patterns\": [{ \"include\": \"#type-parameters\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"patterns\": [{ \"include\": \"#expression-inside-possibly-arrow-parens\" }] }] }, \"possibly-arrow-return-type\": { \"begin\": \"(?<=\\\\)|^)\\\\s*(:)(?=\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*=>)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.arrow.js meta.return.type.arrow.js keyword.operator.type.annotation.js\" } }, \"contentName\": \"meta.arrow.js meta.return.type.arrow.js\", \"end\": \"(?==>|\\\\{|(^\\\\s*(export|function|class|interface|let|var|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|const|import|enum|namespace|module|type|abstract|declare)\\\\s+))\", \"patterns\": [{ \"include\": \"#arrow-return-type-body\" }] }, \"property-accessor\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(accessor|get|set)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.type.property.js\" }, \"punctuation-accessor\": { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.js\" }, \"2\": { \"name\": \"punctuation.accessor.optional.js\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\" }, \"punctuation-comma\": { \"match\": \",\", \"name\": \"punctuation.separator.comma.js\" }, \"punctuation-semicolon\": { \"match\": \";\", \"name\": \"punctuation.terminator.statement.js\" }, \"qstring-double\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": '(\")|((?:[^\\\\\\\\\\\\n])$)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.js\" }, \"2\": { \"name\": \"invalid.illegal.newline.js\" } }, \"name\": \"string.quoted.double.js\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"qstring-single\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": \"(\\\\')|((?:[^\\\\\\\\\\\\n])$)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.js\" }, \"2\": { \"name\": \"invalid.illegal.newline.js\" } }, \"name\": \"string.quoted.single.js\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"regex\": { \"patterns\": [{ \"begin\": \"(?<!\\\\+\\\\+|--|})(?<=[=(:,\\\\[?+!]|^return|[^\\\\._$0-9A-Za-z]return|^case|[^\\\\._$0-9A-Za-z]case|=>|&&|\\\\|\\\\||\\\\*\\\\/)\\\\s*(\\\\/)(?![\\\\/*])(?=(?:[^\\\\/\\\\\\\\\\\\[()]|\\\\\\\\.|\\\\[([^\\\\]\\\\\\\\]|\\\\\\\\.)+\\\\]|\\\\(([^)\\\\\\\\]|\\\\\\\\.)+\\\\))+\\\\/([dgimsuy]+|(?![\\\\/\\\\*])|(?=\\\\/\\\\*))(?!\\\\s*[a-zA-Z0-9_$]))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": \"(/)([dgimsuy]*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.js\" }, \"2\": { \"name\": \"keyword.other.js\" } }, \"name\": \"string.regexp.js\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"begin\": \"((?<![_$0-9A-Za-z)\\\\]]|\\\\+\\\\+|--|}|\\\\*\\\\/)|((?<=^return|[^\\\\._$0-9A-Za-z]return|^case|[^\\\\._$0-9A-Za-z]case))\\\\s*)\\\\/(?![\\\\/*])(?=(?:[^\\\\/\\\\\\\\\\\\[]|\\\\\\\\.|\\\\[([^\\\\]\\\\\\\\]|\\\\\\\\.)*\\\\])+\\\\/([dgimsuy]+|(?![\\\\/\\\\*])|(?=\\\\/\\\\*))(?!\\\\s*[a-zA-Z0-9_$]))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.js\" } }, \"end\": \"(/)([dgimsuy]*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.js\" }, \"2\": { \"name\": \"keyword.other.js\" } }, \"name\": \"string.regexp.js\", \"patterns\": [{ \"include\": \"#regexp\" }] }] }, \"regex-character-class\": { \"patterns\": [{ \"match\": \"\\\\\\\\[wWsSdDtrnvf]|\\\\.\", \"name\": \"constant.other.character-class.regexp\" }, { \"match\": \"\\\\\\\\([0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4})\", \"name\": \"constant.character.numeric.regexp\" }, { \"match\": \"\\\\\\\\c[A-Z]\", \"name\": \"constant.character.control.regexp\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.backslash.regexp\" }] }, \"regexp\": { \"patterns\": [{ \"match\": \"\\\\\\\\[bB]|\\\\^|\\\\$\", \"name\": \"keyword.control.anchor.regexp\" }, { \"captures\": { \"0\": { \"name\": \"keyword.other.back-reference.regexp\" }, \"1\": { \"name\": \"variable.other.regexp\" } }, \"match\": \"\\\\\\\\[1-9]\\\\d*|\\\\\\\\k<([a-zA-Z_$][\\\\w$]*)>\" }, { \"match\": \"[?+*]|\\\\{(\\\\d+,\\\\d+|\\\\d+,|,\\\\d+|\\\\d+)\\\\}\\\\??\", \"name\": \"keyword.operator.quantifier.regexp\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.or.regexp\" }, { \"begin\": \"(\\\\()((\\\\?=)|(\\\\?!)|(\\\\?<=)|(\\\\?<!))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" }, \"2\": { \"name\": \"punctuation.definition.group.assertion.regexp\" }, \"3\": { \"name\": \"meta.assertion.look-ahead.regexp\" }, \"4\": { \"name\": \"meta.assertion.negative-look-ahead.regexp\" }, \"5\": { \"name\": \"meta.assertion.look-behind.regexp\" }, \"6\": { \"name\": \"meta.assertion.negative-look-behind.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.assertion.regexp\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"begin\": \"\\\\((?:(\\\\?:)|(?:\\\\?<([a-zA-Z_$][\\\\w$]*)>))?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" }, \"1\": { \"name\": \"punctuation.definition.group.no-capture.regexp\" }, \"2\": { \"name\": \"variable.other.regexp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.regexp\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"begin\": \"(\\\\[)(\\\\^)?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.regexp\" } }, \"name\": \"constant.other.character-class.set.regexp\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.numeric.regexp\" }, \"2\": { \"name\": \"constant.character.control.regexp\" }, \"3\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"4\": { \"name\": \"constant.character.numeric.regexp\" }, \"5\": { \"name\": \"constant.character.control.regexp\" }, \"6\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"match\": \"(?:.|(\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\c[A-Z])|(\\\\\\\\.))-(?:[^\\\\]\\\\\\\\]|(\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\c[A-Z])|(\\\\\\\\.))\", \"name\": \"constant.other.character-class.range.regexp\" }, { \"include\": \"#regex-character-class\" }] }, { \"include\": \"#regex-character-class\" }] }, \"return-type\": { \"patterns\": [{ \"begin\": \"(?<=\\\\))\\\\s*(:)(?=\\\\s*\\\\S)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.js\" } }, \"end\": \"(?<![:|&])(?=$|^|[{};,]|//)\", \"name\": \"meta.return.type.js\", \"patterns\": [{ \"include\": \"#return-type-core\" }] }, { \"begin\": \"(?<=\\\\))\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.js\" } }, \"end\": \"(?<![:|&])((?=[{};,]|//|^\\\\s*$)|((?<=\\\\S)(?=\\\\s*$)))\", \"name\": \"meta.return.type.js\", \"patterns\": [{ \"include\": \"#return-type-core\" }] }] }, \"return-type-core\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<=[:|&])(?=\\\\s*\\\\{)\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#type-object\" }] }, { \"include\": \"#type-predicate-operator\" }, { \"include\": \"#type\" }] }, \"shebang\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.js\" } }, \"match\": \"\\\\A(#!).*(?=$)\", \"name\": \"comment.line.shebang.js\" }, \"single-line-comment-consuming-line-ending\": { \"begin\": \"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.js\" }, \"2\": { \"name\": \"comment.line.double-slash.js\" }, \"3\": { \"name\": \"punctuation.definition.comment.js\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.js\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.js\" } }, \"contentName\": \"comment.line.double-slash.js\", \"end\": \"(?=^)\" }, \"statements\": { \"patterns\": [{ \"include\": \"#declaration\" }, { \"include\": \"#control-statement\" }, { \"include\": \"#after-operator-block-as-object-literal\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#label\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-semicolon\" }, { \"include\": \"#string\" }, { \"include\": \"#comment\" }] }, \"string\": { \"patterns\": [{ \"include\": \"#qstring-single\" }, { \"include\": \"#qstring-double\" }, { \"include\": \"#template\" }] }, \"string-character-escape\": { \"match\": \"\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|u\\\\{[0-9A-Fa-f]+\\\\}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\", \"name\": \"constant.character.escape.js\" }, \"super-literal\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))super\\\\b(?!\\\\$)\", \"name\": \"variable.language.super.js\" }, \"support-function-call-identifiers\": { \"patterns\": [{ \"include\": \"#literal\" }, { \"include\": \"#support-objects\" }, { \"include\": \"#object-identifiers\" }, { \"include\": \"#punctuation-accessor\" }, { \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))import(?=\\\\s*[(]\\\\s*[\\\\\\\"\\\\'\\\\`]))\", \"name\": \"keyword.operator.expression.import.js\" }] }, \"support-objects\": { \"patterns\": [{ \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(arguments)\\\\b(?!\\\\$)\", \"name\": \"variable.language.arguments.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(Promise)\\\\b(?!\\\\$)\", \"name\": \"support.class.promise.js\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.import.js\" }, \"2\": { \"name\": \"punctuation.accessor.js\" }, \"3\": { \"name\": \"punctuation.accessor.optional.js\" }, \"4\": { \"name\": \"support.variable.property.importmeta.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(import)\\\\s*(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(meta)\\\\b(?!\\\\$)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.new.js\" }, \"2\": { \"name\": \"punctuation.accessor.js\" }, \"3\": { \"name\": \"punctuation.accessor.optional.js\" }, \"4\": { \"name\": \"support.variable.property.target.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(new)\\\\s*(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(target)\\\\b(?!\\\\$)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.js\" }, \"2\": { \"name\": \"punctuation.accessor.optional.js\" }, \"3\": { \"name\": \"support.variable.property.js\" }, \"4\": { \"name\": \"support.constant.js\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(?:(?:(constructor|length|prototype|__proto__)\\\\b(?!\\\\$|\\\\s*(<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\())|(?:(EPSILON|MAX_SAFE_INTEGER|MAX_VALUE|MIN_SAFE_INTEGER|MIN_VALUE|NEGATIVE_INFINITY|POSITIVE_INFINITY)\\\\b(?!\\\\$)))\" }, { \"captures\": { \"1\": { \"name\": \"support.type.object.module.js\" }, \"2\": { \"name\": \"support.type.object.module.js\" }, \"3\": { \"name\": \"punctuation.accessor.js\" }, \"4\": { \"name\": \"punctuation.accessor.optional.js\" }, \"5\": { \"name\": \"support.type.object.module.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(exports)|(module)(?:(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))(exports|id|filename|loaded|parent|children))?)\\\\b(?!\\\\$)\" }] }, \"switch-statement\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?=\\\\bswitch\\\\s*\\\\()\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"name\": \"switch-statement.expr.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(switch)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.switch.js\" }, \"2\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"name\": \"switch-expression.expr.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"(?=\\\\})\", \"name\": \"switch-block.expr.js\", \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(case|default(?=:))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.switch.js\" } }, \"end\": \"(?=:)\", \"name\": \"case-clause.expr.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(:)\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"case-clause.expr.js punctuation.definition.section.case-statement.js\" }, \"2\": { \"name\": \"meta.block.js punctuation.definition.block.js\" } }, \"contentName\": \"meta.block.js\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"meta.block.js punctuation.definition.block.js\" } }, \"patterns\": [{ \"include\": \"#statements\" }] }, { \"captures\": { \"0\": { \"name\": \"case-clause.expr.js punctuation.definition.section.case-statement.js\" } }, \"match\": \"(:)\" }, { \"include\": \"#statements\" }] }] }, \"template\": { \"patterns\": [{ \"include\": \"#template-call\" }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)?(`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tagged-template.js\" }, \"2\": { \"name\": \"string.template.js punctuation.definition.string.template.begin.js\" } }, \"contentName\": \"string.template.js\", \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"string.template.js punctuation.definition.string.template.end.js\" } }, \"patterns\": [{ \"include\": \"#template-substitution-element\" }, { \"include\": \"#string-character-escape\" }] }] }, \"template-call\": { \"patterns\": [{ \"begin\": \"(?=(([_$A-Za-z][_$0-9A-Za-z]*\\\\s*\\\\??\\\\.\\\\s*)*|(\\\\??\\\\.\\\\s*)?)([_$A-Za-z][_$0-9A-Za-z]*)(<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))(([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\\\s*)?`)\", \"end\": \"(?=`)\", \"patterns\": [{ \"begin\": \"(?=(([_$A-Za-z][_$0-9A-Za-z]*\\\\s*\\\\??\\\\.\\\\s*)*|(\\\\??\\\\.\\\\s*)?)([_$A-Za-z][_$0-9A-Za-z]*))\", \"end\": \"(?=(<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))(([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\\\s*)?`)\", \"patterns\": [{ \"include\": \"#support-function-call-identifiers\" }, { \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"entity.name.function.tagged-template.js\" }] }, { \"include\": \"#type-arguments\" }] }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)?\\\\s*(?=(<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))(([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>|<\\\\s*(((keyof|infer|typeof|readonly)\\\\s+)|(([_$A-Za-z][_$0-9A-Za-z]*|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))(?=\\\\s*([<>\\\\,\\\\.\\\\[]|=>|&(?!&)|\\\\|(?!\\\\|)))))([^<>(]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(?<==)>)*(?<!=)>))*(?<!=)>)*(?<!=)>\\\\s*)`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tagged-template.js\" } }, \"end\": \"(?=`)\", \"patterns\": [{ \"include\": \"#type-arguments\" }] }] }, \"template-substitution-element\": { \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.js\" } }, \"contentName\": \"meta.embedded.line.js\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.js\" } }, \"name\": \"meta.template.expression.js\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"template-type\": { \"patterns\": [{ \"include\": \"#template-call\" }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)?(`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tagged-template.js\" }, \"2\": { \"name\": \"string.template.js punctuation.definition.string.template.begin.js\" } }, \"contentName\": \"string.template.js\", \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"string.template.js punctuation.definition.string.template.end.js\" } }, \"patterns\": [{ \"include\": \"#template-type-substitution-element\" }, { \"include\": \"#string-character-escape\" }] }] }, \"template-type-substitution-element\": { \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.js\" } }, \"contentName\": \"meta.embedded.line.js\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.js\" } }, \"name\": \"meta.template.expression.js\", \"patterns\": [{ \"include\": \"#type\" }] }, \"ternary-expression\": { \"begin\": \"(?!\\\\?\\\\.\\\\s*[^\\\\d])(\\\\?)(?!\\\\?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.js\" } }, \"end\": \"\\\\s*(:)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.js\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"this-literal\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))this\\\\b(?!\\\\$)\", \"name\": \"variable.language.this.js\" }, \"type\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-string\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#type-builtin-literals\" }, { \"include\": \"#type-parameters\" }, { \"include\": \"#type-tuple\" }, { \"include\": \"#type-object\" }, { \"include\": \"#type-operators\" }, { \"include\": \"#type-conditional\" }, { \"include\": \"#type-fn-type-parameters\" }, { \"include\": \"#type-paren-or-function-parameters\" }, { \"include\": \"#type-function-return-type\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(readonly)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*\" }, { \"include\": \"#type-name\" }] }, \"type-alias-declaration\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(type)\\\\b\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.type.js\" }, \"4\": { \"name\": \"entity.name.type.alias.js\" } }, \"end\": \"(?=\\\\}|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"name\": \"meta.type.declaration.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameters\" }, { \"begin\": \"(=)\\\\s*(intrinsic)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.js\" }, \"2\": { \"name\": \"keyword.control.intrinsic.js\" } }, \"end\": \"(?=\\\\}|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.js\" } }, \"end\": \"(?=\\\\}|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"patterns\": [{ \"include\": \"#type\" }] }] }, \"type-annotation\": { \"patterns\": [{ \"begin\": \"(:)(?=\\\\s*\\\\S)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.js\" } }, \"end\": \"(?<![:|&])(?!\\\\s*[|&]\\\\s+)((?=^|[,);}\\\\]]|//)|(?==[^>])|((?<=[}>\\\\])]|[_$A-Za-z])\\\\s*(?=\\\\{)))\", \"name\": \"meta.type.annotation.js\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.js\" } }, \"end\": \"(?<![:|&])((?=[,);}\\\\]]|\\\\/\\\\/)|(?==[^>])|(?=^\\\\s*$)|((?<=[}>\\\\])]|[_$A-Za-z])\\\\s*(?=\\\\{)))\", \"name\": \"meta.type.annotation.js\", \"patterns\": [{ \"include\": \"#type\" }] }] }, \"type-arguments\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.begin.js\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.typeparameters.end.js\" } }, \"name\": \"meta.type.parameters.js\", \"patterns\": [{ \"include\": \"#type-arguments-body\" }] }, \"type-arguments-body\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"keyword.operator.type.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(_)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\" }, { \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }] }, \"type-builtin-literals\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(this|true|false|undefined|null|object)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"support.type.builtin.js\" }, \"type-conditional\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(extends)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" } }, \"end\": \"(?<=:)\", \"patterns\": [{ \"begin\": \"\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.ternary.js\" } }, \"end\": \":\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.ternary.js\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#type\" }] }] }, \"type-fn-type-parameters\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(abstract)\\\\s+)?(new)\\\\b(?=\\\\s*<)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.type.constructor.js storage.modifier.js\" }, \"2\": { \"name\": \"meta.type.constructor.js keyword.control.new.js\" } }, \"end\": \"(?<=>)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#type-parameters\" }] }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(abstract)\\\\s+)?(new)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.control.new.js\" } }, \"end\": \"(?<=\\\\))\", \"name\": \"meta.type.constructor.js\", \"patterns\": [{ \"include\": \"#function-parameters\" }] }, { \"begin\": \"((?=[(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>))))))\", \"end\": \"(?<=\\\\))\", \"name\": \"meta.type.function.js\", \"patterns\": [{ \"include\": \"#function-parameters\" }] }] }, \"type-function-return-type\": { \"patterns\": [{ \"begin\": \"(=>)(?=\\\\s*\\\\S)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.arrow.js\" } }, \"end\": \"(?<!=>)(?<![|&])(?=[,\\\\]){}=;>:?]|//|$)\", \"name\": \"meta.type.function.return.js\", \"patterns\": [{ \"include\": \"#type-function-return-type-core\" }] }, { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.function.arrow.js\" } }, \"end\": \"(?<!=>)(?<![|&])((?=[,\\\\]){}=;:?>]|//|^\\\\s*$)|((?<=\\\\S)(?=\\\\s*$)))\", \"name\": \"meta.type.function.return.js\", \"patterns\": [{ \"include\": \"#type-function-return-type-core\" }] }] }, \"type-function-return-type-core\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<==>)(?=\\\\s*\\\\{)\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#type-object\" }] }, { \"include\": \"#type-predicate-operator\" }, { \"include\": \"#type\" }] }, \"type-infer\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.infer.js\" }, \"2\": { \"name\": \"entity.name.type.js\" }, \"3\": { \"name\": \"keyword.operator.expression.extends.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(infer)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))(?:\\\\s+(extends)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))?\", \"name\": \"meta.type.infer.js\" }] }, \"type-name\": { \"patterns\": [{ \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(<)\", \"captures\": { \"1\": { \"name\": \"entity.name.type.module.js\" }, \"2\": { \"name\": \"punctuation.accessor.js\" }, \"3\": { \"name\": \"punctuation.accessor.optional.js\" }, \"4\": { \"name\": \"meta.type.parameters.js punctuation.definition.typeparameters.begin.js\" } }, \"contentName\": \"meta.type.parameters.js\", \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"meta.type.parameters.js punctuation.definition.typeparameters.end.js\" } }, \"patterns\": [{ \"include\": \"#type-arguments-body\" }] }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(<)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.type.js\" }, \"2\": { \"name\": \"meta.type.parameters.js punctuation.definition.typeparameters.begin.js\" } }, \"contentName\": \"meta.type.parameters.js\", \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"meta.type.parameters.js punctuation.definition.typeparameters.end.js\" } }, \"patterns\": [{ \"include\": \"#type-arguments-body\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.name.type.module.js\" }, \"2\": { \"name\": \"punctuation.accessor.js\" }, \"3\": { \"name\": \"punctuation.accessor.optional.js\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"entity.name.type.js\" }] }, \"type-object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.js\" } }, \"name\": \"meta.object.type.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#method-declaration\" }, { \"include\": \"#indexer-declaration\" }, { \"include\": \"#indexer-mapped-type-declaration\" }, { \"include\": \"#field-declaration\" }, { \"include\": \"#type-annotation\" }, { \"begin\": \"\\\\.\\\\.\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.spread.js\" } }, \"end\": \"(?=\\\\}|;|,|$)|(?<=\\\\})\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#punctuation-semicolon\" }, { \"include\": \"#type\" }] }, \"type-operators\": { \"patterns\": [{ \"include\": \"#typeof-operator\" }, { \"include\": \"#type-infer\" }, { \"begin\": \"([&|])(?=\\\\s*\\\\{)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.type.js\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#type-object\" }] }, { \"begin\": \"[&|]\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.type.js\" } }, \"end\": \"(?=\\\\S)\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))keyof(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.expression.keyof.js\" }, { \"match\": \"(\\\\?|:)\", \"name\": \"keyword.operator.ternary.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))import(?=\\\\s*\\\\()\", \"name\": \"keyword.operator.expression.import.js\" }] }, \"type-parameters\": { \"begin\": \"(<)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.typeparameters.begin.js\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.typeparameters.end.js\" } }, \"name\": \"meta.type.parameters.js\", \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(extends|in|out|const)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.js\" }, { \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }, { \"match\": \"(=)(?!>)\", \"name\": \"keyword.operator.assignment.js\" }] }, \"type-paren-or-function-parameters\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.js\" } }, \"name\": \"meta.type.paren.cover.js\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.operator.rest.js\" }, \"3\": { \"name\": \"entity.name.function.js variable.language.this.js\" }, \"4\": { \"name\": \"entity.name.function.js\" }, \"5\": { \"name\": \"keyword.operator.optional.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(public|private|protected|readonly)\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))\\\\s*(\\\\??)(?=\\\\s*(:\\\\s*((<)|([(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>)))))))|(:\\\\s*(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Function(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(:\\\\s*((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))))\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.js\" }, \"2\": { \"name\": \"keyword.operator.rest.js\" }, \"3\": { \"name\": \"variable.parameter.js variable.language.this.js\" }, \"4\": { \"name\": \"variable.parameter.js\" }, \"5\": { \"name\": \"keyword.operator.optional.js\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(public|private|protected|readonly)\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))\\\\s*(\\\\??)(?=:)\" }, { \"include\": \"#type-annotation\" }, { \"match\": \",\", \"name\": \"punctuation.separator.parameter.js\" }, { \"include\": \"#type\" }] }, \"type-predicate-operator\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.operator.type.asserts.js\" }, \"2\": { \"name\": \"variable.parameter.js variable.language.this.js\" }, \"3\": { \"name\": \"variable.parameter.js\" }, \"4\": { \"name\": \"keyword.operator.expression.is.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(asserts)\\\\s+)?(?!asserts)(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))\\\\s(is)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.type.asserts.js\" }, \"2\": { \"name\": \"variable.parameter.js variable.language.this.js\" }, \"3\": { \"name\": \"variable.parameter.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(asserts)\\\\s+(?!is)(?:(this)|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))asserts(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.type.asserts.js\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))is(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.expression.is.js\" }] }, \"type-primitive\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(string|number|bigint|boolean|symbol|any|void|never|unknown)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"support.type.primitive.js\" }, \"type-string\": { \"patterns\": [{ \"include\": \"#qstring-single\" }, { \"include\": \"#qstring-double\" }, { \"include\": \"#template-type\" }] }, \"type-tuple\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.square.js\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.js\" } }, \"name\": \"meta.type.tuple.js\", \"patterns\": [{ \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.rest.js\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.label.js\" }, \"2\": { \"name\": \"keyword.operator.optional.js\" }, \"3\": { \"name\": \"punctuation.separator.label.js\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(\\\\?)?\\\\s*(:)\" }, { \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }] }, \"typeof-operator\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))typeof(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.expression.typeof.js\" } }, \"end\": \"(?=[,);}\\\\]=>:&|{?]|(extends\\\\s+)|$|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))\", \"patterns\": [{ \"include\": \"#type-arguments\" }, { \"include\": \"#expression\" }] }, \"undefined-literal\": { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))undefined(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.undefined.js\" }, \"var-expr\": { \"patterns\": [{ \"begin\": \"(?=(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(var|let)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))\", \"end\": \"(?!(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(var|let)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))((?=^|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))|((?<!^let|[^\\\\._$0-9A-Za-z]let|^var|[^\\\\._$0-9A-Za-z]var)(?=\\\\s*$)))\", \"name\": \"meta.var.expr.js\", \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(var|let)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.js\" } }, \"end\": \"(?=\\\\S)\" }, { \"include\": \"#destructuring-variable\" }, { \"include\": \"#var-single-variable\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#comment\" }, { \"begin\": \"(,)\\\\s*(?=$|\\\\/\\\\/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.comma.js\" } }, \"end\": \"(?<!,)(((?==|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|^\\\\s*$))|((?<=\\\\S)(?=\\\\s*$)))\", \"patterns\": [{ \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#comment\" }, { \"include\": \"#destructuring-variable\" }, { \"include\": \"#var-single-variable\" }, { \"include\": \"#punctuation-comma\" }] }, { \"include\": \"#punctuation-comma\" }] }, { \"begin\": \"(?=(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(const(?!\\\\s+enum\\\\b))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.js\" } }, \"end\": \"(?!(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(const(?!\\\\s+enum\\\\b))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))((?=^|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))|((?<!^const|[^\\\\._$0-9A-Za-z]const)(?=\\\\s*$)))\", \"name\": \"meta.var.expr.js\", \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b(const(?!\\\\s+enum\\\\b))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.js\" } }, \"end\": \"(?=\\\\S)\" }, { \"include\": \"#destructuring-const\" }, { \"include\": \"#var-single-const\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#comment\" }, { \"begin\": \"(,)\\\\s*(?=$|\\\\/\\\\/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.comma.js\" } }, \"end\": \"(?<!,)(((?==|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|^\\\\s*$))|((?<=\\\\S)(?=\\\\s*$)))\", \"patterns\": [{ \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#comment\" }, { \"include\": \"#destructuring-const\" }, { \"include\": \"#var-single-const\" }, { \"include\": \"#punctuation-comma\" }] }, { \"include\": \"#punctuation-comma\" }] }, { \"begin\": \"(?=(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b((?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.js\" } }, \"end\": \"(?!(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b((?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))((?=;|}|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b))|((?<!^using|[^\\\\._$0-9A-Za-z]using|^await\\\\s+using|[^\\\\._$0-9A-Za-z]await\\\\s+using)(?=\\\\s*$)))\", \"name\": \"meta.var.expr.js\", \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bexport)\\\\s+)?(?:(\\\\bdeclare)\\\\s+)?\\\\b((?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.export.js\" }, \"2\": { \"name\": \"storage.modifier.js\" }, \"3\": { \"name\": \"storage.type.js\" } }, \"end\": \"(?=\\\\S)\" }, { \"include\": \"#var-single-const\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#comment\" }, { \"begin\": \"(,)\\\\s*((?!\\\\S)|(?=\\\\/\\\\/))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.comma.js\" } }, \"end\": \"(?<!,)(((?==|;|}|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|^\\\\s*$))|((?<=\\\\S)(?=\\\\s*$)))\", \"patterns\": [{ \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#comment\" }, { \"include\": \"#var-single-const\" }, { \"include\": \"#punctuation-comma\" }] }, { \"include\": \"#punctuation-comma\" }] }] }, \"var-single-const\": { \"patterns\": [{ \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)(?=\\\\s*(=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))|(:\\\\s*((<)|([(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>)))))))|(:\\\\s*(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Function(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(:\\\\s*((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))))|(:\\\\s*(=>|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.js variable.other.constant.js entity.name.function.js\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|(;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b)))\", \"name\": \"meta.var-single-variable.expr.js\", \"patterns\": [{ \"include\": \"#var-single-variable-type-annotation\" }] }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.js variable.other.constant.js\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|(;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b)))\", \"name\": \"meta.var-single-variable.expr.js\", \"patterns\": [{ \"include\": \"#var-single-variable-type-annotation\" }] }] }, \"var-single-variable\": { \"patterns\": [{ \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)(!)?(?=\\\\s*(=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))|(:\\\\s*((<)|([(]\\\\s*(([)])|(\\\\.\\\\.\\\\.)|([_$0-9A-Za-z]+\\\\s*(([:,?=])|([)]\\\\s*=>)))))))|(:\\\\s*(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Function(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.)))|(:\\\\s*((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))))))|(:\\\\s*(=>|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(<[^<>]*>)|[^<>(),=])+=\\\\s*(((async\\\\s+)?((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((async\\\\s*)?(((<\\\\s*$)|([(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|([<]\\\\s*[_$A-Za-z][_$0-9A-Za-z]*\\\\s+extends\\\\s*[^=>])|((<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*(((const\\\\s+)?[_$A-Za-z])|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.js entity.name.function.js\" }, \"2\": { \"name\": \"keyword.operator.definiteassignment.js\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|(;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b)))\", \"name\": \"meta.var-single-variable.expr.js\", \"patterns\": [{ \"include\": \"#var-single-variable-type-annotation\" }] }, { \"begin\": \"([A-Z][_$\\\\dA-Z]*)(?![_$0-9A-Za-z])(!)?\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.js variable.other.constant.js\" }, \"2\": { \"name\": \"keyword.operator.definiteassignment.js\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|(;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b)))\", \"name\": \"meta.var-single-variable.expr.js\", \"patterns\": [{ \"include\": \"#var-single-variable-type-annotation\" }] }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)(!)?\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.js variable.other.readwrite.js\" }, \"2\": { \"name\": \"keyword.operator.definiteassignment.js\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+)|(;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|(?:\\\\bawait\\\\s+(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)\\\\b)|break|case|catch|class|const|continue|declare|do|else|enum|export|finally|function|for|goto|if|import|interface|let|module|namespace|switch|return|throw|try|type|(?:\\\\busing(?=\\\\s+(?!in\\\\b|of\\\\b(?!\\\\s*(?:of\\\\b|=)))[_$A-Za-z])\\\\b)|var|while)\\\\b)))\", \"name\": \"meta.var-single-variable.expr.js\", \"patterns\": [{ \"include\": \"#var-single-variable-type-annotation\" }] }] }, \"var-single-variable-type-annotation\": { \"patterns\": [{ \"include\": \"#type-annotation\" }, { \"include\": \"#string\" }, { \"include\": \"#comment\" }] }, \"variable-initializer\": { \"patterns\": [{ \"begin\": \"(?<!=|!)(=)(?!=)(?=\\\\s*\\\\S)(?!\\\\s*.*=>\\\\s*$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.js\" } }, \"end\": \"(?=$|^|[,);}\\\\]]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+))\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(?<!=|!)(=)(?!=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.js\" } }, \"end\": \"(?=[,);}\\\\]]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(of|in)\\\\s+))|(?=^\\\\s*$)|(?<![\\\\|\\\\&+\\\\-\\\\*\\\\/])(?<=\\\\S)(?<!=)(?=\\\\s*$)\", \"patterns\": [{ \"include\": \"#expression\" }] }] } }, \"scopeName\": \"source.js\", \"aliases\": [\"js\"] });\nvar javascript = [\n  lang\n];\n\nexport { javascript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,QAAQ;IAAc,YAAY;QAAC;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,SAAS;YAA+J,QAAQ;QAAsB;QAAG,0CAA0C;YAAE,SAAS;YAAqQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;oBAAwG,QAAQ;gBAAgB;gBAAG;oBAAE,SAAS;oBAAm3D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAuR,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAyD,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAA6C;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAmB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAuR,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAkG,QAAQ;QAA4B;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+F,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAgG,QAAQ;gBAAqC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,OAAO;oBAAc,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,OAAO;oBAAgB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAO;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA2I,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAA4C;aAAE;QAAC;QAAG,4CAA4C;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAA2B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAqG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAA4C;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAc,OAAO;oBAAyY,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAA0C;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAAyH,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAA8I;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA6B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAAoD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,eAAe;oBAAgC,OAAO;gBAAQ;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAoH,QAAQ;gBAA8B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAA6I;gBAAG;oBAAE,SAAS;oBAAyH,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAmG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAA6X,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgH,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAiG,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAAqG,QAAQ;gBAA4B;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,SAAS;oBAA2G,QAAQ;gBAAsB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAoD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAW,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuE,OAAO;oBAA6E,QAAQ;oBAA2C,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuE,OAAO;oBAA6E,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAA+B;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,QAAQ;oBAA4C,YAAY;wBAAC;4BAAE,WAAW;wBAAoC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAA6B;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,SAAS;QAAiD;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuE,OAAO;oBAA6E,QAAQ;oBAA2C,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuE,OAAO;oBAA6E,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,SAAS;QAAiD;QAAG,qCAAqC;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyD;YAAE;YAAG,SAAS;QAAiD;QAAG,cAAc;YAAE,SAAS;YAA+N,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAS,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,SAAS;oBAA4C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAM,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAAsD,QAAQ;wBAA2C;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAuD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,SAAS;gBAA6E;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA+E;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAc,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,SAAS;wBAAc;wBAAG;4BAAE,SAAS;4BAAoB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;gCAAG,KAAK;oCAAE,QAAQ;gCAAmD;gCAAG,KAAK;oCAAE,QAAQ;gCAAiD;4BAAE;4BAAG,eAAe;4BAAoC,OAAO;4BAA2B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;gCAAG,KAAK;oCAAE,QAAQ;gCAAmD;gCAAG,KAAK;oCAAE,QAAQ;gCAAiD;4BAAE;wBAAE;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAqB;4BAAE;4BAAG,SAAS;wBAA4B;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAsG;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA2J;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAAoF;gBAAG;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAAgG;gBAAG;oBAAE,SAAS;oBAAyB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAA0B,QAAQ;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAAuB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmE;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqB;gCAAG,KAAK;oCAAE,QAAQ;gCAAiE;gCAAG,KAAK;oCAAE,QAAQ;gCAA+B;4BAAE;4BAAG,SAAS,CAAC,4SAA4S,CAAC;4BAAE,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA2O;gBAAG;oBAAE,SAAS,CAAC,uDAAuD,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAoB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;gBAAE;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAA8E;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;oBAAs8B,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;gBAA4C;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAoJ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAY,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAA8B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA+B;4BAAE;4BAAG,OAAO;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAsI,OAAO;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;gBAA6G;gBAAG;oBAAE,SAAS;oBAAiH,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAwX,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwf,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAwX,QAAQ;oBAAkB,YAAY;wBAAC;4BAAE,WAAW;wBAA6B;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,2CAA2C;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAAoJ;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAA82K;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAAqR;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkG,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAkJ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAA+G;gBAAG;oBAAE,SAAS;oBAAiG,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAqG,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAA8F,QAAQ;gBAA0B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA+F,QAAQ;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAAqF;gBAAG;oBAAE,SAAS;oBAAyE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAwH,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAiC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAkG,OAAO;oBAAsD,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAqD;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAuxB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAquB,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;gBAA0pK;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAA0D;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAyC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAmK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAY,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAgC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAk7C,OAAO;oBAA07C,YAAY;wBAAC;4BAAE,SAAS;4BAA8H,OAAO;4BAA+yC,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8J,OAAO;oBAAoK,YAAY;wBAAC;4BAAE,SAAS;4BAA8H,OAAO;4BAA2B,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,QAAQ;gBAAyD;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA+D;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAkC,QAAQ;gBAA0B;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAyL,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,OAAO;YAA+X,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAgJ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,OAAO;YAAkB,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA6C;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA4B,QAAQ;QAAsD;QAAG,uBAAuB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAA6zE;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA6E;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;gBAAoE;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA8B;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuH,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAA8D,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA+I,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAyC;4BAAE;4BAAG,OAAO;4BAAmB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAmB;4BAAE;4BAAG,QAAQ;4BAAoB,YAAY;gCAAC;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAsL,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAiD,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,WAAW;gBAA6C;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS,CAAC,gDAAgD,CAAC;oBAAE,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA6B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+K,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2K,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAA6C;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS;wBAAgE;wBAAG;4BAAE,SAAS;4BAA8B,QAAQ;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAA6E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAiF,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqC;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAAwN;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAA6B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAA+C;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA0B;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAoH,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mCAAmC;YAAE,SAAS;YAA8H,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAgC,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;gBAAwD;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,SAAS;oBAA+D,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAgD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS;wBAAgD;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS;wBAAsC;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAuG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAA+Q,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAA+I,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAA2B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,eAAe;oBAAmC,OAAO;oBAAsB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,OAAO;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA4C;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;oBAA6C,QAAQ;gBAA+B;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,eAAe;YAA+B,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAyJ,OAAO;YAAsI,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAe,YAAY;gBAAC;oBAAE,SAAS;oBAAqJ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAU,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS,CAAC,8BAA8B,CAAC;YAAE,QAAQ;QAAiC;QAAG,0BAA0B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAA2F;QAAG,sBAAsB;YAAE,SAAS;YAAQ,OAAO;YAAa,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAQ,QAAQ;QAA+B;QAAG,yBAAyB;YAAE,SAAS;YAA6Y,OAAO;YAAyJ,YAAY;gBAAC;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAA4H,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,eAAe;YAAwB,OAAO;YAA6H,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,4CAA4C;YAAE,SAAS;YAAyT,OAAO;YAAgI,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAoC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4N,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,OAAO;oBAA0B,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAy1B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA0B,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA68C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA0B,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAgrB,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAoD;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAyI,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAA+X,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAgG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAoP,QAAQ;YAAe,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAA+F,QAAQ;QAA4B;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;oBAA6D,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;oBAA+C,QAAQ;gBAA6B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;oBAAkD,QAAQ;gBAA4B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,MAAM;4BAAE,QAAQ;wBAAmC;wBAAG,MAAM;4BAAE,QAAQ;wBAAiC;wBAAG,MAAM;4BAAE,QAAQ;wBAAmC;wBAAG,MAAM;4BAAE,QAAQ;wBAAiC;wBAAG,MAAM;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAkW;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8F,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAmG,QAAQ;gBAAgC;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAqqB,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAqqB,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,uCAAuC;YAAE,SAAS;YAAqqB,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA8B;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoE,QAAQ;gBAAmB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAA8I;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAmG;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,qCAAqC;YAAE,SAAS;YAA23C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAwB,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAA23C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAkC,QAAQ;oBAAoD,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,OAAO;oBAAyE,QAAQ;oBAAoD,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAogB,OAAO;oBAAkD,QAAQ;oBAAoD,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoC,OAAO;oBAAwB,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;oBAA2E,QAAQ;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;oBAA02E,QAAQ;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;oBAAiF,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;oBAA2D,QAAQ;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;oBAAgF,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAyE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAiH,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqC,OAAO;oBAA6B,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgE;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,SAAS;4BAAy9B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,OAAO;4BAAY,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;gCAAG;oCAAE,SAAS;oCAAO,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAsB;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsB;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,WAAW;wCAA2C;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA2X,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAA2C;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAoC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,OAAO;4BAAU,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAA+W,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAA2C;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAA8B;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mCAAmC;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAAoJ;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAA82K;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAAuQ;aAAE;QAAC;QAAG,oCAAoC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAqqB,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;wBAAG;4BAAE,WAAW;wBAA6B;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAoC;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oCAAoC;YAAE,SAAS;YAA6B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAoC;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAsB,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mCAAmC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAg+B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAuD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2xC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAuD;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,uDAAuD;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA2C;qBAAE;gBAAC;aAAE;QAAC;QAAG,8BAA8B;YAAE,SAAS;YAAwG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8E;YAAE;YAAG,eAAe;YAA2C,OAAO;YAAuR,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA6G,QAAQ;QAA2B;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAAkC;QAAG,qBAAqB;YAAE,SAAS;YAAK,QAAQ;QAAiC;QAAG,yBAAyB;YAAE,SAAS;YAAK,QAAQ;QAAsC;QAAG,kBAAkB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAyB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoR,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqP,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAkD,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA6C;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;gBAA2C;gBAAG;oBAAE,SAAS;oBAAgD,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAwC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAA6C,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,SAAS;4BAAwK,QAAQ;wBAA8C;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAA+B,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAwD,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAyB,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAkB,QAAQ;QAA0B;QAAG,6CAA6C;YAAE,SAAS;YAAoD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,eAAe;YAAgC,OAAO;QAAQ;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0C;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAAmH,QAAQ;QAA+B;QAAG,iBAAiB;YAAE,SAAS;YAAgE,QAAQ;QAA6B;QAAG,qCAAqC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,SAAS;oBAAsF,QAAQ;gBAAwC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsE,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAoE,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAA+G;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA8G;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAA2zB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,SAAS;gBAAgK;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAqE,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAkE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,SAAS;4BAA8G,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,OAAO;4BAAS,QAAQ;4BAAuB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAgB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuE;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;4BAAE;4BAAG,eAAe;4BAAiB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAgD;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuE;4BAAE;4BAAG,SAAS;wBAAM;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAqE;oBAAE;oBAAG,eAAe;oBAAsB,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmE;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAu2C,OAAO;oBAAS,YAAY;wBAAC;4BAAE,SAAS;4BAA6F,OAAO;4BAAkxC,YAAY;gCAAC;oCAAE,WAAW;gCAAqC;gCAAG;oCAAE,SAAS;oCAA8B,QAAQ;gCAA0C;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgzC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iCAAiC;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,eAAe;YAAyB,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAqE;oBAAE;oBAAG,eAAe;oBAAsB,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmE;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAsC;wBAAG;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;aAAE;QAAC;QAAG,sCAAsC;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsD;YAAE;YAAG,eAAe;YAAyB,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAA+D,QAAQ;QAA4B;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAqC;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAAwG;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAwI,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAA0X,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAgE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAA0X,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAA0X,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAkG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAA+F,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAA6F;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAkI,QAAQ;QAA0B;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAU,YAAY;wBAAC;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAU,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAY,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+E,OAAO;oBAAY,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAA2C,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAsE,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAkC;qBAAE;gBAAC;aAAE;QAAC;QAAG,kCAAkC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAsB,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,SAAS;oBAA4L,QAAQ;gBAAqB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyE;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuE;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyE;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuE;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAAgE;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAsB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;gBAAU;gBAAG;oBAAE,SAAS;oBAAgG,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAAwC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAiH,QAAQ;gBAAsB;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAAiC;aAAE;QAAC;QAAG,qCAAqC;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAAuwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;gBAAyN;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqC;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAAmK;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;gBAAkJ;gBAAG;oBAAE,SAAS;oBAAkG,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAA6F,QAAQ;gBAAoC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAuJ,QAAQ;QAA4B;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,SAAS;oBAAa,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAA2F;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAiG,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAuZ,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAoG,QAAQ;QAAiC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoJ,OAAO;oBAA6oB,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,SAAS;4BAAoJ,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;4BAAG,OAAO;wBAAU;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAuB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;4BAAE;4BAAG,OAAO;4BAA6G,YAAY;gCAAC;oCAAE,WAAW;gCAA6C;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAA0B;gCAAG;oCAAE,WAAW;gCAAuB;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,OAAO;oBAAooB,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,SAAS;4BAAiK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;4BAAG,OAAO;wBAAU;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAuB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;4BAAE;4BAAG,OAAO;4BAA6G,YAAY;gCAAC;oCAAE,WAAW;gCAA6C;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAuB;gCAAG;oCAAE,WAAW;gCAAoB;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,OAAO;oBAAwzB,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,SAAS;4BAAuS,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAkB;4BAAE;4BAAG,OAAO;wBAAU;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAA+B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;4BAAE;4BAAG,OAAO;4BAA6G,YAAY;gCAAC;oCAAE,WAAW;gCAA6C;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAoB;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqoK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiF;oBAAE;oBAAG,OAAO;oBAA8b,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAA8b,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyoK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAA8b,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyD;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAA8b,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAA8b,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAuC;qBAAE;gBAAC;aAAE;QAAC;QAAG,uCAAuC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAgF,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAsI,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAa,WAAW;QAAC;KAAK;AAAC;AACltwK,IAAI,aAAa;IACf;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/css.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"CSS\", \"name\": \"css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#combinators\" }, { \"include\": \"#selector\" }, { \"include\": \"#at-rules\" }, { \"include\": \"#rule-list\" }], \"repository\": { \"at-rules\": { \"patterns\": [{ \"begin\": \"\\\\A(?:\\\\xEF\\\\xBB\\\\xBF)?(?i:(?=\\\\s*@charset\\\\b))\", \"end\": \";|(?=$)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.charset.css\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.illegal.not-lowercase.charset.css\" }, \"2\": { \"name\": \"invalid.illegal.leading-whitespace.charset.css\" }, \"3\": { \"name\": \"invalid.illegal.no-whitespace.charset.css\" }, \"4\": { \"name\": \"invalid.illegal.whitespace.charset.css\" }, \"5\": { \"name\": \"invalid.illegal.not-double-quoted.charset.css\" }, \"6\": { \"name\": \"invalid.illegal.unclosed-string.charset.css\" }, \"7\": { \"name\": \"invalid.illegal.unexpected-characters.charset.css\" } }, \"match\": '\\\\G((?!@charset)@\\\\w+)|\\\\G(\\\\s+)|(@charset\\\\S[^;]*)|(?<=@charset)(\\\\x20{2,}|\\\\t+)|(?<=@charset\\\\x20)([^\";]+)|(\"[^\"]+$)|(?<=\")([^;]+)' }, { \"captures\": { \"1\": { \"name\": \"keyword.control.at-rule.charset.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"match\": \"((@)charset)(?=\\\\s)\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"|$', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"begin\": '(?:\\\\G|^)(?=(?:[^\"])+$)', \"end\": \"$\", \"name\": \"invalid.illegal.unclosed.string.css\" }] }] }, { \"begin\": `(?i)((@)import)(?:\\\\s+|$|(?=['\"]|/\\\\*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.import.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.import.css\", \"patterns\": [{ \"begin\": \"\\\\G\\\\s*(?=/\\\\*)\", \"end\": \"(?<=\\\\*/)\\\\s*\", \"patterns\": [{ \"include\": \"#comment-block\" }] }, { \"include\": \"#string\" }, { \"include\": \"#url\" }, { \"include\": \"#media-query-list\" }] }, { \"begin\": \"(?i)((@)font-face)(?=\\\\s*|{|/\\\\*|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.font-face.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?!\\\\G)\", \"name\": \"meta.at-rule.font-face.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#rule-list\" }] }, { \"begin\": \"(?i)(@)page(?=[\\\\s:{]|/\\\\*|$)\", \"captures\": { \"0\": { \"name\": \"keyword.control.at-rule.page.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*($|[:{;]))\", \"name\": \"meta.at-rule.page.css\", \"patterns\": [{ \"include\": \"#rule-list\" }] }, { \"begin\": \"(?i)(?=@media(\\\\s|\\\\(|/\\\\*|$))\", \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)media\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.media.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.media.header.css\", \"patterns\": [{ \"include\": \"#media-query-list\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.media.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.media.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.media.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": `(?i)(?=@counter-style([\\\\s'\"{;]|/\\\\*|$))`, \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)counter-style\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.counter-style.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*{)\", \"name\": \"meta.at-rule.counter-style.header.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.parameter.style-name.css\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.counter-style.body.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#rule-list-innards\" }] }] }, { \"begin\": `(?i)(?=@document([\\\\s'\"{;]|/\\\\*|$))`, \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)document\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.document.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.document.header.css\", \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(url-prefix|domain|regexp)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.document-rule.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.document-rule.css\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"match\": `[^'\")\\\\s]+`, \"name\": \"variable.parameter.document-rule.css\" }] }, { \"include\": \"#url\" }, { \"include\": \"#commas\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.document.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.document.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.document.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": `(?i)(?=@(?:-(?:webkit|moz|o|ms)-)?keyframes([\\\\s'\"{;]|/\\\\*|$))`, \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)(?:-(?:webkit|moz|o|ms)-)?keyframes\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.keyframes.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*{)\", \"name\": \"meta.at-rule.keyframes.header.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.parameter.keyframe-list.css\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.keyframes.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.keyframes.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.keyframes.body.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.keyframe-offset.css\" }, \"2\": { \"name\": \"entity.other.keyframe-offset.percentage.css\" } }, \"match\": \"(?i)(?<![\\\\w-])(from|to)(?![\\\\w-])|([-+]?(?:\\\\d+(?:\\\\.\\\\d+)?|\\\\.\\\\d+)%)\" }, { \"include\": \"#rule-list\" }] }] }, { \"begin\": \"(?i)(?=@supports(\\\\s|\\\\(|/\\\\*|$))\", \"end\": \"(?<=})(?!\\\\G)|(?=;)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)supports\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.supports.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.supports.header.css\", \"patterns\": [{ \"include\": \"#feature-query-operators\" }, { \"include\": \"#feature-query\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.supports.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.supports.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.supports.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": `(?i)((@)(-(ms|o)-)?viewport)(?=[\\\\s'\"{;]|/\\\\*|$)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.viewport.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[@{;])\", \"name\": \"meta.at-rule.viewport.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"begin\": `(?i)((@)font-feature-values)(?=[\\\\s'\"{;]|/\\\\*|$)\\\\s*`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.font-feature-values.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"contentName\": \"variable.parameter.font-name.css\", \"end\": \"(?=\\\\s*[@{;])\", \"name\": \"meta.at-rule.font-features.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] }, { \"include\": \"#font-features\" }, { \"begin\": `(?i)((@)namespace)(?=[\\\\s'\";]|/\\\\*|$)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.namespace.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \";|(?=[@{])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.namespace.css\", \"patterns\": [{ \"include\": \"#url\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#comment-block\" }] }, \"2\": { \"name\": \"entity.name.function.namespace-prefix.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?i)(?:\\\\G|^|(?<=\\\\s))(?=(?<=\\\\s|^)(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])|\\\\s*/\\\\*(?:[^*]|\\\\*[^/])*\\\\*/)(.*?)((?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*)\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?i)(?=@[\\\\w-]+[^;]+;s*$)\", \"end\": \"(?<=;)(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)[\\\\w-]+\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"name\": \"meta.at-rule.header.css\" }] }, { \"begin\": \"(?i)(?=@[\\\\w-]+(\\\\s|\\\\(|{|/\\\\*|$))\", \"end\": \"(?<=})(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(?i)\\\\G(@)[\\\\w-]+\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.at-rule.css\" }, \"1\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?=\\\\s*[{;])\", \"name\": \"meta.at-rule.header.css\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.end.bracket.curly.css\" } }, \"name\": \"meta.at-rule.body.css\", \"patterns\": [{ \"include\": \"$self\" }] }] }] }, \"color-keywords\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)(?![\\\\w-])\", \"name\": \"support.constant.color.w3c-standard-color-name.css\" }, { \"match\": \"(?i)(?<![\\\\w-])(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|transparent|turquoise|violet|wheat|whitesmoke|yellowgreen)(?![\\\\w-])\", \"name\": \"support.constant.color.w3c-extended-color-name.css\" }, { \"match\": \"(?i)(?<![\\\\w-])currentColor(?![\\\\w-])\", \"name\": \"support.constant.color.current.css\" }, { \"match\": \"(?i)(?<![\\\\w-])(ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText)(?![\\\\w-])\", \"name\": \"invalid.deprecated.color.system.css\" }] }, \"combinators\": { \"patterns\": [{ \"match\": \"/deep/|>>>\", \"name\": \"invalid.deprecated.combinator.css\" }, { \"match\": \">>|>|\\\\+|~\", \"name\": \"keyword.operator.combinator.css\" }] }, \"commas\": { \"match\": \",\", \"name\": \"punctuation.separator.list.comma.css\" }, \"comment-block\": { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.css\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.css\" } }, \"name\": \"comment.block.css\" }, \"escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[0-9a-fA-F]{1,6}\", \"name\": \"constant.character.escape.codepoint.css\" }, { \"begin\": \"\\\\\\\\$\\\\s*\", \"end\": \"^(?<!\\\\G)\", \"name\": \"constant.character.escape.newline.css\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.css\" }] }, \"feature-query\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.condition.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.condition.end.bracket.round.css\" } }, \"name\": \"meta.feature-query.css\", \"patterns\": [{ \"include\": \"#feature-query-operators\" }, { \"include\": \"#feature-query\" }] }, \"feature-query-operators\": { \"patterns\": [{ \"match\": \"(?i)(?<=[\\\\s()]|^|\\\\*/)(and|not|or)(?=[\\\\s()]|/\\\\*|$)\", \"name\": \"keyword.operator.logical.feature.$1.css\" }, { \"include\": \"#rule-list-innards\" }] }, \"font-features\": { \"begin\": `(?i)((@)(annotation|character-variant|ornaments|styleset|stylistic|swash))(?=[\\\\s@'\"{;]|/\\\\*|$)`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.at-rule.${3:/downcase}.css\" }, \"2\": { \"name\": \"punctuation.definition.keyword.css\" } }, \"end\": \"(?<=})\", \"name\": \"meta.at-rule.${3:/downcase}.css\", \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.css\" } }, \"name\": \"meta.property-list.font-feature.css\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.font-feature.css\" }, { \"include\": \"#rule-list-innards\" }] }] }, \"functional-pseudo-classes\": { \"patterns\": [{ \"begin\": \"(?i)((:)dir)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"match\": \"(?i)(?<![\\\\w-])(ltr|rtl)(?![\\\\w-])\", \"name\": \"support.constant.text-direction.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)((:)lang)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"match\": \"(?<=[(,\\\\s])[a-zA-Z]+(-[a-zA-Z0-9]*|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*(?=[),\\\\s])\", \"name\": \"support.constant.language-range.css\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"include\": \"#escapes\" }, { \"match\": '(?<=[\"\\\\s])[a-zA-Z*]+(-[a-zA-Z0-9*]*)*(?=[\"\\\\s])', \"name\": \"support.constant.language-range.css\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.single.css\", \"patterns\": [{ \"include\": \"#escapes\" }, { \"match\": \"(?<=['\\\\s])[a-zA-Z*]+(-[a-zA-Z0-9*]*)*(?=['\\\\s])\", \"name\": \"support.constant.language-range.css\" }] }, { \"include\": \"#commas\" }] }, { \"begin\": \"(?i)((:)(?:not|has|matches|where|is))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#selector-innards\" }] }, { \"begin\": \"(?i)((:)nth-(?:last-)?(?:child|of-type))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" }, \"3\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"match\": \"(?i)[+-]?(\\\\d+n?|n)(\\\\s*[+-]\\\\s*\\\\d+)?\", \"name\": \"constant.numeric.css\" }, { \"match\": \"(?i)even|odd\", \"name\": \"support.constant.parity.css\" }] }] }, \"functions\": { \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(calc)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.calc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.calc.css\", \"patterns\": [{ \"match\": \"[*/]|(?<=\\\\s|^)[-+](?=\\\\s|$)\", \"name\": \"keyword.operator.arithmetic.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(rgba?|rgb|hsla?|hsl|hwb|lab|oklab|lch|oklch|color)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.color.css\", \"patterns\": [{ \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])((?:-webkit-|-moz-|-o-)?(?:repeating-)?(?:linear|radial|conic)-gradient)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.gradient.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.gradient.css\", \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(from|to|at|in|hue)(?![\\\\w-])\", \"name\": \"keyword.operator.gradient.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(-webkit-gradient)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated.gradient.function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.gradient.invalid.deprecated.gradient.css\", \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(from|to|color-stop)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated.function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#property-values\" }] }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(annotation|attr|blur|brightness|character-variant|clamp|contrast|counters?|cross-fade|drop-shadow|element|fit-content|format|grayscale|hue-rotate|color-mix|image-set|invert|local|max|min|minmax|opacity|ornaments|repeat|saturate|sepia|styleset|stylistic|swash|symbols|cos|sin|tan|acos|asin|atan|atan2|hypot|sqrt|pow|log|exp|abs|sign)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.misc.css\", \"patterns\": [{ \"match\": `(?i)(?<=[,\\\\s\"]|\\\\*/|^)\\\\d+x(?=[\\\\s,\"')]|/\\\\*|$)`, \"name\": \"constant.numeric.other.density.css\" }, { \"include\": \"#property-values\" }, { \"match\": `[^'\"),\\\\s]+`, \"name\": \"variable.parameter.misc.css\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(circle|ellipse|inset|polygon|rect)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.shape.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.shape.css\", \"patterns\": [{ \"match\": \"(?i)(?<=\\\\s|^|\\\\*/)(at|round)(?=\\\\s|/\\\\*|$)\", \"name\": \"keyword.operator.shape.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(cubic-bezier|steps)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.timing-function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.timing-function.css\", \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(start|end)(?=\\\\s*\\\\)|$)\", \"name\": \"support.constant.step-direction.css\" }, { \"include\": \"#property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])((?:translate|scale|rotate)(?:[XYZ]|3D)?|matrix(?:3D)?|skew[XY]?|perspective)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.transform.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#property-values\" }] }, { \"include\": \"#url\" }, { \"begin\": \"(?i)(?<![\\\\w-])(var)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.variable.css\", \"patterns\": [{ \"match\": \"--(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.argument.css\" }, { \"include\": \"#property-values\" }] }] }, \"media-feature-keywords\": { \"match\": \"(?i)(?<=^|\\\\s|:|\\\\*/)(?:portrait|landscape|progressive|interlace|fullscreen|standalone|minimal-ui|browser|hover)(?=\\\\s|\\\\)|$)\", \"name\": \"support.constant.property-value.css\" }, \"media-features\": { \"captures\": { \"1\": { \"name\": \"support.type.property-name.media.css\" }, \"2\": { \"name\": \"support.type.property-name.media.css\" }, \"3\": { \"name\": \"support.type.vendored.property-name.media.css\" } }, \"match\": \"(?i)(?<=^|\\\\s|\\\\(|\\\\*/)(?:((?:min-|max-)?(?:height|width|aspect-ratio|color|color-index|monochrome|resolution)|grid|scan|orientation|display-mode|hover)|((?:min-|max-)?device-(?:height|width|aspect-ratio))|((?:[-_](?:webkit|apple|khtml|epub|moz|ms|o|xv|ah|rim|atsc|hp|tc|wap|ro)|(?:mso|prince))-[\\\\w-]+(?=\\\\s*(?:/\\\\*(?:[^*]|\\\\*[^/])*\\\\*/)?\\\\s*[:)])))(?=\\\\s|$|[><:=]|\\\\)|/\\\\*)\" }, \"media-query\": { \"begin\": \"\\\\G\", \"end\": \"(?=\\\\s*[{;])\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#media-types\" }, { \"match\": \"(?i)(?<=\\\\s|^|,|\\\\*/)(only|not)(?=\\\\s|{|/\\\\*|$)\", \"name\": \"keyword.operator.logical.$1.media.css\" }, { \"match\": \"(?i)(?<=\\\\s|^|\\\\*/|\\\\))and(?=\\\\s|/\\\\*|$)\", \"name\": \"keyword.operator.logical.and.media.css\" }, { \"match\": \",(?:(?:\\\\s*,)+|(?=\\\\s*[;){]))\", \"name\": \"invalid.illegal.comma.css\" }, { \"include\": \"#commas\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#media-features\" }, { \"include\": \"#media-feature-keywords\" }, { \"match\": \":\", \"name\": \"punctuation.separator.key-value.css\" }, { \"match\": \">=|<=|=|<|>\", \"name\": \"keyword.operator.comparison.css\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.css\" }, \"2\": { \"name\": \"keyword.operator.arithmetic.css\" }, \"3\": { \"name\": \"constant.numeric.css\" } }, \"match\": \"(\\\\d+)\\\\s*(/)\\\\s*(\\\\d+)\", \"name\": \"meta.ratio.css\" }, { \"include\": \"#numeric-values\" }, { \"include\": \"#comment-block\" }] }] }, \"media-query-list\": { \"begin\": \"(?=\\\\s*[^{;])\", \"end\": \"(?=\\\\s*[{;])\", \"patterns\": [{ \"include\": \"#media-query\" }] }, \"media-types\": { \"captures\": { \"1\": { \"name\": \"support.constant.media.css\" }, \"2\": { \"name\": \"invalid.deprecated.constant.media.css\" } }, \"match\": \"(?i)(?<=^|\\\\s|,|\\\\*/)(?:(all|print|screen|speech)|(aural|braille|embossed|handheld|projection|tty|tv))(?=$|[{,\\\\s;]|/\\\\*)\" }, \"numeric-values\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.css\" } }, \"match\": \"(#)(?:[0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\\\b\", \"name\": \"constant.other.color.rgb-value.hex.css\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.unit.percentage.css\" }, \"2\": { \"name\": \"keyword.other.unit.${2:/downcase}.css\" } }, \"match\": \"(?i)(?<![\\\\w-])[-+]?(?:\\\\d+(?:\\\\.\\\\d+)?|\\\\.\\\\d+)(?:(?<=\\\\d)E[-+]?\\\\d+)?(?:(%)|(deg|grad|rad|turn|Hz|kHz|ch|cm|em|ex|fr|in|mm|mozmm|pc|pt|px|q|rem|rch|rex|rlh|ic|ric|rcap|vh|vw|vb|vi|svh|svw|svb|svi|dvh|dvw|dvb|dvi|lvh|lvw|lvb|lvi|vmax|vmin|cqw|cqi|cqh|cqb|cqmin|cqmax|dpi|dpcm|dppx|s|ms)\\\\b)?\", \"name\": \"constant.numeric.css\" }] }, \"property-keywords\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(above|absolute|active|add|additive|after-edge|alias|all|all-petite-caps|all-scroll|all-small-caps|alpha|alphabetic|alternate|alternate-reverse|always|antialiased|auto|auto-fill|auto-fit|auto-pos|available|avoid|avoid-column|avoid-page|avoid-region|backwards|balance|baseline|before-edge|below|bevel|bidi-override|blink|block|block-axis|block-start|block-end|bold|bolder|border|border-box|both|bottom|bottom-outside|break-all|break-word|bullets|butt|capitalize|caption|cell|center|central|char|circle|clip|clone|close-quote|closest-corner|closest-side|col-resize|collapse|color|color-burn|color-dodge|column|column-reverse|common-ligatures|compact|condensed|contain|content|content-box|contents|context-menu|contextual|copy|cover|crisp-edges|crispEdges|crosshair|cyclic|dark|darken|dashed|decimal|default|dense|diagonal-fractions|difference|digits|disabled|disc|discretionary-ligatures|distribute|distribute-all-lines|distribute-letter|distribute-space|dot|dotted|double|double-circle|downleft|downright|e-resize|each-line|ease|ease-in|ease-in-out|ease-out|economy|ellipse|ellipsis|embed|end|evenodd|ew-resize|exact|exclude|exclusion|expanded|extends|extra-condensed|extra-expanded|fallback|farthest-corner|farthest-side|fill|fill-available|fill-box|filled|fit-content|fixed|flat|flex|flex-end|flex-start|flip|flow-root|forwards|freeze|from-image|full-width|geometricPrecision|georgian|grab|grabbing|grayscale|grid|groove|hand|hanging|hard-light|help|hidden|hide|historical-forms|historical-ligatures|horizontal|horizontal-tb|hue|icon|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|ideographic|inactive|infinite|inherit|initial|inline|inline-axis|inline-block|inline-end|inline-flex|inline-grid|inline-list-item|inline-start|inline-table|inset|inside|inter-character|inter-ideograph|inter-word|intersect|invert|isolate|isolate-override|italic|jis04|jis78|jis83|jis90|justify|justify-all|kannada|keep-all|landscape|large|larger|left|light|lighten|lighter|line|line-edge|line-through|linear|linearRGB|lining-nums|list-item|local|loose|lowercase|lr|lr-tb|ltr|luminance|luminosity|main-size|mandatory|manipulation|manual|margin-box|match-parent|match-source|mathematical|max-content|medium|menu|message-box|middle|min-content|miter|mixed|move|multiply|n-resize|narrower|ne-resize|nearest-neighbor|nesw-resize|newspaper|no-change|no-clip|no-close-quote|no-common-ligatures|no-contextual|no-discretionary-ligatures|no-drop|no-historical-ligatures|no-open-quote|no-repeat|none|nonzero|normal|not-allowed|nowrap|ns-resize|numbers|numeric|nw-resize|nwse-resize|oblique|oldstyle-nums|open|open-quote|optimizeLegibility|optimizeQuality|optimizeSpeed|optional|ordinal|outset|outside|over|overlay|overline|padding|padding-box|page|painted|pan-down|pan-left|pan-right|pan-up|pan-x|pan-y|paused|petite-caps|pixelated|plaintext|pointer|portrait|pre|pre-line|pre-wrap|preserve-3d|progress|progressive|proportional-nums|proportional-width|proximity|radial|recto|region|relative|remove|repeat|repeat-[xy]|reset-size|reverse|revert|ridge|right|rl|rl-tb|round|row|row-resize|row-reverse|row-severse|rtl|ruby|ruby-base|ruby-base-container|ruby-text|ruby-text-container|run-in|running|s-resize|saturation|scale-down|screen|scroll|scroll-position|se-resize|semi-condensed|semi-expanded|separate|sesame|show|sideways|sideways-left|sideways-lr|sideways-right|sideways-rl|simplified|slashed-zero|slice|small|small-caps|small-caption|smaller|smooth|soft-light|solid|space|space-around|space-between|space-evenly|spell-out|square|sRGB|stacked-fractions|start|static|status-bar|swap|step-end|step-start|sticky|stretch|strict|stroke|stroke-box|style|sub|subgrid|subpixel-antialiased|subtract|super|sw-resize|symbolic|table|table-caption|table-cell|table-column|table-column-group|table-footer-group|table-header-group|table-row|table-row-group|tabular-nums|tb|tb-rl|text|text-after-edge|text-before-edge|text-bottom|text-top|thick|thin|titling-caps|top|top-outside|touch|traditional|transparent|triangle|ultra-condensed|ultra-expanded|under|underline|unicase|unset|upleft|uppercase|upright|use-glyph-orientation|use-script|verso|vertical|vertical-ideographic|vertical-lr|vertical-rl|vertical-text|view-box|visible|visibleFill|visiblePainted|visibleStroke|w-resize|wait|wavy|weight|whitespace|wider|words|wrap|wrap-reverse|x|x-large|x-small|xx-large|xx-small|y|zero|zoom-in|zoom-out)(?![\\\\w-])\", \"name\": \"support.constant.property-value.css\" }, { \"match\": \"(?i)(?<![\\\\w-])(arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|cjk-ideographic|decimal|decimal-leading-zero|devanagari|disc|disclosure-closed|disclosure-open|ethiopic-halehame-am|ethiopic-halehame-ti-e[rt]|ethiopic-numeric|georgian|gujarati|gurmukhi|hangul|hangul-consonant|hebrew|hiragana|hiragana-iroha|japanese-formal|japanese-informal|kannada|katakana|katakana-iroha|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman|urdu)(?![\\\\w-])\", \"name\": \"support.constant.property-value.list-style-type.css\" }, { \"match\": \"(?<![\\\\w-])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-zA-Z-]+\", \"name\": \"support.constant.vendored.property-value.css\" }, { \"match\": \"(?<![\\\\w-])(?i:arial|century|comic|courier|garamond|georgia|helvetica|impact|lucida|symbol|system-ui|system|tahoma|times|trebuchet|ui-monospace|ui-rounded|ui-sans-serif|ui-serif|utopia|verdana|webdings|sans-serif|serif|monospace)(?![\\\\w-])\", \"name\": \"support.constant.font-name.css\" }] }, \"property-names\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(?:accent-color|additive-symbols|align-content|align-items|align-self|all|animation|animation-delay|animation-direction|animation-duration|animation-fill-mode|animation-iteration-count|animation-name|animation-play-state|animation-timing-function|backdrop-filter|backface-visibility|background|background-attachment|background-blend-mode|background-clip|background-color|background-image|background-origin|background-position|background-position-[xy]|background-repeat|background-size|bleed|block-size|border|border-block-end|border-block-end-color|border-block-end-style|border-block-end-width|border-block-start|border-block-start-color|border-block-start-style|border-block-start-width|border-bottom|border-bottom-color|border-bottom-left-radius|border-bottom-right-radius|border-bottom-style|border-bottom-width|border-collapse|border-color|border-end-end-radius|border-end-start-radius|border-image|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-inline-end|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-start|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-left|border-left-color|border-left-style|border-left-width|border-radius|border-right|border-right-color|border-right-style|border-right-width|border-spacing|border-start-end-radius|border-start-start-radius|border-style|border-top|border-top-color|border-top-left-radius|border-top-right-radius|border-top-style|border-top-width|border-width|bottom|box-decoration-break|box-shadow|box-sizing|break-after|break-before|break-inside|caption-side|caret-color|clear|clip|clip-path|clip-rule|color|color-adjust|color-interpolation-filters|color-scheme|column-count|column-fill|column-gap|column-rule|column-rule-color|column-rule-style|column-rule-width|column-span|column-width|columns|contain|container|container-name|container-type|content|counter-increment|counter-reset|cursor|direction|display|empty-cells|enable-background|fallback|fill|fill-opacity|fill-rule|filter|flex|flex-basis|flex-direction|flex-flow|flex-grow|flex-shrink|flex-wrap|float|flood-color|flood-opacity|font|font-display|font-family|font-feature-settings|font-kerning|font-language-override|font-optical-sizing|font-size|font-size-adjust|font-stretch|font-style|font-synthesis|font-variant|font-variant-alternates|font-variant-caps|font-variant-east-asian|font-variant-ligatures|font-variant-numeric|font-variant-position|font-variation-settings|font-weight|gap|glyph-orientation-horizontal|glyph-orientation-vertical|grid|grid-area|grid-auto-columns|grid-auto-flow|grid-auto-rows|grid-column|grid-column-end|grid-column-gap|grid-column-start|grid-gap|grid-row|grid-row-end|grid-row-gap|grid-row-start|grid-template|grid-template-areas|grid-template-columns|grid-template-rows|hanging-punctuation|height|hyphens|image-orientation|image-rendering|image-resolution|ime-mode|initial-letter|initial-letter-align|inline-size|inset|inset-block|inset-block-end|inset-block-start|inset-inline|inset-inline-end|inset-inline-start|isolation|justify-content|justify-items|justify-self|kerning|left|letter-spacing|lighting-color|line-break|line-clamp|line-height|list-style|list-style-image|list-style-position|list-style-type|margin|margin-block|margin-block-end|margin-block-start|margin-bottom|margin-inline|margin-inline-end|margin-inline-start|margin-left|margin-right|margin-top|marker-end|marker-mid|marker-start|marks|mask|mask-border|mask-border-mode|mask-border-outset|mask-border-repeat|mask-border-slice|mask-border-source|mask-border-width|mask-clip|mask-composite|mask-image|mask-mode|mask-origin|mask-position|mask-repeat|mask-size|mask-type|max-block-size|max-height|max-inline-size|max-lines|max-width|max-zoom|min-block-size|min-height|min-inline-size|min-width|min-zoom|mix-blend-mode|negative|object-fit|object-position|offset|offset-anchor|offset-distance|offset-path|offset-position|offset-rotation|opacity|order|orientation|orphans|outline|outline-color|outline-offset|outline-style|outline-width|overflow|overflow-anchor|overflow-block|overflow-inline|overflow-wrap|overflow-[xy]|overscroll-behavior|overscroll-behavior-block|overscroll-behavior-inline|overscroll-behavior-[xy]|pad|padding|padding-block|padding-block-end|padding-block-start|padding-bottom|padding-inline|padding-inline-end|padding-inline-start|padding-left|padding-right|padding-top|page-break-after|page-break-before|page-break-inside|paint-order|perspective|perspective-origin|place-content|place-items|place-self|pointer-events|position|prefix|quotes|range|resize|right|rotate|row-gap|ruby-align|ruby-merge|ruby-position|scale|scroll-behavior|scroll-margin|scroll-margin-block|scroll-margin-block-end|scroll-margin-block-start|scroll-margin-bottom|scroll-margin-inline|scroll-margin-inline-end|scroll-margin-inline-start|scroll-margin-left|scroll-margin-right|scroll-margin-top|scroll-padding|scroll-padding-block|scroll-padding-block-end|scroll-padding-block-start|scroll-padding-bottom|scroll-padding-inline|scroll-padding-inline-end|scroll-padding-inline-start|scroll-padding-left|scroll-padding-right|scroll-padding-top|scroll-snap-align|scroll-snap-coordinate|scroll-snap-destination|scroll-snap-stop|scroll-snap-type|scrollbar-color|scrollbar-gutter|scrollbar-width|shape-image-threshold|shape-margin|shape-outside|shape-rendering|size|speak-as|src|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|suffix|symbols|system|tab-size|table-layout|text-align|text-align-last|text-anchor|text-combine-upright|text-decoration|text-decoration-color|text-decoration-line|text-decoration-skip|text-decoration-skip-ink|text-decoration-style|text-decoration-thickness|text-emphasis|text-emphasis-color|text-emphasis-position|text-emphasis-style|text-indent|text-justify|text-orientation|text-overflow|text-rendering|text-shadow|text-size-adjust|text-transform|text-underline-offset|text-underline-position|top|touch-action|transform|transform-box|transform-origin|transform-style|transition|transition-delay|transition-duration|transition-property|transition-timing-function|translate|unicode-bidi|unicode-range|user-select|user-zoom|vertical-align|visibility|white-space|widows|width|will-change|word-break|word-spacing|word-wrap|writing-mode|z-index|zoom|alignment-baseline|baseline-shift|clip-rule|color-interpolation|color-interpolation-filters|color-profile|color-rendering|cx|cy|dominant-baseline|enable-background|fill|fill-opacity|fill-rule|flood-color|flood-opacity|glyph-orientation-horizontal|glyph-orientation-vertical|height|kerning|lighting-color|marker-end|marker-mid|marker-start|r|rx|ry|shape-rendering|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|text-anchor|width|x|y|adjust|after|align|align-last|alignment|alignment-adjust|appearance|attachment|azimuth|background-break|balance|baseline|before|bidi|binding|bookmark|bookmark-label|bookmark-level|bookmark-target|border-length|bottom-color|bottom-left-radius|bottom-right-radius|bottom-style|bottom-width|box|box-align|box-direction|box-flex|box-flex-group|box-lines|box-ordinal-group|box-orient|box-pack|break|character|collapse|column|column-break-after|column-break-before|count|counter|crop|cue|cue-after|cue-before|decoration|decoration-break|delay|display-model|display-role|down|drop|drop-initial-after-adjust|drop-initial-after-align|drop-initial-before-adjust|drop-initial-before-align|drop-initial-size|drop-initial-value|duration|elevation|emphasis|family|fit|fit-position|flex-group|float-offset|gap|grid-columns|grid-rows|hanging-punctuation|header|hyphenate|hyphenate-after|hyphenate-before|hyphenate-character|hyphenate-lines|hyphenate-resource|icon|image|increment|indent|index|initial-after-adjust|initial-after-align|initial-before-adjust|initial-before-align|initial-size|initial-value|inline-box-align|iteration-count|justify|label|left-color|left-style|left-width|length|level|line|line-stacking|line-stacking-ruby|line-stacking-shift|line-stacking-strategy|lines|list|mark|mark-after|mark-before|marks|marquee|marquee-direction|marquee-play-count|marquee-speed|marquee-style|max|min|model|move-to|name|nav|nav-down|nav-index|nav-left|nav-right|nav-up|new|numeral|offset|ordinal-group|orient|origin|overflow-style|overhang|pack|page|page-policy|pause|pause-after|pause-before|phonemes|pitch|pitch-range|play-count|play-during|play-state|point|presentation|presentation-level|profile|property|punctuation|punctuation-trim|radius|rate|rendering-intent|repeat|replace|reset|resolution|resource|respond-to|rest|rest-after|rest-before|richness|right-color|right-style|right-width|role|rotation|rotation-point|rows|ruby|ruby-overhang|ruby-span|rule|rule-color|rule-style|rule-width|shadow|size|size-adjust|sizing|space|space-collapse|spacing|span|speak|speak-header|speak-numeral|speak-punctuation|speech|speech-rate|speed|stacking|stacking-ruby|stacking-shift|stacking-strategy|stress|stretch|string-set|style|style-image|style-position|style-type|target|target-name|target-new|target-position|text|text-height|text-justify|text-outline|text-replace|text-wrap|timing-function|top-color|top-left-radius|top-right-radius|top-style|top-width|trim|unicode|up|user-select|variant|voice|voice-balance|voice-duration|voice-family|voice-pitch|voice-pitch-range|voice-rate|voice-stress|voice-volume|volume|weight|white|white-space-collapse|word|wrap)(?![\\\\w-])\", \"name\": \"support.type.property-name.css\" }, { \"match\": \"(?<![\\\\w-])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-zA-Z-]+\", \"name\": \"support.type.vendored.property-name.css\" }] }, \"property-values\": { \"patterns\": [{ \"include\": \"#commas\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#functions\" }, { \"include\": \"#property-keywords\" }, { \"include\": \"#unicode-range\" }, { \"include\": \"#numeric-values\" }, { \"include\": \"#color-keywords\" }, { \"include\": \"#string\" }, { \"match\": \"!\\\\s*important(?![\\\\w-])\", \"name\": \"keyword.other.important.css\" }] }, \"pseudo-classes\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"invalid.illegal.colon.css\" } }, \"match\": \"(?i)(:)(:*)(?:active|any-link|checked|default|disabled|empty|enabled|first|(?:first|last|only)-(?:child|of-type)|focus|focus-visible|focus-within|fullscreen|host|hover|in-range|indeterminate|invalid|left|link|optional|out-of-range|read-only|read-write|required|right|root|scope|target|unresolved|valid|visited)(?![\\\\w-]|\\\\s*[;}])\", \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"pseudo-elements\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(?i)(?:(::?)(?:after|before|first-letter|first-line|(?:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[a-z-]+)|(::)(?:backdrop|content|grammar-error|marker|placeholder|selection|shadow|spelling-error))(?![\\\\w-]|\\\\s*[;}])\", \"name\": \"entity.other.attribute-name.pseudo-element.css\" }, \"rule-list\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.begin.bracket.curly.css\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.property-list.end.bracket.curly.css\" } }, \"name\": \"meta.property-list.css\", \"patterns\": [{ \"include\": \"#rule-list-innards\" }] }, \"rule-list-innards\": { \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }, { \"include\": \"#font-features\" }, { \"match\": \"(?<![\\\\w-])--(?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*\", \"name\": \"variable.css\" }, { \"begin\": \"(?<![-a-zA-Z])(?=[-a-zA-Z])\", \"end\": \"$|(?![-a-zA-Z])\", \"name\": \"meta.property-name.css\", \"patterns\": [{ \"include\": \"#property-names\" }] }, { \"begin\": \"(:)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.key-value.css\" } }, \"contentName\": \"meta.property-value.css\", \"end\": \"\\\\s*(;)|\\\\s*(?=}|\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.rule.css\" } }, \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#property-values\" }] }, { \"match\": \";\", \"name\": \"punctuation.terminator.rule.css\" }] }, \"selector\": { \"begin\": \"(?=(?:\\\\|)?(?:[-\\\\[:.*#a-zA-Z_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.)))\", \"end\": \"(?=\\\\s*[/@{)])\", \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#selector-innards\" }] }, \"selector-innards\": { \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#commas\" }, { \"include\": \"#escapes\" }, { \"include\": \"#combinators\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.namespace-prefix.css\" }, \"2\": { \"name\": \"punctuation.separator.css\" } }, \"match\": \"(?:^|(?<=[\\\\s,(};]))(?![-\\\\w*]+\\\\|(?![-\\\\[:.*#a-zA-Z_]|[^\\\\x00-\\\\x7F]))((?:[-a-zA-Z_]|[^\\\\x00-\\\\x7F])(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*|\\\\*)?(\\\\|)\" }, { \"include\": \"#tag-names\" }, { \"match\": \"\\\\*\", \"name\": \"entity.name.tag.wildcard.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(?<![@\\\\w-])([.#])((?:-?\\\\d|-(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)|(?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*(?:[!\\\"'%&(*;<?@^`|\\\\]}]|/(?!\\\\*))+)(?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))*)\", \"name\": \"invalid.illegal.bad-identifier.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(\\\\.)((?:[-a-zA-Z_0-9]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)\", \"name\": \"entity.other.attribute-name.class.css\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(\\\\#)(-?(?!\\\\d)(?:[-a-zA-Z0-9_]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)(?=$|[\\\\s,.#)\\\\[:{>+~|]|/\\\\*)\", \"name\": \"entity.other.attribute-name.id.css\" }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.begin.bracket.square.css\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.end.bracket.square.css\" } }, \"name\": \"meta.attribute-selector.css\", \"patterns\": [{ \"include\": \"#comment-block\" }, { \"include\": \"#string\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.ignore-case.css\" } }, \"match\": `(?<=[\"'\\\\s]|^|\\\\*/)\\\\s*([iI])\\\\s*(?=[\\\\s\\\\]]|/\\\\*|$)` }, { \"captures\": { \"1\": { \"name\": \"string.unquoted.attribute-value.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": `(?<==)\\\\s*((?!/\\\\*)(?:[^\\\\\\\\\"'\\\\s\\\\]]|\\\\\\\\.)+)` }, { \"include\": \"#escapes\" }, { \"match\": \"[~|^$*]?=\", \"name\": \"keyword.operator.pattern.css\" }, { \"match\": \"\\\\|\", \"name\": \"punctuation.separator.css\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.namespace-prefix.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(-?(?!\\\\d)(?:[\\\\w-]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+|\\\\*)(?=\\\\|(?!\\\\s|=|$|\\\\])(?:-?(?!\\\\d)|[\\\\\\\\\\\\w-]|[^\\\\x00-\\\\x7F]))\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.css\", \"patterns\": [{ \"include\": \"#escapes\" }] } }, \"match\": \"(-?(?!\\\\d)(?>[\\\\w-]|[^\\\\x00-\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)\\\\s*(?=[~|^\\\\]$*=]|/\\\\*)\" }] }, { \"include\": \"#pseudo-classes\" }, { \"include\": \"#pseudo-elements\" }, { \"include\": \"#functional-pseudo-classes\" }, { \"match\": \"(?<![@\\\\w-])(?=[a-z]\\\\w*-)(?:(?![A-Z])[\\\\w-])+(?![(\\\\w-])\", \"name\": \"entity.name.tag.custom.css\" }] }, \"string\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": '\"|(?<!\\\\\\\\)(?=$|\\\\n)', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.double.css\", \"patterns\": [{ \"begin\": '(?:\\\\G|^)(?=(?:[^\\\\\\\\\"]|\\\\\\\\.)+$)', \"end\": \"$\", \"name\": \"invalid.illegal.unclosed.string.css\", \"patterns\": [{ \"include\": \"#escapes\" }] }, { \"include\": \"#escapes\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.css\" } }, \"end\": \"'|(?<!\\\\\\\\)(?=$|\\\\n)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.css\" } }, \"name\": \"string.quoted.single.css\", \"patterns\": [{ \"begin\": \"(?:\\\\G|^)(?=(?:[^\\\\\\\\']|\\\\\\\\.)+$)\", \"end\": \"$\", \"name\": \"invalid.illegal.unclosed.string.css\", \"patterns\": [{ \"include\": \"#escapes\" }] }, { \"include\": \"#escapes\" }] }] }, \"tag-names\": { \"match\": \"(?i)(?<![\\\\w:-])(?:a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdi|bdo|bgsound|big|blink|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|command|content|data|datalist|dd|del|details|dfn|dialog|dir|div|dl|dt|element|em|embed|fieldset|figcaption|figure|font|footer|form|frame|frameset|h[1-6]|head|header|hgroup|hr|html|i|iframe|image|img|input|ins|isindex|kbd|keygen|label|legend|li|link|listing|main|map|mark|marquee|math|menu|menuitem|meta|meter|multicol|nav|nextid|nobr|noembed|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|plaintext|pre|progress|q|rb|rp|rt|rtc|ruby|s|samp|script|section|select|shadow|slot|small|source|spacer|span|strike|strong|style|sub|summary|sup|table|tbody|td|template|textarea|tfoot|th|thead|time|title|tr|track|tt|u|ul|var|video|wbr|xmp|altGlyph|altGlyphDef|altGlyphItem|animate|animateColor|animateMotion|animateTransform|circle|clipPath|color-profile|cursor|defs|desc|discard|ellipse|feBlend|feColorMatrix|feComponentTransfer|feComposite|feConvolveMatrix|feDiffuseLighting|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feImage|feMerge|feMergeNode|feMorphology|feOffset|fePointLight|feSpecularLighting|feSpotLight|feTile|feTurbulence|filter|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|foreignObject|g|glyph|glyphRef|hatch|hatchpath|hkern|line|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|metadata|missing-glyph|mpath|path|pattern|polygon|polyline|radialGradient|rect|set|solidcolor|stop|svg|switch|symbol|text|textPath|tref|tspan|use|view|vkern|annotation|annotation-xml|maction|maligngroup|malignmark|math|menclose|merror|mfenced|mfrac|mglyph|mi|mlabeledtr|mlongdiv|mmultiscripts|mn|mo|mover|mpadded|mphantom|mroot|mrow|ms|mscarries|mscarry|msgroup|msline|mspace|msqrt|msrow|mstack|mstyle|msub|msubsup|msup|mtable|mtd|mtext|mtr|munder|munderover|semantics)(?=[+~>\\\\s,.#|){:\\\\[]|/\\\\*|$)\", \"name\": \"entity.name.tag.css\" }, \"unicode-range\": { \"captures\": { \"0\": { \"name\": \"constant.other.unicode-range.css\" }, \"1\": { \"name\": \"punctuation.separator.dash.unicode-range.css\" } }, \"match\": \"(?<![\\\\w-])[Uu]\\\\+[0-9A-Fa-f?]{1,6}(?:(-)[0-9A-Fa-f]{1,6})?(?![\\\\w-])\" }, \"url\": { \"begin\": \"(?i)(?<![\\\\w@-])(url)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.url.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.url.css\", \"patterns\": [{ \"match\": `[^'\")\\\\s]+`, \"name\": \"variable.parameter.url.css\" }, { \"include\": \"#string\" }, { \"include\": \"#comment-block\" }, { \"include\": \"#escapes\" }] } }, \"scopeName\": \"source.css\" });\nvar css = [\n  lang\n];\n\nexport { css as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmD,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAiD;gCAAG,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAyC;gCAAG,KAAK;oCAAE,QAAQ;gCAAgD;gCAAG,KAAK;oCAAE,QAAQ;gCAA8C;gCAAG,KAAK;oCAAE,QAAQ;gCAAoD;4BAAE;4BAAG,SAAS;wBAAuI;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,SAAS;wBAAsB;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,SAAS;oCAA2B,OAAO;oCAAK,QAAQ;gCAAsC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,uCAAuC,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAAmB,OAAO;4BAAiB,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAW,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAqB,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkC,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAmB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;4BAAiC,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkD;4BAAE;4BAAG,QAAQ;4BAA+B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,wCAAwC,CAAC;oBAAE,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAA2B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAa,QAAQ;4BAAyC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;oCAAE;oCAAG,SAAS;oCAA6F,QAAQ;gCAAoC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA4D;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA0D;4BAAE;4BAAG,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,mCAAmC,CAAC;oBAAE,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,SAAS;oCAAkD,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAqC;wCAAG,KAAK;4CAAE,QAAQ;wCAAuD;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAqD;oCAAE;oCAAG,QAAQ;oCAAmC,YAAY;wCAAC;4CAAE,WAAW;wCAAU;wCAAG;4CAAE,WAAW;wCAAiB;wCAAG;4CAAE,WAAW;wCAAW;wCAAG;4CAAE,SAAS,CAAC,UAAU,CAAC;4CAAE,QAAQ;wCAAuC;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAO;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,8DAA8D,CAAC;oBAAE,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAiD,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAa,QAAQ;4BAAqC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,YAAY;gDAAC;oDAAE,WAAW;gDAAW;6CAAE;wCAAC;oCAAE;oCAAG,SAAS;oCAA6F,QAAQ;gCAAuC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsD;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;wCAAG,KAAK;4CAAE,QAAQ;wCAA8C;oCAAE;oCAAG,SAAS;gCAA0E;gCAAG;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqC,OAAO;oBAAuB,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,WAAW;gCAA2B;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,gDAAgD,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAiB,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,oDAAoD,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,eAAe;oBAAoC,OAAO;oBAAiB,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS,CAAC,qCAAqC,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAc,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAiB;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;oCAA6C,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAAkM;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6B,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAqB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;4BAAE;4BAAG,QAAQ;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsC,OAAO;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAqB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAgB,QAAQ;wBAA0B;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA8C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;4BAAE;4BAAG,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmI,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAm4C,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAyC,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAA6X,QAAQ;gBAAsC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAkC;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,QAAQ;QAAuC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;QAAoB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwB,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAa,OAAO;oBAAa,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAgC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyD;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyD,QAAQ;gBAA0C;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS,CAAC,+FAA+F,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;4BAA6F,QAAQ;wBAA4B;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAsC,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA+E,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,SAAS;oCAAoD,QAAQ;gCAAsC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,SAAS;oCAAoD,QAAQ;gCAAsC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA0C,QAAQ;wBAAuB;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS;4BAAgC,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2E,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAgD,QAAQ;wBAAgC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA0D,YAAY;wBAAC;4BAAE,SAAS;4BAA4C,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,SAAS,CAAC,gDAAgD,CAAC;4BAAE,QAAQ;wBAAqC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,SAAS,CAAC,WAAW,CAAC;4BAAE,QAAQ;wBAA8B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAA+C,QAAQ;wBAA6B;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,SAAS;4BAA2C,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,SAAS;oBAA6B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAA+F,QAAQ;wBAAwB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,0BAA0B;YAAE,SAAS;YAAiI,QAAQ;QAAsC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgD;YAAE;YAAG,SAAS;QAA0X;QAAG,eAAe;YAAE,SAAS;YAAO,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,SAAS;oBAAmD,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAkC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;4BAA2B,QAAQ;wBAAiB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAiB,OAAO;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,SAAS;QAA4H;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAA4D,QAAQ;gBAAyC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;oBAAwS,QAAQ;gBAAuB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqzI,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAixB,QAAQ;gBAAsD;gBAAG;oBAAE,SAAS;oBAA+G,QAAQ;gBAA+C;gBAAG;oBAAE,SAAS;oBAAmP,QAAQ;gBAAiC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAw3S,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA+G,QAAQ;gBAA0C;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA8B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;YAA6U,QAAQ;QAA+C;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;YAAmQ,QAAQ;QAAiD;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4D;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0D;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAA0G,QAAQ;gBAAe;gBAAG;oBAAE,SAAS;oBAA+B,OAAO;oBAAmB,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAA2B,OAAO;oBAAyB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAkC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA+E,OAAO;YAAkB,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAA+K;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAA4N,QAAQ;gBAAqC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAoG,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAA6G,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS,CAAC,oDAAoD,CAAC;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAAuC,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS,CAAC,8CAA8C,CAAC;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAAa,QAAQ;wBAA+B;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAA4B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAAoI;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;oCAAmC,YAAY;wCAAC;4CAAE,WAAW;wCAAW;qCAAE;gCAAC;4BAAE;4BAAG,SAAS;wBAA2F;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAA6B;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAwB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,SAAS;4BAAqC,OAAO;4BAAK,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,OAAO;oBAAwB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,SAAS;4BAAqC,OAAO;4BAAK,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAW;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAo8D,QAAQ;QAAsB;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,SAAS;QAAwE;QAAG,OAAO;YAAE,SAAS;YAA8B,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAuD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,SAAS,CAAC,UAAU,CAAC;oBAAE,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;IAAE;IAAG,aAAa;AAAa;AACvwjD,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/html.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport css from './css.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"HTML\", \"injections\": { \"R:text.html - (comment.block, text.html meta.embedded, meta.tag.*.*.html, meta.tag.*.*.*.html, meta.tag.*.*.*.*.html)\": { \"comment\": \"Uses R: to ensure this matches after any other injections.\", \"patterns\": [{ \"match\": \"<\", \"name\": \"invalid.illegal.bad-angle-bracket.html\" }] } }, \"name\": \"html\", \"patterns\": [{ \"include\": \"#xml-processing\" }, { \"include\": \"#comment\" }, { \"include\": \"#doctype\" }, { \"include\": \"#cdata\" }, { \"include\": \"#tags-valid\" }, { \"include\": \"#tags-invalid\" }, { \"include\": \"#entities\" }], \"repository\": { \"attribute\": { \"patterns\": [{ \"begin\": \"(s(hape|cope|t(ep|art)|ize(s)?|p(ellcheck|an)|elected|lot|andbox|rc(set|doc|lang)?)|h(ttp-equiv|i(dden|gh)|e(ight|aders)|ref(lang)?)|n(o(nce|validate|module)|ame)|c(h(ecked|arset)|ite|o(nt(ent(editable)?|rols)|ords|l(s(pan)?|or))|lass|rossorigin)|t(ype(mustmatch)?|itle|a(rget|bindex)|ranslate)|i(s(map)?|n(tegrity|putmode)|tem(scope|type|id|prop|ref)|d)|op(timum|en)|d(i(sabled|r(name)?)|ownload|e(coding|f(er|ault))|at(etime|a)|raggable)|usemap|p(ing|oster|la(ysinline|ceholder)|attern|reload)|enctype|value|kind|for(m(novalidate|target|enctype|action|method)?)?|w(idth|rap)|l(ist|o(op|w)|a(ng|bel))|a(s(ync)?|c(ce(sskey|pt(-charset)?)|tion)|uto(c(omplete|apitalize)|play|focus)|l(t|low(usermedia|paymentrequest|fullscreen))|bbr)|r(ows(pan)?|e(versed|quired|ferrerpolicy|l|adonly))|m(in(length)?|u(ted|ltiple)|e(thod|dia)|a(nifest|x(length)?)))(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"HTML5 attributes, not event handlers\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"begin\": \"style(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"HTML5 style attribute\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.style.html\", \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.html\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"begin\": \"(?=[^\\\\s=<>`/]|/(?!>))\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.line.css\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"source.css\" } }, \"match\": \"([^\\\\s\\\"'=<>`/]|/(?!>))+\", \"name\": \"string.unquoted.html\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"source.css\", \"end\": '(\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" }, \"1\": { \"name\": \"source.css\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"include\": \"#entities\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"source.css\", \"end\": \"(')\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" }, \"1\": { \"name\": \"source.css\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"include\": \"#entities\" }] }] }, { \"match\": \"=\", \"name\": \"invalid.illegal.unexpected-equals-sign.html\" }] }] }, { \"begin\": \"on(s(croll|t(orage|alled)|u(spend|bmit)|e(curitypolicyviolation|ek(ing|ed)|lect))|hashchange|c(hange|o(ntextmenu|py)|u(t|echange)|l(ick|ose)|an(cel|play(through)?))|t(imeupdate|oggle)|in(put|valid)|o(nline|ffline)|d(urationchange|r(op|ag(start|over|e(n(ter|d)|xit)|leave)?)|blclick)|un(handledrejection|load)|p(opstate|lay(ing)?|a(ste|use|ge(show|hide))|rogress)|e(nded|rror|mptied)|volumechange|key(down|up|press)|focus|w(heel|aiting)|l(oad(start|e(nd|d(data|metadata)))?|anguagechange)|a(uxclick|fterprint|bort)|r(e(s(ize|et)|jectionhandled)|atechange)|m(ouse(o(ut|ver)|down|up|enter|leave|move)|essage(error)?)|b(efore(unload|print)|lur))(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"HTML5 attributes, event handlers\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.event-handler.$1.html\", \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.html\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"begin\": \"(?=[^\\\\s=<>`/]|/(?!>))\", \"end\": \"(?!\\\\G)\", \"name\": \"meta.embedded.line.js\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"source.js\" }, \"1\": { \"patterns\": [{ \"include\": \"source.js\" }] } }, \"match\": \"(([^\\\\s\\\"'=<>`/]|/(?!>))+)\", \"name\": \"string.unquoted.html\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"source.js\", \"end\": '(\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" }, \"1\": { \"name\": \"source.js\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"source.js\" }] } }, \"match\": '([^\\\\n\"/]|/(?![/*]))+' }, { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": '(?=\")|\\\\n', \"name\": \"comment.line.double-slash.js\" }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.js\" } }, \"end\": '(?=\")|\\\\*/', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.js\" } }, \"name\": \"comment.block.js\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"contentName\": \"source.js\", \"end\": \"(')\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" }, \"1\": { \"name\": \"source.js\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"source.js\" }] } }, \"match\": \"([^\\\\n'/]|/(?![/*]))+\" }, { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"(?=')|\\\\n\", \"name\": \"comment.line.double-slash.js\" }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.js\" } }, \"end\": \"(?=')|\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.js\" } }, \"name\": \"comment.block.js\" }] }] }, { \"match\": \"=\", \"name\": \"invalid.illegal.unexpected-equals-sign.html\" }] }] }, { \"begin\": \"(data-[a-z\\\\-]+)(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"HTML5 attributes, data-*\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.data-x.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"begin\": \"(align|bgcolor|border)(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"invalid.deprecated.entity.other.attribute-name.html\" } }, \"comment\": \"HTML attributes, deprecated\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"begin\": `([^\\\\x{0020}\"'<>/=\\\\x{0000}-\\\\x{001F}\\\\x{007F}-\\\\x{009F}\\\\x{FDD0}-\\\\x{FDEF}\\\\x{FFFE}\\\\x{FFFF}\\\\x{1FFFE}\\\\x{1FFFF}\\\\x{2FFFE}\\\\x{2FFFF}\\\\x{3FFFE}\\\\x{3FFFF}\\\\x{4FFFE}\\\\x{4FFFF}\\\\x{5FFFE}\\\\x{5FFFF}\\\\x{6FFFE}\\\\x{6FFFF}\\\\x{7FFFE}\\\\x{7FFFF}\\\\x{8FFFE}\\\\x{8FFFF}\\\\x{9FFFE}\\\\x{9FFFF}\\\\x{AFFFE}\\\\x{AFFFF}\\\\x{BFFFE}\\\\x{BFFFF}\\\\x{CFFFE}\\\\x{CFFFF}\\\\x{DFFFE}\\\\x{DFFFF}\\\\x{EFFFE}\\\\x{EFFFF}\\\\x{FFFFE}\\\\x{FFFFF}\\\\x{10FFFE}\\\\x{10FFFF}]+)`, \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"Anything else that is valid\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.unrecognized.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"match\": \"[^\\\\s>]+\", \"name\": \"invalid.illegal.character-not-allowed-here.html\" }] }, \"attribute-interior\": { \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.html\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"match\": \"([^\\\\s\\\"'=<>`/]|/(?!>))+\", \"name\": \"string.unquoted.html\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.double.html\", \"patterns\": [{ \"include\": \"#entities\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.html\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.html\" } }, \"name\": \"string.quoted.single.html\", \"patterns\": [{ \"include\": \"#entities\" }] }, { \"match\": \"=\", \"name\": \"invalid.illegal.unexpected-equals-sign.html\" }] }] }, \"cdata\": { \"begin\": \"<!\\\\[CDATA\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.begin.html\" } }, \"contentName\": \"string.other.inline-data.html\", \"end\": \"]]>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.cdata.html\" }, \"comment\": { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.html\" } }, \"end\": \"-->\", \"name\": \"comment.block.html\", \"patterns\": [{ \"match\": \"\\\\G-?>\", \"name\": \"invalid.illegal.characters-not-allowed-here.html\" }, { \"match\": \"<!--(?!>)|<!-(?=-->)\", \"name\": \"invalid.illegal.characters-not-allowed-here.html\" }, { \"match\": \"--!>\", \"name\": \"invalid.illegal.characters-not-allowed-here.html\" }] }, \"core-minus-invalid\": { \"comment\": \"This should be the root pattern array includes minus #tags-invalid\", \"patterns\": [{ \"include\": \"#xml-processing\" }, { \"include\": \"#comment\" }, { \"include\": \"#doctype\" }, { \"include\": \"#cdata\" }, { \"include\": \"#tags-valid\" }, { \"include\": \"#entities\" }] }, \"doctype\": { \"begin\": \"<!(?=(?i:DOCTYPE\\\\s))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.begin.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.doctype.html\", \"patterns\": [{ \"match\": \"\\\\G(?i:DOCTYPE)\", \"name\": \"entity.name.tag.html\" }, { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.html\" }, { \"match\": \"[^\\\\s>]+\", \"name\": \"entity.other.attribute-name.html\" }] }, \"entities\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.html\" }, \"912\": { \"name\": \"punctuation.definition.entity.html\" } }, \"comment\": \"Yes this is a bit ridiculous, there are quite a lot of these\", \"match\": \"(&)(?=[a-zA-Z])((a(s(ymp(eq)?|cr|t)|n(d(slope|d|v|and)?|g(s(t|ph)|zarr|e|le|rt(vb(d)?)?|msd(a(h|c|d|e|f|a|g|b))?)?)|c(y|irc|d|ute|E)?|tilde|o(pf|gon)|uml|p(id|os|prox(eq)?|e|E|acir)?|elig|f(r)?|w(conint|int)|l(pha|e(ph|fsym))|acute|ring|grave|m(p|a(cr|lg))|breve)|A(s(sign|cr)|nd|MP|c(y|irc)|tilde|o(pf|gon)|uml|pplyFunction|fr|Elig|lpha|acute|ring|grave|macr|breve))|(B(scr|cy|opf|umpeq|e(cause|ta|rnoullis)|fr|a(ckslash|r(v|wed))|reve)|b(s(cr|im(e)?|ol(hsub|b)?|emi)|n(ot|e(quiv)?)|c(y|ong)|ig(s(tar|qcup)|c(irc|up|ap)|triangle(down|up)|o(times|dot|plus)|uplus|vee|wedge)|o(t(tom)?|pf|wtie|x(h(d|u|D|U)?|times|H(d|u|D|U)?|d(R|l|r|L)|u(R|l|r|L)|plus|D(R|l|r|L)|v(R|h|H|l|r|L)?|U(R|l|r|L)|V(R|h|H|l|r|L)?|minus|box))|Not|dquo|u(ll(et)?|mp(e(q)?|E)?)|prime|e(caus(e)?|t(h|ween|a)|psi|rnou|mptyv)|karow|fr|l(ock|k(1(2|4)|34)|a(nk|ck(square|triangle(down|left|right)?|lozenge)))|a(ck(sim(eq)?|cong|prime|epsilon)|r(vee|wed(ge)?))|r(eve|vbar)|brk(tbrk)?))|(c(s(cr|u(p(e)?|b(e)?))|h(cy|i|eck(mark)?)|ylcty|c(irc|ups(sm)?|edil|a(ps|ron))|tdot|ir(scir|c(eq|le(d(R|circ|S|dash|ast)|arrow(left|right)))?|e|fnint|E|mid)?|o(n(int|g(dot)?)|p(y(sr)?|f|rod)|lon(e(q)?)?|m(p(fn|le(xes|ment))?|ma(t)?))|dot|u(darr(l|r)|p(s|c(up|ap)|or|dot|brcap)?|e(sc|pr)|vee|wed|larr(p)?|r(vearrow(left|right)|ly(eq(succ|prec)|vee|wedge)|arr(m)?|ren))|e(nt(erdot)?|dil|mptyv)|fr|w(conint|int)|lubs(uit)?|a(cute|p(s|c(up|ap)|dot|and|brcup)?|r(on|et))|r(oss|arr))|C(scr|hi|c(irc|onint|edil|aron)|ircle(Minus|Times|Dot|Plus)|Hcy|o(n(tourIntegral|int|gruent)|unterClockwiseContourIntegral|p(f|roduct)|lon(e)?)|dot|up(Cap)?|OPY|e(nterDot|dilla)|fr|lo(seCurly(DoubleQuote|Quote)|ckwiseContourIntegral)|a(yleys|cute|p(italDifferentialD)?)|ross))|(d(s(c(y|r)|trok|ol)|har(l|r)|c(y|aron)|t(dot|ri(f)?)|i(sin|e|v(ide(ontimes)?|onx)?|am(s|ond(suit)?)?|gamma)|Har|z(cy|igrarr)|o(t(square|plus|eq(dot)?|minus)?|ublebarwedge|pf|wn(harpoon(left|right)|downarrows|arrow)|llar)|d(otseq|a(rr|gger))?|u(har|arr)|jcy|e(lta|g|mptyv)|f(isht|r)|wangle|lc(orn|rop)|a(sh(v)?|leth|rr|gger)|r(c(orn|rop)|bkarow)|b(karow|lac)|Arr)|D(s(cr|trok)|c(y|aron)|Scy|i(fferentialD|a(critical(Grave|Tilde|Do(t|ubleAcute)|Acute)|mond))|o(t(Dot|Equal)?|uble(Right(Tee|Arrow)|ContourIntegral|Do(t|wnArrow)|Up(DownArrow|Arrow)|VerticalBar|L(ong(RightArrow|Left(RightArrow|Arrow))|eft(RightArrow|Tee|Arrow)))|pf|wn(Right(TeeVector|Vector(Bar)?)|Breve|Tee(Arrow)?|arrow|Left(RightVector|TeeVector|Vector(Bar)?)|Arrow(Bar|UpArrow)?))|Zcy|el(ta)?|D(otrahd)?|Jcy|fr|a(shv|rr|gger)))|(e(s(cr|im|dot)|n(sp|g)|c(y|ir(c)?|olon|aron)|t(h|a)|o(pf|gon)|dot|u(ro|ml)|p(si(v|lon)?|lus|ar(sl)?)|e|D(ot|Dot)|q(s(im|lant(less|gtr))|c(irc|olon)|u(iv(DD)?|est|als)|vparsl)|f(Dot|r)|l(s(dot)?|inters|l)?|a(ster|cute)|r(Dot|arr)|g(s(dot)?|rave)?|x(cl|ist|p(onentiale|ectation))|m(sp(1(3|4))?|pty(set|v)?|acr))|E(s(cr|im)|c(y|irc|aron)|ta|o(pf|gon)|NG|dot|uml|TH|psilon|qu(ilibrium|al(Tilde)?)|fr|lement|acute|grave|x(ists|ponentialE)|m(pty(SmallSquare|VerySmallSquare)|acr)))|(f(scr|nof|cy|ilig|o(pf|r(k(v)?|all))|jlig|partint|emale|f(ilig|l(ig|lig)|r)|l(tns|lig|at)|allingdotseq|r(own|a(sl|c(1(2|8|3|4|5|6)|78|2(3|5)|3(8|4|5)|45|5(8|6)))))|F(scr|cy|illed(SmallSquare|VerySmallSquare)|o(uriertrf|pf|rAll)|fr))|(G(scr|c(y|irc|edil)|t|opf|dot|T|Jcy|fr|amma(d)?|reater(Greater|SlantEqual|Tilde|Equal(Less)?|FullEqual|Less)|g|breve)|g(s(cr|im(e|l)?)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|irc)|t(c(c|ir)|dot|quest|lPar|r(sim|dot|eq(qless|less)|less|a(pprox|rr)))?|imel|opf|dot|jcy|e(s(cc|dot(o(l)?)?|l(es)?)?|q(slant|q)?|l)?|v(nE|ertneqq)|fr|E(l)?|l(j|E|a)?|a(cute|p|mma(d)?)|rave|g(g)?|breve))|(h(s(cr|trok|lash)|y(phen|bull)|circ|o(ok(leftarrow|rightarrow)|pf|arr|rbar|mtht)|e(llip|arts(uit)?|rcon)|ks(earow|warow)|fr|a(irsp|lf|r(dcy|r(cir|w)?)|milt)|bar|Arr)|H(s(cr|trok)|circ|ilbertSpace|o(pf|rizontalLine)|ump(DownHump|Equal)|fr|a(cek|t)|ARDcy))|(i(s(cr|in(s(v)?|dot|v|E)?)|n(care|t(cal|prod|e(rcal|gers)|larhk)?|odot|fin(tie)?)?|c(y|irc)?|t(ilde)?|i(nfin|i(nt|int)|ota)?|o(cy|ta|pf|gon)|u(kcy|ml)|jlig|prod|e(cy|xcl)|quest|f(f|r)|acute|grave|m(of|ped|a(cr|th|g(part|e|line))))|I(scr|n(t(e(rsection|gral))?|visible(Comma|Times))|c(y|irc)|tilde|o(ta|pf|gon)|dot|u(kcy|ml)|Ocy|Jlig|fr|Ecy|acute|grave|m(plies|a(cr|ginaryI))?))|(j(s(cr|ercy)|c(y|irc)|opf|ukcy|fr|math)|J(s(cr|ercy)|c(y|irc)|opf|ukcy|fr))|(k(scr|hcy|c(y|edil)|opf|jcy|fr|appa(v)?|green)|K(scr|c(y|edil)|Hcy|opf|Jcy|fr|appa))|(l(s(h|cr|trok|im(e|g)?|q(uo(r)?|b)|aquo)|h(ar(d|u(l)?)|blk)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|ub|e(il|dil)|aron)|Barr|t(hree|c(c|ir)|imes|dot|quest|larr|r(i(e|f)?|Par))?|Har|o(ng(left(arrow|rightarrow)|rightarrow|mapsto)|times|z(enge|f)?|oparrow(left|right)|p(f|lus|ar)|w(ast|bar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|r(dhar|ushar))|ur(dshar|uhar)|jcy|par(lt)?|e(s(s(sim|dot|eq(qgtr|gtr)|approx|gtr)|cc|dot(o(r)?)?|g(es)?)?|q(slant|q)?|ft(harpoon(down|up)|threetimes|leftarrows|arrow(tail)?|right(squigarrow|harpoons|arrow(s)?))|g)?|v(nE|ertneqq)|f(isht|loor|r)|E(g)?|l(hard|corner|tri|arr)?|a(ng(d|le)?|cute|t(e(s)?|ail)?|p|emptyv|quo|rr(sim|hk|tl|pl|fs|lp|b(fs)?)?|gran|mbda)|r(har(d)?|corner|tri|arr|m)|g(E)?|m(idot|oust(ache)?)|b(arr|r(k(sl(d|u)|e)|ac(e|k))|brk)|A(tail|arr|rr))|L(s(h|cr|trok)|c(y|edil|aron)|t|o(ng(RightArrow|left(arrow|rightarrow)|rightarrow|Left(RightArrow|Arrow))|pf|wer(RightArrow|LeftArrow))|T|e(ss(Greater|SlantEqual|Tilde|EqualGreater|FullEqual|Less)|ft(Right(Vector|Arrow)|Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|rightarrow|Floor|A(ngleBracket|rrow(RightArrow|Bar)?)))|Jcy|fr|l(eftarrow)?|a(ng|cute|placetrf|rr|mbda)|midot))|(M(scr|cy|inusPlus|opf|u|e(diumSpace|llintrf)|fr|ap)|m(s(cr|tpos)|ho|nplus|c(y|omma)|i(nus(d(u)?|b)?|cro|d(cir|dot|ast)?)|o(dels|pf)|dash|u(ltimap|map)?|p|easuredangle|DDot|fr|l(cp|dr)|a(cr|p(sto(down|up|left)?)?|l(t(ese)?|e)|rker)))|(n(s(hort(parallel|mid)|c(cue|e|r)?|im(e(q)?)?|u(cc(eq)?|p(set(eq(q)?)?|e|E)?|b(set(eq(q)?)?|e|E)?)|par|qsu(pe|be)|mid)|Rightarrow|h(par|arr|Arr)|G(t(v)?|g)|c(y|ong(dot)?|up|edil|a(p|ron))|t(ilde|lg|riangle(left(eq)?|right(eq)?)|gl)|i(s(d)?|v)?|o(t(ni(v(c|a|b))?|in(dot|v(c|a|b)|E)?)?|pf)|dash|u(m(sp|ero)?)?|jcy|p(olint|ar(sl|t|allel)?|r(cue|e(c(eq)?)?)?)|e(s(im|ear)|dot|quiv|ar(hk|r(ow)?)|xist(s)?|Arr)?|v(sim|infin|Harr|dash|Dash|l(t(rie)?|e|Arr)|ap|r(trie|Arr)|g(t|e))|fr|w(near|ar(hk|r(ow)?)|Arr)|V(dash|Dash)|l(sim|t(ri(e)?)?|dr|e(s(s)?|q(slant|q)?|ft(arrow|rightarrow))?|E|arr|Arr)|a(ng|cute|tur(al(s)?)?|p(id|os|prox|E)?|bla)|r(tri(e)?|ightarrow|arr(c|w)?|Arr)|g(sim|t(r)?|e(s|q(slant|q)?)?|E)|mid|L(t(v)?|eft(arrow|rightarrow)|l)|b(sp|ump(e)?))|N(scr|c(y|edil|aron)|tilde|o(nBreakingSpace|Break|t(R(ightTriangle(Bar|Equal)?|everseElement)|Greater(Greater|SlantEqual|Tilde|Equal|FullEqual|Less)?|S(u(cceeds(SlantEqual|Tilde|Equal)?|perset(Equal)?|bset(Equal)?)|quareSu(perset(Equal)?|bset(Equal)?))|Hump(DownHump|Equal)|Nested(GreaterGreater|LessLess)|C(ongruent|upCap)|Tilde(Tilde|Equal|FullEqual)?|DoubleVerticalBar|Precedes(SlantEqual|Equal)?|E(qual(Tilde)?|lement|xists)|VerticalBar|Le(ss(Greater|SlantEqual|Tilde|Equal|Less)?|ftTriangle(Bar|Equal)?))?|pf)|u|e(sted(GreaterGreater|LessLess)|wLine|gative(MediumSpace|Thi(nSpace|ckSpace)|VeryThinSpace))|Jcy|fr|acute))|(o(s(cr|ol|lash)|h(m|bar)|c(y|ir(c)?)|ti(lde|mes(as)?)|S|int|opf|d(sold|iv|ot|ash|blac)|uml|p(erp|lus|ar)|elig|vbar|f(cir|r)|l(c(ir|ross)|t|ine|arr)|a(st|cute)|r(slope|igof|or|d(er(of)?|f|m)?|v|arr)?|g(t|on|rave)|m(i(nus|cron|d)|ega|acr))|O(s(cr|lash)|c(y|irc)|ti(lde|mes)|opf|dblac|uml|penCurly(DoubleQuote|Quote)|ver(B(ar|rac(e|ket))|Parenthesis)|fr|Elig|acute|r|grave|m(icron|ega|acr)))|(p(s(cr|i)|h(i(v)?|one|mmat)|cy|i(tchfork|v)?|o(intint|und|pf)|uncsp|er(cnt|tenk|iod|p|mil)|fr|l(us(sim|cir|two|d(o|u)|e|acir|mn|b)?|an(ck(h)?|kv))|ar(s(im|l)|t|a(llel)?)?|r(sim|n(sim|E|ap)|cue|ime(s)?|o(d|p(to)?|f(surf|line|alar))|urel|e(c(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?)?|E|ap)?|m)|P(s(cr|i)|hi|cy|i|o(incareplane|pf)|fr|lusMinus|artialD|r(ime|o(duct|portion(al)?)|ecedes(SlantEqual|Tilde|Equal)?)?))|(q(scr|int|opf|u(ot|est(eq)?|at(int|ernions))|prime|fr)|Q(scr|opf|UOT|fr))|(R(s(h|cr)|ho|c(y|edil|aron)|Barr|ight(Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|Floor|A(ngleBracket|rrow(Bar|LeftArrow)?))|o(undImplies|pf)|uleDelayed|e(verse(UpEquilibrium|E(quilibrium|lement)))?|fr|EG|a(ng|cute|rr(tl)?)|rightarrow)|r(s(h|cr|q(uo(r)?|b)|aquo)|h(o(v)?|ar(d|u(l)?))|nmid|c(y|ub|e(il|dil)|aron)|Barr|t(hree|imes|ri(e|f|ltri)?)|i(singdotseq|ng|ght(squigarrow|harpoon(down|up)|threetimes|left(harpoons|arrows)|arrow(tail)?|rightarrows))|Har|o(times|p(f|lus|ar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|ldhar)|uluhar|p(polint|ar(gt)?)|e(ct|al(s|ine|part)?|g)|f(isht|loor|r)|l(har|arr|m)|a(ng(d|e|le)?|c(ute|e)|t(io(nals)?|ail)|dic|emptyv|quo|rr(sim|hk|c|tl|pl|fs|w|lp|ap|b(fs)?)?)|rarr|x|moust(ache)?|b(arr|r(k(sl(d|u)|e)|ac(e|k))|brk)|A(tail|arr|rr)))|(s(s(cr|tarf|etmn|mile)|h(y|c(hcy|y)|ort(parallel|mid)|arp)|c(sim|y|n(sim|E|ap)|cue|irc|polint|e(dil)?|E|a(p|ron))?|t(ar(f)?|r(ns|aight(phi|epsilon)))|i(gma(v|f)?|m(ne|dot|plus|e(q)?|l(E)?|rarr|g(E)?)?)|zlig|o(pf|ftcy|l(b(ar)?)?)|dot(e|b)?|u(ng|cc(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?|p(s(im|u(p|b)|et(neq(q)?|eq(q)?)?)|hs(ol|ub)|1|n(e|E)|2|d(sub|ot)|3|plus|e(dot)?|E|larr|mult)?|m|b(s(im|u(p|b)|et(neq(q)?|eq(q)?)?)|n(e|E)|dot|plus|e(dot)?|E|rarr|mult)?)|pa(des(uit)?|r)|e(swar|ct|tm(n|inus)|ar(hk|r(ow)?)|xt|mi|Arr)|q(su(p(set(eq)?|e)?|b(set(eq)?|e)?)|c(up(s)?|ap(s)?)|u(f|ar(e|f))?)|fr(own)?|w(nwar|ar(hk|r(ow)?)|Arr)|larr|acute|rarr|m(t(e(s)?)?|i(d|le)|eparsl|a(shp|llsetminus))|bquo)|S(scr|hort(RightArrow|DownArrow|UpArrow|LeftArrow)|c(y|irc|edil|aron)?|tar|igma|H(cy|CHcy)|opf|u(c(hThat|ceeds(SlantEqual|Tilde|Equal)?)|p(set|erset(Equal)?)?|m|b(set(Equal)?)?)|OFTcy|q(uare(Su(perset(Equal)?|bset(Equal)?)|Intersection|Union)?|rt)|fr|acute|mallCircle))|(t(s(hcy|c(y|r)|trok)|h(i(nsp|ck(sim|approx))|orn|e(ta(sym|v)?|re(4|fore))|k(sim|ap))|c(y|edil|aron)|i(nt|lde|mes(d|b(ar)?)?)|o(sa|p(cir|f(ork)?|bot)?|ea)|dot|prime|elrec|fr|w(ixt|ohead(leftarrow|rightarrow))|a(u|rget)|r(i(sb|time|dot|plus|e|angle(down|q|left(eq)?|right(eq)?)?|minus)|pezium|ade)|brk)|T(s(cr|trok)|RADE|h(i(nSpace|ckSpace)|e(ta|refore))|c(y|edil|aron)|S(cy|Hcy)|ilde(Tilde|Equal|FullEqual)?|HORN|opf|fr|a(u|b)|ripleDot))|(u(scr|h(ar(l|r)|blk)|c(y|irc)|t(ilde|dot|ri(f)?)|Har|o(pf|gon)|d(har|arr|blac)|u(arr|ml)|p(si(h|lon)?|harpoon(left|right)|downarrow|uparrows|lus|arrow)|f(isht|r)|wangle|l(c(orn(er)?|rop)|tri)|a(cute|rr)|r(c(orn(er)?|rop)|tri|ing)|grave|m(l|acr)|br(cy|eve)|Arr)|U(scr|n(ion(Plus)?|der(B(ar|rac(e|ket))|Parenthesis))|c(y|irc)|tilde|o(pf|gon)|dblac|uml|p(si(lon)?|downarrow|Tee(Arrow)?|per(RightArrow|LeftArrow)|DownArrow|Equilibrium|arrow|Arrow(Bar|DownArrow)?)|fr|a(cute|rr(ocir)?)|ring|grave|macr|br(cy|eve)))|(v(s(cr|u(pn(e|E)|bn(e|E)))|nsu(p|b)|cy|Bar(v)?|zigzag|opf|dash|prop|e(e(eq|bar)?|llip|r(t|bar))|Dash|fr|ltri|a(ngrt|r(s(igma|u(psetneq(q)?|bsetneq(q)?))|nothing|t(heta|riangle(left|right))|p(hi|i|ropto)|epsilon|kappa|r(ho)?))|rtri|Arr)|V(scr|cy|opf|dash(l)?|e(e|r(yThinSpace|t(ical(Bar|Separator|Tilde|Line))?|bar))|Dash|vdash|fr|bar))|(w(scr|circ|opf|p|e(ierp|d(ge(q)?|bar))|fr|r(eath)?)|W(scr|circ|opf|edge|fr))|(X(scr|i|opf|fr)|x(s(cr|qcup)|h(arr|Arr)|nis|c(irc|up|ap)|i|o(time|dot|p(f|lus))|dtri|u(tri|plus)|vee|fr|wedge|l(arr|Arr)|r(arr|Arr)|map))|(y(scr|c(y|irc)|icy|opf|u(cy|ml)|en|fr|ac(y|ute))|Y(scr|c(y|irc)|opf|uml|Icy|Ucy|fr|acute|Acy))|(z(scr|hcy|c(y|aron)|igrarr|opf|dot|e(ta|etrf)|fr|w(nj|j)|acute)|Z(scr|c(y|aron)|Hcy|opf|dot|e(ta|roWidthSpace)|fr|acute)))(;)\", \"name\": \"constant.character.entity.named.$2.html\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.html\" }, \"3\": { \"name\": \"punctuation.definition.entity.html\" } }, \"match\": \"(&)#\\\\d+(;)\", \"name\": \"constant.character.entity.numeric.decimal.html\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.html\" }, \"3\": { \"name\": \"punctuation.definition.entity.html\" } }, \"match\": \"(&)#[xX][0-9a-fA-F]+(;)\", \"name\": \"constant.character.entity.numeric.hexadecimal.html\" }, { \"match\": \"&(?=[a-zA-Z0-9]+;)\", \"name\": \"invalid.illegal.ambiguous-ampersand.html\" }] }, \"math\": { \"patterns\": [{ \"begin\": `(?i)(<)(math)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.structure.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.structure.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.structure.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }], \"repository\": { \"attribute\": { \"patterns\": [{ \"begin\": \"(s(hift|ymmetric|cript(sizemultiplier|level|minsize)|t(ackalign|retchy)|ide|u(pscriptshift|bscriptshift)|e(parator(s)?|lection)|rc)|h(eight|ref)|n(otation|umalign)|c(haralign|olumn(spa(n|cing)|width|lines|align)|lose|rossout)|i(n(dent(shift(first|last)?|target|align(first|last)?)|fixlinebreakstyle)|d)|o(pen|verflow)|d(i(splay(style)?|r)|e(nomalign|cimalpoint|pth))|position|e(dge|qual(columns|rows))|voffset|f(orm|ence|rame(spacing)?)|width|l(space|ine(thickness|leading|break(style|multchar)?)|o(ngdivstyle|cation)|ength|quote|argeop)|a(c(cent(under)?|tiontype)|l(t(text|img(-(height|valign|width))?)|ign(mentscope)?))|r(space|ow(spa(n|cing)|lines|align)|quote)|groupalign|x(link:href|mlns)|m(in(size|labelspacing)|ovablelimits|a(th(size|color|variant|background)|xsize))|bevelled)(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"begin\": `([^\\\\x{0020}\"'<>/=\\\\x{0000}-\\\\x{001F}\\\\x{007F}-\\\\x{009F}\\\\x{FDD0}-\\\\x{FDEF}\\\\x{FFFE}\\\\x{FFFF}\\\\x{1FFFE}\\\\x{1FFFF}\\\\x{2FFFE}\\\\x{2FFFF}\\\\x{3FFFE}\\\\x{3FFFF}\\\\x{4FFFE}\\\\x{4FFFF}\\\\x{5FFFE}\\\\x{5FFFF}\\\\x{6FFFE}\\\\x{6FFFF}\\\\x{7FFFE}\\\\x{7FFFF}\\\\x{8FFFE}\\\\x{8FFFF}\\\\x{9FFFE}\\\\x{9FFFF}\\\\x{AFFFE}\\\\x{AFFFF}\\\\x{BFFFE}\\\\x{BFFFF}\\\\x{CFFFE}\\\\x{CFFFF}\\\\x{DFFFE}\\\\x{DFFFF}\\\\x{EFFFE}\\\\x{EFFFF}\\\\x{FFFFE}\\\\x{FFFFF}\\\\x{10FFFE}\\\\x{10FFFF}]+)`, \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"Anything else that is valid\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.unrecognized.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"match\": \"[^\\\\s>]+\", \"name\": \"invalid.illegal.character-not-allowed-here.html\" }] }, \"tags\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#cdata\" }, { \"captures\": { \"0\": { \"name\": \"meta.tag.structure.math.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(annotation|annotation-xml|semantics|menclose|merror|mfenced|mfrac|mpadded|mphantom|mroot|mrow|msqrt|mstyle|mmultiscripts|mover|mprescripts|msub|msubsup|msup|munder|munderover|none|mlabeledtr|mtable|mtd|mtr|mlongdiv|mscarries|mscarry|msgroup|msline|msrow|mstack|maction)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.structure.math.$2.html\" }, { \"begin\": `(?i)(<)(annotation|annotation-xml|semantics|menclose|merror|mfenced|mfrac|mpadded|mphantom|mroot|mrow|msqrt|mstyle|mmultiscripts|mover|mprescripts|msub|msubsup|msup|munder|munderover|none|mlabeledtr|mtable|mtd|mtr|mlongdiv|mscarries|mscarry|msgroup|msline|msrow|mstack|maction)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.structure.math.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.structure.math.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.structure.math.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.inline.math.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(mi|mn|mo|ms|mspace|mtext|maligngroup|malignmark)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.inline.math.$2.html\" }, { \"begin\": `(?i)(<)(mi|mn|mo|ms|mspace|mtext|maligngroup|malignmark)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.inline.math.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.inline.math.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.inline.math.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.object.math.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(mglyph)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.object.math.$2.html\" }, { \"begin\": `(?i)(<)(mglyph)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.object.math.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.object.math.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.object.math.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.other.invalid.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" }, \"4\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"6\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(([\\\\w:]+))(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.other.invalid.html\" }, { \"begin\": `(?i)(<)((\\\\w[^\\\\s>]*))(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.other.invalid.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" }, \"4\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"6\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)((\\\\2))\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.other.invalid.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.other.invalid.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.invalid.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"include\": \"#tags-invalid\" }] } } }, \"svg\": { \"patterns\": [{ \"begin\": `(?i)(<)(svg)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.structure.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.structure.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.structure.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }], \"repository\": { \"attribute\": { \"patterns\": [{ \"begin\": \"(s(hape-rendering|ystemLanguage|cale|t(yle|itchTiles|op-(color|opacity)|dDeviation|em(h|v)|artOffset|r(i(ng|kethrough-(thickness|position))|oke(-(opacity|dash(offset|array)|width|line(cap|join)|miterlimit))?))|urfaceScale|p(e(cular(Constant|Exponent)|ed)|acing|readMethod)|eed|lope)|h(oriz-(origin-x|adv-x)|eight|anging|ref(lang)?)|y(1|2|ChannelSelector)?|n(umOctaves|ame)|c(y|o(ntentS(criptType|tyleType)|lor(-(interpolation(-filters)?|profile|rendering))?)|ursor|l(ip(-(path|rule)|PathUnits)?|ass)|a(p-height|lcMode)|x)|t(ype|o|ext(-(decoration|anchor|rendering)|Length)|a(rget(X|Y)?|b(index|leValues))|ransform)|i(n(tercept|2)?|d(eographic)?|mage-rendering)|z(oomAndPan)?|o(p(erator|acity)|ver(flow|line-(thickness|position))|ffset|r(i(ent(ation)?|gin)|der))|d(y|i(splay|visor|ffuseConstant|rection)|ominant-baseline|ur|e(scent|celerate)|x)?|u(1|n(i(code(-(range|bidi))?|ts-per-em)|derline-(thickness|position))|2)|p(ing|oint(s(At(X|Y|Z))?|er-events)|a(nose-1|t(h(Length)?|tern(ContentUnits|Transform|Units))|int-order)|r(imitiveUnits|eserveA(spectRatio|lpha)))|e(n(d|able-background)|dgeMode|levation|x(ternalResourcesRequired|ponent))|v(i(sibility|ew(Box|Target))|-(hanging|ideographic|alphabetic|mathematical)|e(ctor-effect|r(sion|t-(origin-(y|x)|adv-y)))|alues)|k(1|2|3|e(y(Splines|Times|Points)|rn(ing|el(Matrix|UnitLength)))|4)?|f(y|il(ter(Res|Units)?|l(-(opacity|rule))?)|o(nt-(s(t(yle|retch)|ize(-adjust)?)|variant|family|weight)|rmat)|lood-(color|opacity)|r(om)?|x)|w(idth(s)?|ord-spacing|riting-mode)|l(i(ghting-color|mitingConeAngle)|ocal|e(ngthAdjust|tter-spacing)|ang)|a(scent|cc(umulate|ent-height)|ttribute(Name|Type)|zimuth|dditive|utoReverse|l(ignment-baseline|phabetic|lowReorder)|rabic-form|mplitude)|r(y|otate|e(s(tart|ult)|ndering-intent|peat(Count|Dur)|quired(Extensions|Features)|f(X|Y|errerPolicy)|l)|adius|x)?|g(1|2|lyph(Ref|-(name|orientation-(horizontal|vertical)))|radient(Transform|Units))|x(1|2|ChannelSelector|-height|link:(show|href|t(ype|itle)|a(ctuate|rcrole)|role)|ml:(space|lang|base))?|m(in|ode|e(thod|dia)|a(sk(ContentUnits|Units)?|thematical|rker(Height|-(start|end|mid)|Units|Width)|x))|b(y|ias|egin|ase(Profile|line-shift|Frequency)|box))(?![\\\\w:-])\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"begin\": `([^\\\\x{0020}\"'<>/=\\\\x{0000}-\\\\x{001F}\\\\x{007F}-\\\\x{009F}\\\\x{FDD0}-\\\\x{FDEF}\\\\x{FFFE}\\\\x{FFFF}\\\\x{1FFFE}\\\\x{1FFFF}\\\\x{2FFFE}\\\\x{2FFFF}\\\\x{3FFFE}\\\\x{3FFFF}\\\\x{4FFFE}\\\\x{4FFFF}\\\\x{5FFFE}\\\\x{5FFFF}\\\\x{6FFFE}\\\\x{6FFFF}\\\\x{7FFFE}\\\\x{7FFFF}\\\\x{8FFFE}\\\\x{8FFFF}\\\\x{9FFFE}\\\\x{9FFFF}\\\\x{AFFFE}\\\\x{AFFFF}\\\\x{BFFFE}\\\\x{BFFFF}\\\\x{CFFFE}\\\\x{CFFFF}\\\\x{DFFFE}\\\\x{DFFFF}\\\\x{EFFFE}\\\\x{EFFFF}\\\\x{FFFFE}\\\\x{FFFFF}\\\\x{10FFFE}\\\\x{10FFFF}]+)`, \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.html\" } }, \"comment\": \"Anything else that is valid\", \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.unrecognized.$1.html\", \"patterns\": [{ \"include\": \"#attribute-interior\" }] }, { \"match\": \"[^\\\\s>]+\", \"name\": \"invalid.illegal.character-not-allowed-here.html\" }] }, \"tags\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#cdata\" }, { \"captures\": { \"0\": { \"name\": \"meta.tag.metadata.svg.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(color-profile|desc|metadata|script|style|title)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.metadata.svg.$2.html\" }, { \"begin\": `(?i)(<)(color-profile|desc|metadata|script|style|title)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.svg.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.svg.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.metadata.svg.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.structure.svg.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(animateMotion|clipPath|defs|feComponentTransfer|feDiffuseLighting|feMerge|feSpecularLighting|filter|g|hatch|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|pattern|radialGradient|switch|text|textPath)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.structure.svg.$2.html\" }, { \"begin\": `(?i)(<)(animateMotion|clipPath|defs|feComponentTransfer|feDiffuseLighting|feMerge|feSpecularLighting|filter|g|hatch|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|pattern|radialGradient|switch|text|textPath)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.structure.svg.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.structure.svg.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.structure.svg.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.inline.svg.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(a|animate|discard|feBlend|feColorMatrix|feComposite|feConvolveMatrix|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feMergeNode|feMorphology|feOffset|fePointLight|feSpotLight|feTile|feTurbulence|hatchPath|mpath|set|solidcolor|stop|tspan)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.inline.svg.$2.html\" }, { \"begin\": `(?i)(<)(a|animate|discard|feBlend|feColorMatrix|feComposite|feConvolveMatrix|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feMergeNode|feMorphology|feOffset|fePointLight|feSpotLight|feTile|feTurbulence|hatchPath|mpath|set|solidcolor|stop|tspan)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.inline.svg.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.inline.svg.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.inline.svg.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.object.svg.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(circle|ellipse|feImage|foreignObject|image|line|path|polygon|polyline|rect|symbol|use|view)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.object.svg.$2.html\" }, { \"begin\": `(?i)(<)(a|circle|ellipse|feImage|foreignObject|image|line|path|polygon|polyline|rect|symbol|use|view)(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.object.svg.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.object.svg.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.object.svg.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.other.svg.$2.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" }, \"4\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"6\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)((altGlyph|altGlyphDef|altGlyphItem|animateColor|animateTransform|cursor|font|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|glyph|glyphRef|hkern|missing-glyph|tref|vkern))(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.other.svg.$2.html\" }, { \"begin\": `(?i)(<)((altGlyph|altGlyphDef|altGlyphItem|animateColor|animateTransform|cursor|font|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|glyph|glyphRef|hkern|missing-glyph|tref|vkern))(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.other.svg.$2.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" }, \"4\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"6\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)((\\\\2))\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.other.svg.$2.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.other.svg.$2.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.tag.other.invalid.void.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" }, \"4\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"6\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"match\": `(?i)(<)(([\\\\w:]+))(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(/>))`, \"name\": \"meta.element.other.invalid.html\" }, { \"begin\": `(?i)(<)((\\\\w[^\\\\s>]*))(?=\\\\s|/?>)(?:(([^\"'>]|\"[^\"]*\"|'[^']*')*)(>))?`, \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.other.invalid.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" }, \"4\": { \"patterns\": [{ \"include\": \"#attribute\" }] }, \"6\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?i)(</)((\\\\2))\\\\s*(>)|(/>)|(?=</\\\\w+)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.other.invalid.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"5\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.element.other.invalid.html\", \"patterns\": [{ \"begin\": \"(?<!>)\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.invalid.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#tags\" }] }, { \"include\": \"#tags-invalid\" }] } } }, \"tags-invalid\": { \"patterns\": [{ \"begin\": \"(</?)((\\\\w[^\\\\s>]*))(?<!/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-tag.html\" } }, \"end\": \"((?: ?/)?>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.$2.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }] }, \"tags-valid\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=<(?i:style)\\\\b(?!-))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.embedded.leading.html\" } }, \"end\": \"(?!\\\\G)([ \\\\t]*$\\\\n?)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.embedded.trailing.html\" } }, \"patterns\": [{ \"begin\": \"(?i)(<)(style)(?=\\\\s|/?>)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.style.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"(?i)((<)/)(style)\\\\s*(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.style.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"source.css-ignored-vscode\" }, \"3\": { \"name\": \"entity.name.tag.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.embedded.block.html\", \"patterns\": [{ \"begin\": \"\\\\G\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(>)\", \"name\": \"meta.tag.metadata.style.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?!\\\\G)\", \"end\": \"(?=</(?i:style))\", \"name\": \"source.css\", \"patterns\": [{ \"include\": \"source.css\" }] }] }] }, { \"begin\": \"(^[ \\\\t]+)?(?=<(?i:script)\\\\b(?!-))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.embedded.leading.html\" } }, \"end\": \"(?!\\\\G)([ \\\\t]*$\\\\n?)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.embedded.trailing.html\" } }, \"patterns\": [{ \"begin\": \"(<)((?i:script))\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.script.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"(/)((?i:script))(>)\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.script.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.embedded.block.html\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=/)\", \"patterns\": [{ \"begin\": \"(>)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.script.start.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"((<))(?=/(?i:script))\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.script.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"source.js-ignored-vscode\" } }, \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=</(?i:script))\", \"name\": \"source.js\", \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.js\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"(?=<\\/script)|\\\\n\", \"name\": \"comment.line.double-slash.js\" }] }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.js\" } }, \"end\": \"\\\\*/|(?=<\\/script)\", \"name\": \"comment.block.js\" }, { \"include\": \"source.js\" }] }] }, { \"begin\": \"\\\\G\", \"end\": `(?i:(?=>|type(?=[\\\\s=])(?!\\\\s*=\\\\s*(''|\"\"|('|\"|)(text/(javascript(1\\\\.[0-5])?|x-javascript|jscript|livescript|(x-)?ecmascript|babel)|application/((x-)?javascript|(x-)?ecmascript)|module)[\\\\s\"'>]))))`, \"name\": \"meta.tag.metadata.script.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": `(?i:(?=type\\\\s*=\\\\s*('|\"|)text/(x-handlebars|(x-(handlebars-)?|ng-)?template|html)[\\\\s\"'>]))`, \"end\": \"((<))(?=/(?i:script))\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.script.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"text.html.basic\" } }, \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.script.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?!\\\\G)\", \"end\": \"(?=</(?i:script))\", \"name\": \"text.html.basic\", \"patterns\": [{ \"include\": \"text.html.basic\" }] }] }, { \"begin\": \"(?=(?i:type))\", \"end\": \"(<)(?=/(?i:script))\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.script.end.html\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" } }, \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.script.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?!\\\\G)\", \"end\": \"(?=</(?i:script))\", \"name\": \"source.unknown\" }] }] }] }] }, { \"begin\": \"(?i)(<)(base|link|meta)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.$2.void.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(noscript|title)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)(noscript|title)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(col|hr|input)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.$2.void.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(address|article|aside|blockquote|body|button|caption|colgroup|datalist|dd|details|dialog|div|dl|dt|fieldset|figcaption|figure|footer|form|head|header|hgroup|html|h[1-6]|label|legend|li|main|map|menu|meter|nav|ol|optgroup|option|output|p|pre|progress|section|select|slot|summary|table|tbody|td|template|textarea|tfoot|th|thead|tr|ul)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)(address|article|aside|blockquote|body|button|caption|colgroup|datalist|dd|details|dialog|div|dl|dt|fieldset|figcaption|figure|footer|form|head|header|hgroup|html|h[1-6]|label|legend|li|main|map|menu|meter|nav|ol|optgroup|option|output|p|pre|progress|section|select|slot|summary|table|tbody|td|template|textarea|tfoot|th|thead|tr|ul)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(area|br|wbr)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.$2.void.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(a|abbr|b|bdi|bdo|cite|code|data|del|dfn|em|i|ins|kbd|mark|q|rp|rt|ruby|s|samp|small|span|strong|sub|sup|time|u|var)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)(a|abbr|b|bdi|bdo|cite|code|data|del|dfn|em|i|ins|kbd|mark|q|rp|rt|ruby|s|samp|small|span|strong|sub|sup|time|u|var)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(embed|img|param|source|track)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.$2.void.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)(audio|canvas|iframe|object|picture|video)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)(audio|canvas|iframe|object|picture|video)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)((basefont|isindex))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.metadata.$2.void.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)((center|frameset|noembed|noframes))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)((center|frameset|noembed|noframes))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)((acronym|big|blink|font|strike|tt|xmp))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)((acronym|big|blink|font|strike|tt|xmp))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.inline.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)((frame))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.$2.void.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)((applet))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)((applet))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.deprecated.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.object.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(<)((dir|keygen|listing|menuitem|plaintext|spacer))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.no-longer-supported.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.$2.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(?i)(</)((dir|keygen|listing|menuitem|plaintext|spacer))(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"invalid.illegal.no-longer-supported.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.other.$2.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"include\": \"#math\" }, { \"include\": \"#svg\" }, { \"begin\": \"(<)([a-zA-Z][.0-9_a-zA-Z\\\\x{00B7}\\\\x{00C0}-\\\\x{00D6}\\\\x{00D8}-\\\\x{00F6}\\\\x{00F8}-\\\\x{037D}\\\\x{037F}-\\\\x{1FFF}\\\\x{200C}-\\\\x{200D}\\\\x{203F}-\\\\x{2040}\\\\x{2070}-\\\\x{218F}\\\\x{2C00}-\\\\x{2FEF}\\\\x{3001}-\\\\x{D7FF}\\\\x{F900}-\\\\x{FDCF}\\\\x{FDF0}-\\\\x{FFFD}\\\\x{10000}-\\\\x{EFFFF}]*-[\\\\-.0-9_a-zA-Z\\\\x{00B7}\\\\x{00C0}-\\\\x{00D6}\\\\x{00D8}-\\\\x{00F6}\\\\x{00F8}-\\\\x{037D}\\\\x{037F}-\\\\x{1FFF}\\\\x{200C}-\\\\x{200D}\\\\x{203F}-\\\\x{2040}\\\\x{2070}-\\\\x{218F}\\\\x{2C00}-\\\\x{2FEF}\\\\x{3001}-\\\\x{D7FF}\\\\x{F900}-\\\\x{FDCF}\\\\x{FDF0}-\\\\x{FFFD}\\\\x{10000}-\\\\x{EFFFF}]*)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.custom.start.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }, { \"begin\": \"(</)([a-zA-Z][.0-9_a-zA-Z\\\\x{00B7}\\\\x{00C0}-\\\\x{00D6}\\\\x{00D8}-\\\\x{00F6}\\\\x{00F8}-\\\\x{037D}\\\\x{037F}-\\\\x{1FFF}\\\\x{200C}-\\\\x{200D}\\\\x{203F}-\\\\x{2040}\\\\x{2070}-\\\\x{218F}\\\\x{2C00}-\\\\x{2FEF}\\\\x{3001}-\\\\x{D7FF}\\\\x{F900}-\\\\x{FDCF}\\\\x{FDF0}-\\\\x{FFFD}\\\\x{10000}-\\\\x{EFFFF}]*-[\\\\-.0-9_a-zA-Z\\\\x{00B7}\\\\x{00C0}-\\\\x{00D6}\\\\x{00D8}-\\\\x{00F6}\\\\x{00F8}-\\\\x{037D}\\\\x{037F}-\\\\x{1FFF}\\\\x{200C}-\\\\x{200D}\\\\x{203F}-\\\\x{2040}\\\\x{2070}-\\\\x{218F}\\\\x{2C00}-\\\\x{2FEF}\\\\x{3001}-\\\\x{D7FF}\\\\x{F900}-\\\\x{FDCF}\\\\x{FDF0}-\\\\x{FFFD}\\\\x{10000}-\\\\x{EFFFF}]*)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.custom.end.html\", \"patterns\": [{ \"include\": \"#attribute\" }] }] }, \"xml-processing\": { \"begin\": \"(<\\\\?)(xml)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"(\\\\?>)\", \"name\": \"meta.tag.metadata.processing.xml.html\", \"patterns\": [{ \"include\": \"#attribute\" }] } }, \"scopeName\": \"text.html.basic\", \"embeddedLangs\": [\"javascript\", \"css\"] });\nvar html = [\n  ...javascript,\n  ...css,\n  lang\n];\n\nexport { html as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,cAAc;QAAE,yHAAyH;YAAE,WAAW;YAA8D,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAyC;aAAE;QAAC;IAAE;IAAG,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA61B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,WAAW;oBAAwC,OAAO;oBAAoB,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,WAAW;oBAAyB,OAAO;oBAAoB,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,OAAO;4BAAiC,YAAY;gCAAC;oCAAE,SAAS;oCAA0B,OAAO;oCAAW,QAAQ;oCAA0B,YAAY;wCAAC;4CAAE,YAAY;gDAAE,KAAK;oDAAE,QAAQ;gDAAa;4CAAE;4CAAG,SAAS;4CAA4B,QAAQ;wCAAuB;wCAAG;4CAAE,SAAS;4CAAK,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAA2C;4CAAE;4CAAG,eAAe;4CAAc,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAyC;gDAAG,KAAK;oDAAE,QAAQ;gDAAa;4CAAE;4CAAG,QAAQ;4CAA6B,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAK,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAA2C;4CAAE;4CAAG,eAAe;4CAAc,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAyC;gDAAG,KAAK;oDAAE,QAAQ;gDAAa;4CAAE;4CAAG,QAAQ;4CAA6B,YAAY;gDAAC;oDAAE,WAAW;gDAAY;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAK,QAAQ;gCAA8C;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgpB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,WAAW;oBAAoC,OAAO;oBAAoB,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;4BAAE;4BAAG,OAAO;4BAAiC,YAAY;gCAAC;oCAAE,SAAS;oCAA0B,OAAO;oCAAW,QAAQ;oCAAyB,YAAY;wCAAC;4CAAE,YAAY;gDAAE,KAAK;oDAAE,QAAQ;gDAAY;gDAAG,KAAK;oDAAE,YAAY;wDAAC;4DAAE,WAAW;wDAAY;qDAAE;gDAAC;4CAAE;4CAAG,SAAS;4CAA8B,QAAQ;wCAAuB;wCAAG;4CAAE,SAAS;4CAAK,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAA2C;4CAAE;4CAAG,eAAe;4CAAa,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAyC;gDAAG,KAAK;oDAAE,QAAQ;gDAAY;4CAAE;4CAAG,QAAQ;4CAA6B,YAAY;gDAAC;oDAAE,YAAY;wDAAE,KAAK;4DAAE,YAAY;gEAAC;oEAAE,WAAW;gEAAY;6DAAE;wDAAC;oDAAE;oDAAG,SAAS;gDAAwB;gDAAG;oDAAE,SAAS;oDAAM,iBAAiB;wDAAE,KAAK;4DAAE,QAAQ;wDAAoC;oDAAE;oDAAG,OAAO;oDAAa,QAAQ;gDAA+B;gDAAG;oDAAE,SAAS;oDAAQ,iBAAiB;wDAAE,KAAK;4DAAE,QAAQ;wDAA0C;oDAAE;oDAAG,OAAO;oDAAc,eAAe;wDAAE,KAAK;4DAAE,QAAQ;wDAAwC;oDAAE;oDAAG,QAAQ;gDAAmB;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAK,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAA2C;4CAAE;4CAAG,eAAe;4CAAa,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAyC;gDAAG,KAAK;oDAAE,QAAQ;gDAAY;4CAAE;4CAAG,QAAQ;4CAA6B,YAAY;gDAAC;oDAAE,YAAY;wDAAE,KAAK;4DAAE,YAAY;gEAAC;oEAAE,WAAW;gEAAY;6DAAE;wDAAC;oDAAE;oDAAG,SAAS;gDAAwB;gDAAG;oDAAE,SAAS;oDAAM,iBAAiB;wDAAE,KAAK;4DAAE,QAAQ;wDAAoC;oDAAE;oDAAG,OAAO;oDAAa,QAAQ;gDAA+B;gDAAG;oDAAE,SAAS;oDAAQ,iBAAiB;wDAAE,KAAK;4DAAE,QAAQ;wDAA0C;oDAAE;oDAAG,OAAO;oDAAc,eAAe;wDAAE,KAAK;4DAAE,QAAQ;wDAAwC;oDAAE;oDAAG,QAAQ;gDAAmB;6CAAE;wCAAC;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAK,QAAQ;gCAA8C;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,WAAW;oBAA4B,OAAO;oBAAoB,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAqC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,WAAW;oBAA+B,OAAO;oBAAoB,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,kaAAka,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,WAAW;oBAA+B,OAAO;oBAAoB,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAAkD;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAiC,YAAY;wBAAC;4BAAE,SAAS;4BAA4B,QAAQ;wBAAuB;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAyC;4BAAE;4BAAG,QAAQ;4BAA6B,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAyC;4BAAE;4BAAG,QAAQ;4BAA6B,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAA8C;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,eAAe;YAAiC,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;QAA+B;QAAG,WAAW;YAAE,SAAS;YAAQ,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAO,QAAQ;YAAsB,YAAY;gBAAC;oBAAE,SAAS;oBAAU,QAAQ;gBAAmD;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAmD;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAmD;aAAE;QAAC;QAAG,sBAAsB;YAAE,WAAW;YAAsE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAY,QAAQ;gBAAmC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,OAAO;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,WAAW;oBAAgE,SAAS;oBAA86W,QAAQ;gBAA0C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAAiD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAA2B,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAA2C;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,2DAA2D,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAwB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,SAAS;4BAAa,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAiC,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;YAAE,cAAc;gBAAE,aAAa;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAA+xB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,OAAO;4BAAoB,QAAQ;4BAA0B,YAAY;gCAAC;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS,CAAC,kaAAka,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,WAAW;4BAA+B,OAAO;4BAAoB,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAY,QAAQ;wBAAkD;qBAAE;gBAAC;gBAAG,QAAQ;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,mUAAmU,CAAC;4BAAE,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS,CAAC,mUAAmU,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAAiC,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,sGAAsG,CAAC;4BAAE,QAAQ;wBAAmC;wBAAG;4BAAE,SAAS,CAAC,sGAAsG,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAA8B,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,6DAA6D,CAAC;4BAAE,QAAQ;wBAAmC;wBAAG;4BAAE,SAAS,CAAC,6DAA6D,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAA8B,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,gEAAgE,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS,CAAC,oEAAoE,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAA0C,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;YAAE;QAAE;QAAG,OAAO;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,0DAA0D,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAwB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,SAAS;4BAAa,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAiC,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;YAAE,cAAc;gBAAE,aAAa;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAqpE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,OAAO;4BAAoB,QAAQ;4BAA0B,YAAY;gCAAC;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS,CAAC,kaAAka,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,WAAW;4BAA+B,OAAO;4BAAoB,QAAQ;4BAAuC,YAAY;gCAAC;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAY,QAAQ;wBAAkD;qBAAE;gBAAC;gBAAG,QAAQ;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,qGAAqG,CAAC;4BAAE,QAAQ;wBAAoC;wBAAG;4BAAE,SAAS,CAAC,qGAAqG,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAqC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAAgC,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,6QAA6Q,CAAC;4BAAE,QAAQ;wBAAqC;wBAAG;4BAAE,SAAS,CAAC,6QAA6Q,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAsC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAAiC,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,yVAAyV,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS,CAAC,yVAAyV,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAA8B,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,iJAAiJ,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS,CAAC,mJAAmJ,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAwC,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAA8B,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,wPAAwP,CAAC;4BAAE,QAAQ;wBAAiC;wBAAG;4BAAE,SAAS,CAAC,wPAAwP,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAA0C,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAA6B,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,SAAS,CAAC,gEAAgE,CAAC;4BAAE,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS,CAAC,oEAAoE,CAAC;4BAAE,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAA0C,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAkC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAAmC,YAAY;gCAAC;oCAAE,SAAS;oCAAa,OAAO;oCAAY,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,QAAQ;oCAAqC,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;YAAE;QAAE;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAA0B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA6B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,OAAO;4BAA4B,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,SAAS;oCAAO,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAsC;oCAAE;oCAAG,OAAO;oCAAO,QAAQ;oCAAsC,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAW,OAAO;oCAAoB,QAAQ;oCAAc,YAAY;wCAAC;4CAAE,WAAW;wCAAa;qCAAE;gCAAC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAA0B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAuB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,OAAO;4BAAuB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,QAAQ;4BAA4B,YAAY;gCAAC;oCAAE,SAAS;oCAAO,OAAO;oCAAS,YAAY;wCAAC;4CAAE,SAAS;4CAAO,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAsC;gDAAG,KAAK;oDAAE,QAAQ;gDAAsC;4CAAE;4CAAG,OAAO;4CAAyB,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAoC;gDAAG,KAAK;oDAAE,QAAQ;gDAAwC;gDAAG,KAAK;oDAAE,QAAQ;gDAA2B;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,SAAS;oDAAO,OAAO;oDAAqB,QAAQ;oDAAa,YAAY;wDAAC;4DAAE,SAAS;4DAAqB,iBAAiB;gEAAE,KAAK;oEAAE,QAAQ;gEAA4C;4DAAE;4DAAG,OAAO;4DAAW,YAAY;gEAAC;oEAAE,SAAS;oEAAM,iBAAiB;wEAAE,KAAK;4EAAE,QAAQ;wEAAoC;oEAAE;oEAAG,OAAO;oEAAqB,QAAQ;gEAA+B;6DAAE;wDAAC;wDAAG;4DAAE,SAAS;4DAAQ,YAAY;gEAAE,KAAK;oEAAE,QAAQ;gEAAoC;4DAAE;4DAAG,OAAO;4DAAsB,QAAQ;wDAAmB;wDAAG;4DAAE,WAAW;wDAAY;qDAAE;gDAAC;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAO,OAAO,CAAC,sMAAsM,CAAC;4CAAE,QAAQ;4CAAuC,YAAY;gDAAC;oDAAE,WAAW;gDAAa;6CAAE;wCAAC;wCAAG;4CAAE,SAAS,CAAC,4FAA4F,CAAC;4CAAE,OAAO;4CAAyB,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAoC;gDAAG,KAAK;oDAAE,QAAQ;gDAAwC;gDAAG,KAAK;oDAAE,QAAQ;gDAAkB;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,SAAS;oDAAO,OAAO;oDAAO,eAAe;wDAAE,KAAK;4DAAE,QAAQ;wDAAsC;oDAAE;oDAAG,QAAQ;oDAAuC,YAAY;wDAAC;4DAAE,WAAW;wDAAa;qDAAE;gDAAC;gDAAG;oDAAE,SAAS;oDAAW,OAAO;oDAAqB,QAAQ;oDAAmB,YAAY;wDAAC;4DAAE,WAAW;wDAAkB;qDAAE;gDAAC;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAiB,OAAO;4CAAuB,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAoC;gDAAG,KAAK;oDAAE,QAAQ;gDAAwC;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,SAAS;oDAAO,OAAO;oDAAO,eAAe;wDAAE,KAAK;4DAAE,QAAQ;wDAAsC;oDAAE;oDAAG,QAAQ;oDAAuC,YAAY;wDAAC;4DAAE,WAAW;wDAAa;qDAAE;gDAAC;gDAAG;oDAAE,SAAS;oDAAW,OAAO;oDAAqB,QAAQ;gDAAiB;6CAAE;wCAAC;qCAAE;gCAAC;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0I,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2I,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA0D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA8D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,SAAS;oBAA0hB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA2hB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAe,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAa;aAAE;QAAC;IAAE;IAAG,aAAa;IAAmB,iBAAiB;QAAC;QAAc;KAAM;AAAC;AACvy4D,IAAI,OAAO;OACN,wMAAA,CAAA,UAAU;OACV,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}]}