{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/gdshader.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"GDShader\", \"fileTypes\": [\"gdshader\"], \"name\": \"gdshader\", \"patterns\": [{ \"include\": \"#any\" }], \"repository\": { \"any\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#enclosed\" }, { \"include\": \"#classifier\" }, { \"include\": \"#definition\" }, { \"include\": \"#keyword\" }, { \"include\": \"#element\" }, { \"include\": \"#separator\" }, { \"include\": \"#operator\" }] }, \"arraySize\": { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"punctuation.bracket.gdshader\" } }, \"end\": \"\\\\]\", \"name\": \"meta.array-size.gdshader\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#keyword\" }, { \"include\": \"#element\" }, { \"include\": \"#separator\" }] }, \"classifier\": { \"begin\": \"(?=\\\\b(?:shader_type|render_mode)\\\\b)\", \"end\": \"(?<=;)\", \"name\": \"meta.classifier.gdshader\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#keyword\" }, { \"include\": \"#identifierClassification\" }, { \"include\": \"#separator\" }] }, \"classifierKeyword\": { \"match\": \"\\\\b(?:shader_type|render_mode)\\\\b\", \"name\": \"keyword.language.classifier.gdshader\" }, \"comment\": { \"patterns\": [{ \"include\": \"#commentLine\" }, { \"include\": \"#commentBlock\" }] }, \"commentBlock\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.gdshader\" }, \"commentLine\": { \"begin\": \"//\", \"end\": \"$\", \"name\": \"comment.line.double-slash.gdshader\" }, \"constantFloat\": { \"match\": \"\\\\b(?:E|PI|TAU)\\\\b\", \"name\": \"constant.language.float.gdshader\" }, \"constructor\": { \"match\": \"\\\\b[a-zA-Z_]\\\\w*(?=\\\\s*\\\\[\\\\s*\\\\w*\\\\s*\\\\]\\\\s*[(])|\\\\b[A-Z]\\\\w*(?=\\\\s*[(])\", \"name\": \"entity.name.type.constructor.gdshader\" }, \"controlKeyword\": { \"match\": \"\\\\b(?:if|else|do|while|for|continue|break|switch|case|default|return|discard)\\\\b\", \"name\": \"keyword.control.gdshader\" }, \"definition\": { \"patterns\": [{ \"include\": \"#structDefinition\" }] }, \"element\": { \"patterns\": [{ \"include\": \"#literalFloat\" }, { \"include\": \"#literalInt\" }, { \"include\": \"#literalBool\" }, { \"include\": \"#identifierType\" }, { \"include\": \"#constructor\" }, { \"include\": \"#processorFunction\" }, { \"include\": \"#identifierFunction\" }, { \"include\": \"#swizzling\" }, { \"include\": \"#identifierField\" }, { \"include\": \"#constantFloat\" }, { \"include\": \"#languageVariable\" }, { \"include\": \"#identifierVariable\" }] }, \"enclosed\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.parenthesis.gdshader\" } }, \"end\": \"\\\\)\", \"name\": \"meta.parenthesis.gdshader\", \"patterns\": [{ \"include\": \"#any\" }] }, \"fieldDefinition\": { \"begin\": \"\\\\b[a-zA-Z_]\\\\w*\\\\b\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#typeKeyword\" }, { \"match\": \".+\", \"name\": \"entity.name.type.gdshader\" }] } }, \"end\": \"(?<=;)\", \"name\": \"meta.definition.field.gdshader\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#keyword\" }, { \"include\": \"#arraySize\" }, { \"include\": \"#fieldName\" }, { \"include\": \"#any\" }] }, \"fieldName\": { \"match\": \"\\\\b[a-zA-Z_]\\\\w*\\\\b\", \"name\": \"entity.name.variable.field.gdshader\" }, \"hintKeyword\": { \"match\": \"\\\\b(?:source_color|hint_(?:color|range|(?:black_)?albedo|normal|(?:default_)?(?:white|black)|aniso|anisotropy|roughness_(?:[rgba]|normal|gray))|filter_(?:nearest|linear)(?:_mipmap(?:_anisotropic)?)?|repeat_(?:en|dis)able)\\\\b\", \"name\": \"support.type.annotation.gdshader\" }, \"identifierClassification\": { \"match\": \"\\\\b[a-z_]+\\\\b\", \"name\": \"entity.other.inherited-class.gdshader\" }, \"identifierField\": { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.gdshader\" }, \"2\": { \"name\": \"entity.name.variable.field.gdshader\" } }, \"match\": \"([.])\\\\s*([a-zA-Z_]\\\\w*)\\\\b(?!\\\\s*\\\\()\" }, \"identifierFunction\": { \"match\": \"\\\\b[a-zA-Z_]\\\\w*(?=(?:\\\\s|/\\\\*(?:\\\\*(?!/)|[^*])*\\\\*/)*[(])\", \"name\": \"entity.name.function.gdshader\" }, \"identifierType\": { \"match\": \"\\\\b[a-zA-Z_]\\\\w*(?=(?:\\\\s*\\\\[\\\\s*\\\\w*\\\\s*\\\\])?\\\\s+[a-zA-Z_]\\\\w*\\\\b)\", \"name\": \"entity.name.type.gdshader\" }, \"identifierVariable\": { \"match\": \"\\\\b[a-zA-Z_]\\\\w*\\\\b\", \"name\": \"variable.name.gdshader\" }, \"keyword\": { \"patterns\": [{ \"include\": \"#classifierKeyword\" }, { \"include\": \"#structKeyword\" }, { \"include\": \"#controlKeyword\" }, { \"include\": \"#modifierKeyword\" }, { \"include\": \"#precisionKeyword\" }, { \"include\": \"#typeKeyword\" }, { \"include\": \"#hintKeyword\" }] }, \"languageVariable\": { \"match\": \"\\\\b(?:[A-Z][A-Z_0-9]*)\\\\b\", \"name\": \"variable.language.gdshader\" }, \"literalBool\": { \"match\": \"\\\\b(?:false|true)\\\\b\", \"name\": \"constant.language.boolean.gdshader\" }, \"literalFloat\": { \"match\": \"\\\\b(?:\\\\d+[eE][-+]?\\\\d+|(?:\\\\d*[.]\\\\d+|\\\\d+[.])(?:[eE][-+]?\\\\d+)?)[fF]?\", \"name\": \"constant.numeric.float.gdshader\" }, \"literalInt\": { \"match\": \"\\\\b(?:0[xX][0-9A-Fa-f]+|\\\\d+[uU]?)\\\\b\", \"name\": \"constant.numeric.integer.gdshader\" }, \"modifierKeyword\": { \"match\": \"\\\\b(?:const|global|instance|uniform|varying|in|out|inout|flat|smooth)\\\\b\", \"name\": \"storage.modifier.gdshader\" }, \"operator\": { \"match\": \"<<=?|>>=?|[-+*/&|<>=!]=|\\\\&\\\\&|[|][|]|[-+~!*/%<>&^|=]\", \"name\": \"keyword.operator.gdshader\" }, \"precisionKeyword\": { \"match\": \"\\\\b(?:low|medium|high)p\\\\b\", \"name\": \"storage.type.built-in.primitive.precision.gdshader\" }, \"processorFunction\": { \"match\": \"\\\\b(?:vertex|fragment|light|start|process|sky|fog)(?=(?:\\\\s|/\\\\*(?:\\\\*(?!/)|[^*])*\\\\*/)*[(])\", \"name\": \"support.function.gdshader\" }, \"separator\": { \"patterns\": [{ \"match\": \"[.]\", \"name\": \"punctuation.accessor.gdshader\" }, { \"include\": \"#separatorComma\" }, { \"match\": \"[;]\", \"name\": \"punctuation.terminator.statement.gdshader\" }, { \"match\": \"[:]\", \"name\": \"keyword.operator.type.annotation.gdshader\" }] }, \"separatorComma\": { \"match\": \"[,]\", \"name\": \"punctuation.separator.comma.gdshader\" }, \"structDefinition\": { \"begin\": \"(?=\\\\b(?:struct)\\\\b)\", \"end\": \"(?<=;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#keyword\" }, { \"include\": \"#structName\" }, { \"include\": \"#structDefinitionBlock\" }, { \"include\": \"#separator\" }] }, \"structDefinitionBlock\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.block.struct.gdshader\" } }, \"end\": \"\\\\}\", \"name\": \"meta.definition.block.struct.gdshader\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#precisionKeyword\" }, { \"include\": \"#fieldDefinition\" }, { \"include\": \"#keyword\" }, { \"include\": \"#any\" }] }, \"structKeyword\": { \"match\": \"\\\\b(?:struct)\\\\b\", \"name\": \"keyword.other.struct.gdshader\" }, \"structName\": { \"match\": \"\\\\b[a-zA-Z_]\\\\w*\\\\b\", \"name\": \"entity.name.type.struct.gdshader\" }, \"swizzling\": { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.gdshader\" }, \"2\": { \"name\": \"variable.other.property.gdshader\" } }, \"match\": \"([.])\\\\s*([xyzw]{2,4}|[rgba]{2,4}|[stpq]{2,4})\\\\b\" }, \"typeKeyword\": { \"match\": \"\\\\b(?:void|bool|[biu]?vec[234]|u?int|float|mat[234]|[iu]?sampler(?:3D|2D(?:Array)?)|samplerCube)\\\\b\", \"name\": \"support.type.gdshader\" } }, \"scopeName\": \"source.gdshader\" });\nvar gdshader = [\n  lang\n];\n\nexport { gdshader as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAY,aAAa;QAAC;KAAW;IAAE,QAAQ;IAAY,YAAY;QAAC;YAAE,WAAW;QAAO;KAAE;IAAE,cAAc;QAAE,OAAO;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAO,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAyC,OAAO;YAAU,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAqC,QAAQ;QAAuC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;QAAyB;QAAG,eAAe;YAAE,SAAS;YAAM,OAAO;YAAK,QAAQ;QAAqC;QAAG,iBAAiB;YAAE,SAAS;YAAsB,QAAQ;QAAmC;QAAG,eAAe;YAAE,SAAS;YAA6E,QAAQ;QAAwC;QAAG,kBAAkB;YAAE,SAAS;YAAoF,QAAQ;QAA2B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAO;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,SAAS;4BAAM,QAAQ;wBAA4B;qBAAE;gBAAC;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAO;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAuB,QAAQ;QAAsC;QAAG,eAAe;YAAE,SAAS;YAAoO,QAAQ;QAAmC;QAAG,4BAA4B;YAAE,SAAS;YAAiB,QAAQ;QAAwC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;QAAyC;QAAG,sBAAsB;YAAE,SAAS;YAA8D,QAAQ;QAAgC;QAAG,kBAAkB;YAAE,SAAS;YAAuE,QAAQ;QAA4B;QAAG,sBAAsB;YAAE,SAAS;YAAuB,QAAQ;QAAyB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA6B,QAAQ;QAA6B;QAAG,eAAe;YAAE,SAAS;YAAwB,QAAQ;QAAqC;QAAG,gBAAgB;YAAE,SAAS;YAA2E,QAAQ;QAAkC;QAAG,cAAc;YAAE,SAAS;YAAyC,QAAQ;QAAoC;QAAG,mBAAmB;YAAE,SAAS;YAA4E,QAAQ;QAA4B;QAAG,YAAY;YAAE,SAAS;YAAyD,QAAQ;QAA4B;QAAG,oBAAoB;YAAE,SAAS;YAA8B,QAAQ;QAAqD;QAAG,qBAAqB;YAAE,SAAS;YAAgG,QAAQ;QAA4B;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;gBAAgC;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA4C;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAO,QAAQ;QAAuC;QAAG,oBAAoB;YAAE,SAAS;YAAwB,OAAO;YAAU,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAO,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAO,QAAQ;YAAyC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAO;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAoB,QAAQ;QAAgC;QAAG,cAAc;YAAE,SAAS;YAAuB,QAAQ;QAAmC;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAAoD;QAAG,eAAe;YAAE,SAAS;YAAuG,QAAQ;QAAwB;IAAE;IAAG,aAAa;AAAkB;AACtiN,IAAI,WAAW;IACb;CACD", "ignoreList": [0], "debugId": null}}]}