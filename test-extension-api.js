// Test script to verify extension API integration with local web app
const https = require('https');
const http = require('http');

const API_BASE_URL = 'http://localhost:3000';

// Test function to make API calls
async function testApiCall(endpoint, method = 'GET', data = null, token = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, API_BASE_URL);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Cubent-Extension-Test/1.0'
            }
        };

        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(responseData);
                    resolve({
                        status: res.statusCode,
                        data: parsedData,
                        headers: res.headers
                    });
                } catch (e) {
                    resolve({
                        status: res.statusCode,
                        data: responseData,
                        headers: res.headers
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Test comprehensive usage tracking
async function testUsageTracking() {
    console.log('🧪 Testing Comprehensive Usage Tracking API...\n');

    const testData = {
        modelId: 'gpt-4',
        provider: 'openai',
        cubentUnits: 1.5,
        tokensUsed: 150,
        inputTokens: 100,
        outputTokens: 50,
        costAccrued: 0.003,
        feature: 'code-completion',
        language: 'javascript',
        metadata: {
            sessionId: 'test-session-123',
            timestamp: new Date().toISOString()
        }
    };

    try {
        // Test the new comprehensive tracking endpoint
        console.log('📊 Testing /api/extension/track-usage endpoint...');
        const result = await testApiCall('/api/extension/track-usage', 'POST', testData, 'test-token');
        
        console.log(`Status: ${result.status}`);
        console.log('Response:', JSON.stringify(result.data, null, 2));
        
        if (result.status === 200) {
            console.log('✅ Comprehensive usage tracking endpoint is working!');
        } else {
            console.log('❌ Comprehensive usage tracking endpoint returned error');
        }
    } catch (error) {
        console.log('❌ Error testing comprehensive usage tracking:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    try {
        // Test the enhanced existing endpoint
        console.log('📊 Testing /api/extension/usage endpoint...');
        const result2 = await testApiCall('/api/extension/usage', 'POST', testData, 'test-token');
        
        console.log(`Status: ${result2.status}`);
        console.log('Response:', JSON.stringify(result2.data, null, 2));
        
        if (result2.status === 200) {
            console.log('✅ Enhanced usage tracking endpoint is working!');
        } else {
            console.log('❌ Enhanced usage tracking endpoint returned error');
        }
    } catch (error) {
        console.log('❌ Error testing enhanced usage tracking:', error.message);
    }
}

// Test basic connectivity
async function testConnectivity() {
    console.log('🔗 Testing basic connectivity to web app...\n');
    
    try {
        const result = await testApiCall('/api/extension/status', 'GET');
        console.log(`Status: ${result.status}`);
        console.log('Response:', JSON.stringify(result.data, null, 2));
        
        if (result.status === 200 || result.status === 404) {
            console.log('✅ Web app is accessible!');
        } else {
            console.log('❌ Web app connectivity issue');
        }
    } catch (error) {
        console.log('❌ Error testing connectivity:', error.message);
    }
}

// Run all tests
async function runTests() {
    console.log('🚀 Starting Extension API Integration Tests\n');
    console.log(`Testing against: ${API_BASE_URL}\n`);
    console.log('='.repeat(50) + '\n');
    
    await testConnectivity();
    console.log('\n' + '='.repeat(50) + '\n');
    await testUsageTracking();
    
    console.log('\n🎉 Tests completed!');
}

// Run the tests
runTests().catch(console.error);
