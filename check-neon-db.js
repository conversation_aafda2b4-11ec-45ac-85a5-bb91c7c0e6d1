const { Pool } = require('pg');

// Neon database connection
const pool = new Pool({
  connectionString: "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkDatabase() {
  console.log('🔍 Checking Neon Database for Token Data...\n');
  
  try {
    // Check UsageMetrics table (daily aggregates)
    console.log('📊 USAGE METRICS (Daily Aggregates):');
    console.log('=' .repeat(80));
    
    const metricsQuery = `
      SELECT
        id,
        "userId",
        "tokensUsed",
        "requestsMade",
        "costAccrued",
        date,
        "cubentUnitsUsed"
      FROM "UsageMetrics"
      ORDER BY date DESC
      LIMIT 10
    `;
    
    const metricsResult = await pool.query(metricsQuery);
    
    if (metricsResult.rows.length === 0) {
      console.log('❌ No UsageMetrics records found');
    } else {
      console.log(`✅ Found ${metricsResult.rows.length} UsageMetrics records:`);
      console.table(metricsResult.rows.map(row => ({
        id: row.id.substring(0, 8) + '...',
        userId: row.userId.substring(0, 8) + '...',
        tokensUsed: row.tokensUsed,
        requestsMade: row.requestsMade,
        costAccrued: row.costAccrued,
        cubentUnitsUsed: row.cubentUnitsUsed,
        date: row.date.toISOString().split('T')[0]
      })));
      
      // Check if any have non-zero tokens
      const nonZeroTokens = metricsResult.rows.filter(row => row.tokensUsed > 0);
      if (nonZeroTokens.length > 0) {
        console.log(`🎉 Found ${nonZeroTokens.length} records with token data!`);
      } else {
        console.log('❌ All tokensUsed values are 0');
      }
      
      // Check if any have non-zero costs
      const nonZeroCosts = metricsResult.rows.filter(row => row.costAccrued > 0);
      if (nonZeroCosts.length > 0) {
        console.log(`💰 Found ${nonZeroCosts.length} records with cost data!`);
      } else {
        console.log('❌ All costAccrued values are 0');
      }
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('📈 USAGE ANALYTICS (Individual Requests):');
    console.log('=' .repeat(80));
    
    // Check UsageAnalytics table (individual requests)
    const analyticsQuery = `
      SELECT 
        id,
        "userId",
        "modelId",
        "tokensUsed",
        "requestsMade",
        "costAccrued",
        "sessionId",
        metadata,
        "createdAt",
        "cubentUnitsUsed"
      FROM "UsageAnalytics" 
      ORDER BY "createdAt" DESC 
      LIMIT 10
    `;
    
    const analyticsResult = await pool.query(analyticsQuery);
    
    if (analyticsResult.rows.length === 0) {
      console.log('❌ No UsageAnalytics records found');
    } else {
      console.log(`✅ Found ${analyticsResult.rows.length} UsageAnalytics records:`);
      
      analyticsResult.rows.forEach((row, index) => {
        console.log(`\n📝 Record ${index + 1}:`);
        console.log(`   ID: ${row.id.substring(0, 12)}...`);
        console.log(`   Model: ${row.modelId}`);
        console.log(`   Tokens Used: ${row.tokensUsed}`);
        console.log(`   Cost Accrued: ${row.costAccrued}`);
        console.log(`   Cubent Units: ${row.cubentUnitsUsed}`);
        console.log(`   Created: ${row.createdAt.toISOString()}`);
        
        // Check metadata for token details
        if (row.metadata) {
          const metadata = typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata;
          if (metadata.inputTokens || metadata.outputTokens) {
            console.log(`   📊 Token Details in Metadata:`);
            console.log(`      Input Tokens: ${metadata.inputTokens || 'N/A'}`);
            console.log(`      Output Tokens: ${metadata.outputTokens || 'N/A'}`);
            console.log(`      Cache Read: ${metadata.cacheReadTokens || 'N/A'}`);
            console.log(`      Cache Write: ${metadata.cacheWriteTokens || 'N/A'}`);
            console.log(`      Event Type: ${metadata.originalEventType || metadata.eventType || 'N/A'}`);
          }
        }
      });
      
      // Summary statistics
      const nonZeroTokensAnalytics = analyticsResult.rows.filter(row => row.tokensUsed > 0);
      const nonZeroCostsAnalytics = analyticsResult.rows.filter(row => row.costAccrued > 0);
      const withTokenMetadata = analyticsResult.rows.filter(row => {
        if (!row.metadata) return false;
        const metadata = typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata;
        return metadata.inputTokens || metadata.outputTokens || metadata.originalEventType === 'LLM_COMPLETION';
      });
      
      console.log('\n📊 SUMMARY:');
      console.log(`   Records with tokensUsed > 0: ${nonZeroTokensAnalytics.length}`);
      console.log(`   Records with costAccrued > 0: ${nonZeroCostsAnalytics.length}`);
      console.log(`   Records with token metadata: ${withTokenMetadata.length}`);
    }
    
    // Check for recent LLM_COMPLETION events
    console.log('\n' + '=' .repeat(80));
    console.log('🔍 SEARCHING FOR LLM_COMPLETION EVENTS:');
    console.log('=' .repeat(80));
    
    const llmEventsQuery = `
      SELECT 
        id,
        "userId",
        "modelId",
        "tokensUsed",
        "costAccrued",
        metadata,
        "createdAt"
      FROM "UsageAnalytics" 
      WHERE metadata::text LIKE '%LLM_COMPLETION%' 
         OR metadata::text LIKE '%inputTokens%'
         OR metadata::text LIKE '%outputTokens%'
      ORDER BY "createdAt" DESC 
      LIMIT 5
    `;
    
    const llmResult = await pool.query(llmEventsQuery);
    
    if (llmResult.rows.length === 0) {
      console.log('❌ No LLM_COMPLETION events found in metadata');
    } else {
      console.log(`🎯 Found ${llmResult.rows.length} potential LLM_COMPLETION events:`);
      llmResult.rows.forEach((row, index) => {
        console.log(`\n🤖 LLM Event ${index + 1}:`);
        console.log(`   ID: ${row.id.substring(0, 12)}...`);
        console.log(`   Model: ${row.modelId}`);
        console.log(`   Tokens Used: ${row.tokensUsed}`);
        console.log(`   Cost Accrued: ${row.costAccrued}`);
        console.log(`   Created: ${row.createdAt.toISOString()}`);
        
        if (row.metadata) {
          const metadata = typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata;
          console.log(`   Metadata:`, JSON.stringify(metadata, null, 4));
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Database query error:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the check
checkDatabase().catch(console.error);
