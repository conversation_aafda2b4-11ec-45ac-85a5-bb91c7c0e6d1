{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/shellscript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Shell\", \"name\": \"shellscript\", \"patterns\": [{ \"include\": \"#initial_context\" }], \"repository\": { \"alias_statement\": { \"begin\": \"(?:(?:[ \\\\t]*+)(alias)(?:[ \\\\t]*+)((?:(?:((?<!\\\\w)-\\\\w+\\\\b)(?:[ \\\\t]*+))*))(?:(?:[ \\\\t]*+)(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?))(?:(?:(=)|(\\\\+=))|(-=))))\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.alias.shell\" }, \"2\": { \"patterns\": [{ \"match\": \"(?<!\\\\w)-\\\\w+\\\\b\", \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" }] }, \"3\": { \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" }, \"4\": { \"name\": \"variable.other.assignment.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"variable.other.assignment.shell\" }, \"7\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"8\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"9\": { \"name\": \"keyword.operator.assignment.shell\" }, \"10\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"11\": { \"name\": \"keyword.operator.assignment.compound.shell\" } }, \"end\": \"(?:(?= |\\\\t|$)|(?:(?:(?:(;)|(&&))|(\\\\|\\\\|))|(&)))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.semicolon.shell\" }, \"2\": { \"name\": \"punctuation.separator.statement.and.shell\" }, \"3\": { \"name\": \"punctuation.separator.statement.or.shell\" }, \"4\": { \"name\": \"punctuation.separator.statement.background.shell\" } }, \"name\": \"meta.expression.assignment.alias.shell\", \"patterns\": [{ \"include\": \"#normal_context\" }] }, \"argument\": { \"begin\": \"(?:[ \\\\t]++)(?!(?:&|\\\\||\\\\(|\\\\[|#|\\\\n|$|;))\", \"beginCaptures\": {}, \"end\": \"(?= |\\\\t|;|\\\\||&|$|\\\\n|\\\\)|\\\\`)\", \"endCaptures\": {}, \"name\": \"meta.argument.shell\", \"patterns\": [{ \"include\": \"#argument_context\" }, { \"include\": \"#line_continuation\" }] }, \"argument_context\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.argument.shell\", \"patterns\": [{ \"match\": \"\\\\*\", \"name\": \"variable.language.special.wildcard.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#numeric_literal\" }, { \"captures\": { \"1\": { \"name\": \"constant.language.$1.shell\" } }, \"match\": \"(?<!\\\\w)(\\\\b(?:true|false)\\\\b)(?!\\\\w)\" }] } }, \"match\": \"(?:[ \\\\t]*+)((?:[^ \\\\t\\\\n>&;<>()$`\\\\\\\\\\\"'<\\\\|]+)(?!>))\" }, { \"include\": \"#normal_context\" }] }, \"arithmetic_double\": { \"patterns\": [{ \"begin\": \"\\\\(\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.double.shell\" } }, \"end\": \"\\\\)(?:\\\\s*)\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.double.shell\" } }, \"name\": \"meta.arithmetic.shell\", \"patterns\": [{ \"include\": \"#math\" }, { \"include\": \"#string\" }] }] }, \"arithmetic_no_dollar\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.single.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.arithmetic.single.shell\" } }, \"name\": \"meta.arithmetic.shell\", \"patterns\": [{ \"include\": \"#math\" }, { \"include\": \"#string\" }] }] }, \"array_access_inline\": { \"captures\": { \"1\": { \"name\": \"punctuation.section.array.shell\" }, \"2\": { \"patterns\": [{ \"include\": \"#special_expansion\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }] }, \"3\": { \"name\": \"punctuation.section.array.shell\" } }, \"match\": \"(?:(\\\\[)([^\\\\[\\\\]]+)(\\\\]))\" }, \"array_value\": { \"begin\": \"(?:[ \\\\t]*+)(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?))(?:(?:(=)|(\\\\+=))|(-=))(?:[ \\\\t]*+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.assignment.shell\" }, \"2\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"3\": { \"name\": \"variable.other.assignment.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"keyword.operator.assignment.shell\" }, \"7\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"8\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"9\": { \"name\": \"punctuation.definition.array.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.shell\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.assignment.array.shell entity.other.attribute-name.shell\" }, \"2\": { \"name\": \"keyword.operator.assignment.shell punctuation.definition.assignment.shell\" } }, \"match\": \"(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(=))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.bracket.named-array.shell\" }, \"2\": { \"name\": \"string.unquoted.shell entity.other.attribute-name.bracket.shell\" }, \"3\": { \"name\": \"punctuation.definition.bracket.named-array.shell\" }, \"4\": { \"name\": \"punctuation.definition.assignment.shell\" } }, \"match\": \"(?:(\\\\[)(.+?)(\\\\])(=))\" }, { \"include\": \"#normal_context\" }, { \"include\": \"#simple_unquoted\" }] }, \"assignment_statement\": { \"patterns\": [{ \"include\": \"#array_value\" }, { \"include\": \"#modified_assignment_statement\" }, { \"include\": \"#normal_assignment_statement\" }] }, \"basic_command_name\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.shell\" }, \"2\": { \"name\": \"entity.name.function.call.shell entity.name.command.shell\", \"patterns\": [{ \"match\": \"(?<!\\\\w)(?:continue|return|break)(?!\\\\w)\", \"name\": \"keyword.control.$0.shell\" }, { \"match\": \"(?<!\\\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\\\.)(?!\\\\/))(?!\\\\w)(?!-)\", \"name\": \"support.function.builtin.shell\" }, { \"include\": \"#variable\" }] } }, \"match\": `(?:(?:(?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$))(?:((?<=^|;|&|[ \\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\t]|;|&|$))|((?!\"|'|\\\\\\\\\\\\n?$)(?:[^!'\"<> \\\\t\\\\n\\\\r]+?)))(?:(?= |\\\\t)|(?:(?=;|\\\\||&|\\\\n|\\\\)|\\\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\))))`, \"name\": \"meta.statement.command.name.basic.shell\" }, \"block_comment\": { \"begin\": \"(?:(?:\\\\s*+)(\\\\/\\\\*))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.begin.shell\" } }, \"end\": \"\\\\*\\\\/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.shell\" } }, \"name\": \"comment.block.shell\" }, \"boolean\": { \"match\": \"\\\\b(?:true|false)\\\\b\", \"name\": \"constant.language.$0.shell\" }, \"case_statement\": { \"begin\": \"(?:(\\\\bcase\\\\b)(?:[ \\\\t]*+)(.+?)(?:[ \\\\t]*+)(\\\\bin\\\\b))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.case.shell\" }, \"2\": { \"patterns\": [{ \"include\": \"#initial_context\" }] }, \"3\": { \"name\": \"keyword.control.in.shell\" } }, \"end\": \"\\\\besac\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.esac.shell\" } }, \"name\": \"meta.case.shell\", \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.pattern.case.default.shell\" } }, \"match\": \"(?:[ \\\\t]*+)(\\\\* *\\\\))\" }, { \"begin\": \"(?<!\\\\))(?!(?:[ \\\\t]*+)(?:esac\\\\b|$))\", \"beginCaptures\": {}, \"end\": \"(?:(?=\\\\besac\\\\b)|(\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.pattern.case.shell\" } }, \"name\": \"meta.case.entry.pattern.shell\", \"patterns\": [{ \"include\": \"#case_statement_context\" }] }, { \"begin\": \"(?<=\\\\))\", \"beginCaptures\": {}, \"end\": \"(?:(;;)|(?=\\\\besac\\\\b))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.case.shell\" } }, \"name\": \"meta.case.entry.body.shell\", \"patterns\": [{ \"include\": \"#typical_statements\" }, { \"include\": \"#initial_context\" }] }] }, \"case_statement_context\": { \"patterns\": [{ \"match\": \"\\\\*\", \"name\": \"variable.language.special.quantifier.star.shell keyword.operator.quantifier.star.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\" }, { \"match\": \"\\\\+\", \"name\": \"variable.language.special.quantifier.plus.shell keyword.operator.quantifier.plus.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\" }, { \"match\": \"\\\\?\", \"name\": \"variable.language.special.quantifier.question.shell keyword.operator.quantifier.question.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\" }, { \"match\": \"@\", \"name\": \"variable.language.special.at.shell keyword.operator.at.shell punctuation.definition.regex.at.shell\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.orvariable.language.special.or.shell keyword.operator.alternation.ruby.shell punctuation.definition.regex.alternation.shell punctuation.separator.regex.alternation.shell\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.shell\" }, { \"match\": \"(?<=\\\\tin| in| |\\\\t|;;)\\\\(\", \"name\": \"keyword.operator.pattern.case.shell\" }, { \"begin\": \"(?<=\\\\S)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.shell punctuation.definition.regex.group.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.definition.regex.group.shell\" } }, \"name\": \"meta.parenthese.shell\", \"patterns\": [{ \"include\": \"#case_statement_context\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.shell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.character-class.shell\" } }, \"name\": \"string.regexp.character-class.shell\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.shell\" }] }, { \"include\": \"#string\" }, { \"match\": \"[^) \\\\t\\\\n\\\\[?\\\\*\\\\|\\\\@]\", \"name\": \"string.unquoted.pattern.shell string.regexp.unquoted.shell\" }] }, \"command_name_range\": { \"begin\": \"\\\\G\", \"beginCaptures\": {}, \"end\": \"(?:(?= |\\\\t|;|\\\\||&|$|\\\\n|\\\\)|\\\\`)|(?=<))\", \"endCaptures\": {}, \"name\": \"meta.statement.command.name.shell\", \"patterns\": [{ \"match\": \"(?<!\\\\w)(?:continue|return|break)(?!\\\\w)\", \"name\": \"entity.name.function.call.shell entity.name.command.shell keyword.control.$0.shell\" }, { \"match\": \"(?<!\\\\w)(?:(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|:|\\\\.)(?!\\\\/))(?!\\\\w)(?!-)\", \"name\": \"entity.name.function.call.shell entity.name.command.shell support.function.builtin.shell\" }, { \"include\": \"#variable\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.call.shell entity.name.command.shell\" } }, \"match\": `(?:(?<!\\\\w)(?<=\\\\G|'|\"|\\\\}|\\\\))([^ \\\\n\\\\t\\\\r\"'=;&\\\\|\\`){<>]+))` }, { \"begin\": `(?:(?:\\\\G|(?<! |\\\\t|;|\\\\||&|\\\\n|\\\\{|#))(?:(\\\\$?)((?:(\")|(')))))`, \"beginCaptures\": { \"1\": { \"name\": \"meta.statement.command.name.quoted.shell punctuation.definition.string.shell entity.name.function.call.shell entity.name.command.shell\" }, \"2\": {}, \"3\": { \"name\": \"meta.statement.command.name.quoted.shell string.quoted.double.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\" }, \"4\": { \"name\": \"meta.statement.command.name.quoted.shell string.quoted.single.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\" } }, \"end\": \"(?<!\\\\G)(?<=(?:\\\\2))\", \"endCaptures\": {}, \"patterns\": [{ \"include\": \"#continuation_of_single_quoted_command_name\" }, { \"include\": \"#continuation_of_double_quoted_command_name\" }] }, { \"include\": \"#line_continuation\" }, { \"include\": \"#simple_unquoted\" }] }, \"command_statement\": { \"begin\": \"(?:(?:[ \\\\t]*+)(?:(?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$)(?!\\\\\\\\\\\\n?$)))\", \"beginCaptures\": {}, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.command.shell\", \"patterns\": [{ \"include\": \"#command_name_range\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#option\" }, { \"include\": \"#argument\" }, { \"include\": \"#string\" }, { \"include\": \"#heredoc\" }] }, \"comment\": { \"captures\": { \"1\": { \"name\": \"comment.line.number-sign.shell meta.shebang.shell\" }, \"2\": { \"name\": \"punctuation.definition.comment.shebang.shell\" }, \"3\": { \"name\": \"comment.line.number-sign.shell\" }, \"4\": { \"name\": \"punctuation.definition.comment.shell\" } }, \"match\": \"(?:(?:^|(?:[ \\\\t]++))(?:((?:(#!)(?:.*)))|((?:(#)(?:.*)))))\" }, \"comments\": { \"patterns\": [{ \"include\": \"#block_comment\" }, { \"include\": \"#line_comment\" }] }, \"compound-command\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"name\": \"meta.scope.logical-expression.shell\", \"patterns\": [{ \"include\": \"#logical-expression\" }, { \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=\\\\s|^){(?=\\\\s|$)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell\" } }, \"end\": \"(?<=^|;)\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.shell\" } }, \"name\": \"meta.scope.group.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"continuation_of_double_quoted_command_name\": { \"begin\": '(?:\\\\G(?<=\"))', \"beginCaptures\": {}, \"contentName\": \"meta.statement.command.name.continuation string.quoted.double entity.name.function.call entity.name.command\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.double.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\" } }, \"patterns\": [{ \"match\": '\\\\\\\\[$\\\\n`\"\\\\\\\\]', \"name\": \"constant.character.escape.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, \"continuation_of_single_quoted_command_name\": { \"begin\": \"(?:\\\\G(?<='))\", \"beginCaptures\": {}, \"contentName\": \"meta.statement.command.name.continuation string.quoted.single entity.name.function.call entity.name.command\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.single.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\" } } }, \"custom_command_names\": { \"patterns\": [] }, \"custom_commands\": { \"patterns\": [] }, \"double_quote_context\": { \"patterns\": [{ \"match\": '\\\\\\\\[$`\"\\\\\\\\\\\\n]', \"name\": \"constant.character.escape.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, \"double_quote_escape_char\": { \"match\": '\\\\\\\\[$`\"\\\\\\\\\\\\n]', \"name\": \"constant.character.escape.shell\" }, \"floating_keyword\": { \"patterns\": [{ \"match\": \"(?<=^|;|&| |\\\\t)(?:then|elif|else|done|end|do|if|fi)(?= |\\\\t|;|&|$)\", \"name\": \"keyword.control.$0.shell\" }] }, \"for_statement\": { \"patterns\": [{ \"begin\": \"(?:(\\\\bfor\\\\b)(?:(?:[ \\\\t]*+)((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:[ \\\\t]*+)(\\\\bin\\\\b)))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.shell\" }, \"2\": { \"name\": \"variable.other.for.shell\" }, \"3\": { \"name\": \"keyword.control.in.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.for.in.shell\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#simple_unquoted\" }, { \"include\": \"#normal_context\" }] }, { \"begin\": \"(\\\\bfor\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.for.shell\", \"patterns\": [{ \"include\": \"#arithmetic_double\" }, { \"include\": \"#normal_context\" }] }] }, \"function_definition\": { \"applyEndPatternLast\": 1, \"begin\": `(?:[ \\\\t]*+)(?:(?:(\\\\bfunction\\\\b)(?:[ \\\\t]*+)([^ \\\\t\\\\n\\\\r()=\"']+)(?:(?:(\\\\()(?:[ \\\\t]*+)(\\\\)))?))|(?:([^ \\\\t\\\\n\\\\r()=\"']+)(?:[ \\\\t]*+)(\\\\()(?:[ \\\\t]*+)(\\\\))))`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.shell\" }, \"2\": { \"name\": \"entity.name.function.shell\" }, \"3\": { \"name\": \"punctuation.definition.arguments.shell\" }, \"4\": { \"name\": \"punctuation.definition.arguments.shell\" }, \"5\": { \"name\": \"entity.name.function.shell\" }, \"6\": { \"name\": \"punctuation.definition.arguments.shell\" }, \"7\": { \"name\": \"punctuation.definition.arguments.shell\" } }, \"end\": \"(?<=\\\\}|\\\\))\", \"endCaptures\": {}, \"name\": \"meta.function.shell\", \"patterns\": [{ \"match\": \"(?:\\\\G(?:\\\\t| |\\\\n))\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"name\": \"meta.function.body.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell punctuation.section.function.definition.shell\" } }, \"name\": \"meta.function.body.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"include\": \"#initial_context\" }] }, \"heredoc\": { \"patterns\": [{ \"begin\": `(?:((?<!<)(?:<<-))(?:[ \\\\t]*+)(\"|')(?:[ \\\\t]*+)([^\"']+?)(?=\\\\s|;|&|<|\"|')((?:\\\\2))(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"4\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"5\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.quoted.heredoc.indent.$3\", \"end\": \"(?:(?:^\\\\t*)(?:\\\\3)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.$0.shell\" } }, \"patterns\": [] }, { \"begin\": `(?:((?<!<)(?:<<)(?!<))(?:[ \\\\t]*+)(\"|')(?:[ \\\\t]*+)([^\"']+?)(?=\\\\s|;|&|<|\"|')((?:\\\\2))(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"4\": { \"name\": \"punctuation.definition.string.heredoc.quote.shell\" }, \"5\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.quoted.heredoc.no-indent.$3\", \"end\": \"(?:^(?:\\\\3)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" } }, \"patterns\": [] }, { \"begin\": `(?:((?<!<)(?:<<-))(?:[ \\\\t]*+)([^\"' \\\\t]+)(?=\\\\s|;|&|<|\"|')(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"3\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.unquoted.heredoc.indent.$2\", \"end\": \"(?:(?:^\\\\t*)(?:\\\\2)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" } }, \"patterns\": [{ \"include\": \"#double_quote_escape_char\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": `(?:((?<!<)(?:<<)(?!<))(?:[ \\\\t]*+)([^\"' \\\\t]+)(?=\\\\s|;|&|<|\"|')(.*))`, \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.shell\" }, \"2\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" }, \"3\": { \"patterns\": [{ \"include\": \"#redirect_fix\" }, { \"include\": \"#typical_statements\" }] } }, \"contentName\": \"string.unquoted.heredoc.no-indent.$2\", \"end\": \"(?:^(?:\\\\2)(?=\\\\s|;|&|$))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.heredoc.delimiter.shell\" } }, \"patterns\": [{ \"include\": \"#double_quote_escape_char\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }] }, \"herestring\": { \"patterns\": [{ \"begin\": \"(<<<)\\\\s*(('))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.herestring.shell\" }, \"2\": { \"name\": \"string.quoted.single.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"contentName\": \"string.quoted.single.shell\", \"end\": \"(')\", \"endCaptures\": { \"0\": { \"name\": \"string.quoted.single.shell\" }, \"1\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"meta.herestring.shell\" }, { \"begin\": '(<<<)\\\\s*((\"))', \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.herestring.shell\" }, \"2\": { \"name\": \"string.quoted.double.shell\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"contentName\": \"string.quoted.double.shell\", \"end\": '(\")', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.double.shell\" }, \"1\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"meta.herestring.shell\", \"patterns\": [{ \"include\": \"#double_quote_context\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.herestring.shell\" }, \"2\": { \"name\": \"string.unquoted.herestring.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] } }, \"match\": \"(<<<)\\\\s*(([^\\\\s)\\\\\\\\]|\\\\\\\\.)+)\", \"name\": \"meta.herestring.shell\" }] }, \"initial_context\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pipeline\" }, { \"include\": \"#normal_statement_seperator\" }, { \"include\": \"#logical_expression_double\" }, { \"include\": \"#logical_expression_single\" }, { \"include\": \"#assignment_statement\" }, { \"include\": \"#case_statement\" }, { \"include\": \"#for_statement\" }, { \"include\": \"#loop\" }, { \"include\": \"#function_definition\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#arithmetic_double\" }, { \"include\": \"#misc_ranges\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#redirection\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#alias_statement\" }, { \"include\": \"#normal_statement\" }, { \"include\": \"#string\" }, { \"include\": \"#support\" }] }, \"inline_comment\": { \"captures\": { \"1\": { \"name\": \"comment.block.shell punctuation.definition.comment.begin.shell\" }, \"2\": { \"name\": \"comment.block.shell\" }, \"3\": { \"patterns\": [{ \"match\": \"\\\\*\\\\/\", \"name\": \"comment.block.shell punctuation.definition.comment.end.shell\" }, { \"match\": \"\\\\*\", \"name\": \"comment.block.shell\" }] } }, \"match\": \"(\\\\/\\\\*)((?:(?:[^\\\\*]|(?:(?:\\\\*++)[^\\\\/]))*+)((?:(?:\\\\*++)\\\\/)))\" }, \"interpolation\": { \"patterns\": [{ \"include\": \"#arithmetic_dollar\" }, { \"include\": \"#subshell_dollar\" }, { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.evaluation.backticks.shell\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.evaluation.backticks.shell\" } }, \"name\": \"string.interpolated.backtick.shell\", \"patterns\": [{ \"match\": \"\\\\\\\\[`\\\\\\\\$]\", \"name\": \"constant.character.escape.shell\" }, { \"begin\": \"(?<=\\\\W)(?=#)(?!#{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.shell\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.shell\" } }, \"end\": \"(?=`)\", \"name\": \"comment.line.number-sign.shell\" }] }, { \"include\": \"#initial_context\" }] }] }, \"keyword\": { \"patterns\": [{ \"match\": \"(?<=^|;|&|\\\\s)(then|else|elif|fi|for|in|do|done|select|continue|esac|while|until|return)(?=\\\\s|;|&|$)\", \"name\": \"keyword.control.shell\" }, { \"match\": \"(?<=^|;|&|\\\\s)(?:export|declare|typeset|local|readonly)(?=\\\\s|;|&|$)\", \"name\": \"storage.modifier.shell\" }] }, \"line_comment\": { \"begin\": \"(?:\\\\s*+)(\\\\/\\\\/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.shell\" } }, \"end\": \"(?<=\\\\n)(?<!\\\\\\\\\\\\n)\", \"endCaptures\": {}, \"name\": \"comment.line.double-slash.shell\", \"patterns\": [{ \"include\": \"#line_continuation_character\" }] }, \"line_continuation\": { \"match\": \"\\\\\\\\(?=\\\\n)\", \"name\": \"constant.character.escape.line-continuation.shell\" }, \"logical-expression\": { \"patterns\": [{ \"include\": \"#arithmetic_no_dollar\" }, { \"comment\": \"do we want a special rule for ( expr )?\", \"match\": \"=[=~]?|!=?|<|>|&&|\\\\|\\\\|\", \"name\": \"keyword.operator.logical.shell\" }, { \"match\": \"(?<!\\\\S)-(nt|ot|ef|eq|ne|l[te]|g[te]|[a-hknoprstuwxzOGLSN])\\\\b\", \"name\": \"keyword.operator.logical.shell\" }] }, \"logical_expression_context\": { \"patterns\": [{ \"include\": \"#regex_comparison\" }, { \"include\": \"#arithmetic_no_dollar\" }, { \"include\": \"#logical-expression\" }, { \"include\": \"#logical_expression_single\" }, { \"include\": \"#logical_expression_double\" }, { \"include\": \"#comment\" }, { \"include\": \"#boolean\" }, { \"include\": \"#redirect_number\" }, { \"include\": \"#numeric_literal\" }, { \"include\": \"#pipeline\" }, { \"include\": \"#normal_statement_seperator\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#support\" }] }, \"logical_expression_double\": { \"begin\": \"\\\\[\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"end\": \"\\\\]\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"name\": \"meta.scope.logical-expression.shell\", \"patterns\": [{ \"include\": \"#logical_expression_context\" }] }, \"logical_expression_single\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.logical-expression.shell\" } }, \"name\": \"meta.scope.logical-expression.shell\", \"patterns\": [{ \"include\": \"#logical_expression_context\" }] }, \"loop\": { \"patterns\": [{ \"begin\": \"(?<=^|;|&|\\\\s)(for)\\\\s+(.+?)\\\\s+(in)(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" }, \"2\": { \"name\": \"variable.other.loop.shell\", \"patterns\": [{ \"include\": \"#string\" }] }, \"3\": { \"name\": \"keyword.control.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)done(?=\\\\s|;|&|$|\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.shell\" } }, \"name\": \"meta.scope.for-in-loop.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=^|;|&|\\\\s)(while|until)(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)done(?=\\\\s|;|&|$|\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.shell\" } }, \"name\": \"meta.scope.while-loop.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=^|;|&|\\\\s)(select)\\\\s+((?:[^\\\\s\\\\\\\\]|\\\\\\\\.)+)(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" }, \"2\": { \"name\": \"variable.other.loop.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)(done)(?=\\\\s|;|&|$|\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.shell\" } }, \"name\": \"meta.scope.select-block.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"begin\": \"(?<=^|;|&|\\\\s)if(?=\\\\s|;|&|$)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.if.shell\" } }, \"end\": \"(?<=^|;|&|\\\\s)fi(?=\\\\s|;|&|$)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.fi.shell\" } }, \"name\": \"meta.scope.if-block.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"math\": { \"patterns\": [{ \"include\": \"#variable\" }, { \"match\": \"\\\\+{1,2}|-{1,2}|!|~|\\\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\\\|{1,2}|&{1,2}|\\\\?|:|,|=|[*/%+\\\\-&^|]=|<<=|>>=\", \"name\": \"keyword.operator.arithmetic.shell\" }, { \"match\": \"0[xX][0-9A-Fa-f]+\", \"name\": \"constant.numeric.hex.shell\" }, { \"match\": \";\", \"name\": \"punctuation.separator.semicolon.range\" }, { \"match\": \"0\\\\d+\", \"name\": \"constant.numeric.octal.shell\" }, { \"match\": \"\\\\d{1,2}#[0-9a-zA-Z@_]+\", \"name\": \"constant.numeric.other.shell\" }, { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.integer.shell\" }, { \"match\": \"(?<!\\\\w)(?:[a-zA-Z_0-9]+)(?!\\\\w)\", \"name\": \"variable.other.normal.shell\" }] }, \"math_operators\": { \"patterns\": [{ \"match\": \"\\\\+{1,2}|-{1,2}|!|~|\\\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|^|\\\\|{1,2}|&{1,2}|\\\\?|:|,|=|[*/%+\\\\-&^|]=|<<=|>>=\", \"name\": \"keyword.operator.arithmetic.shell\" }, { \"match\": \"0[xX][0-9A-Fa-f]+\", \"name\": \"constant.numeric.hex.shell\" }, { \"match\": \"0\\\\d+\", \"name\": \"constant.numeric.octal.shell\" }, { \"match\": \"\\\\d{1,2}#[0-9a-zA-Z@_]+\", \"name\": \"constant.numeric.other.shell\" }, { \"match\": \"\\\\d+\", \"name\": \"constant.numeric.integer.shell\" }] }, \"misc_ranges\": { \"patterns\": [{ \"include\": \"#logical_expression_single\" }, { \"include\": \"#logical_expression_double\" }, { \"include\": \"#subshell_dollar\" }, { \"begin\": \"(?<![^ \\\\t])({)(?!\\\\w|\\\\$)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.shell\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.shell\" } }, \"name\": \"meta.scope.group.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"modified_assignment_statement\": { \"begin\": \"(?<=^|;|&|[ \\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\t]|;|&|$)\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.$0.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.shell meta.expression.assignment.modified.shell\", \"patterns\": [{ \"match\": \"(?<!\\\\w)-\\\\w+\\\\b\", \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" }, { \"include\": \"#array_value\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.assignment.shell\" }, \"2\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"3\": { \"name\": \"variable.other.assignment.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"keyword.operator.assignment.shell\" }, \"7\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"8\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"9\": { \"name\": \"constant.numeric.shell constant.numeric.hex.shell\" }, \"10\": { \"name\": \"constant.numeric.shell constant.numeric.octal.shell\" }, \"11\": { \"name\": \"constant.numeric.shell constant.numeric.other.shell\" }, \"12\": { \"name\": \"constant.numeric.shell constant.numeric.decimal.shell\" }, \"13\": { \"name\": \"constant.numeric.shell constant.numeric.version.shell\" }, \"14\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" } }, \"match\": \"(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?)(?:(?:(?:(=)|(\\\\+=))|(-=))?)(?:(?:(?<==| |\\\\t|^|\\\\{|\\\\(|\\\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\\\d+))|(\\\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\\\d+(?:\\\\.\\\\d+)))|(-?\\\\d+(?:\\\\.\\\\d+)+))|(-?\\\\d+))(?= |\\\\t|$|\\\\}|\\\\)|;))?))\" }, { \"include\": \"#normal_context\" }] }, \"modifiers\": { \"match\": \"(?<=^|;|&|[ \\\\t])(?:readonly|declare|typeset|export|local)(?=[ \\\\t]|;|&|$)\", \"name\": \"storage.modifier.$0.shell\" }, \"normal_assignment_statement\": { \"begin\": \"(?:[ \\\\t]*+)(?:((?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))(?:(?:(\\\\[)((?:(?:(?:(?:\\\\$?)(?:(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w))|@)|\\\\*)|(-?\\\\d+)))(\\\\]))?))(?:(?:(=)|(\\\\+=))|(-=))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.assignment.shell\" }, \"2\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"3\": { \"name\": \"variable.other.assignment.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" }, \"5\": { \"name\": \"punctuation.definition.array.access.shell\" }, \"6\": { \"name\": \"keyword.operator.assignment.shell\" }, \"7\": { \"name\": \"keyword.operator.assignment.compound.shell\" }, \"8\": { \"name\": \"keyword.operator.assignment.compound.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.expression.assignment.shell\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#normal_assignment_statement\" }, { \"begin\": \"(?<= |\\\\t)(?! |\\\\t|\\\\w+=)\", \"beginCaptures\": {}, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.command.env.shell\", \"patterns\": [{ \"include\": \"#command_name_range\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#option\" }, { \"include\": \"#argument\" }, { \"include\": \"#string\" }] }, { \"include\": \"#simple_unquoted\" }, { \"include\": \"#normal_context\" }] }, \"normal_context\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pipeline\" }, { \"include\": \"#normal_statement_seperator\" }, { \"include\": \"#misc_ranges\" }, { \"include\": \"#boolean\" }, { \"include\": \"#redirect_number\" }, { \"include\": \"#numeric_literal\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#redirection\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#support\" }, { \"include\": \"#parenthese\" }] }, \"normal_statement\": { \"begin\": \"(?:(?!^[ \\\\t]*+$)(?:(?<=^until | until |\\\\tuntil |^while | while |\\\\twhile |^elif | elif |\\\\telif |^else | else |\\\\telse |^then | then |\\\\tthen |^do | do |\\\\tdo |^if | if |\\\\tif )|(?<=(?:^|;|\\\\||&|!|\\\\(|\\\\{|\\\\`)))(?:[ \\\\t]*+)(?!nocorrect\\\\W|nocorrect\\\\$|function\\\\W|function\\\\$|foreach\\\\W|foreach\\\\$|repeat\\\\W|repeat\\\\$|logout\\\\W|logout\\\\$|coproc\\\\W|coproc\\\\$|select\\\\W|select\\\\$|while\\\\W|while\\\\$|pushd\\\\W|pushd\\\\$|until\\\\W|until\\\\$|case\\\\W|case\\\\$|done\\\\W|done\\\\$|elif\\\\W|elif\\\\$|else\\\\W|else\\\\$|esac\\\\W|esac\\\\$|popd\\\\W|popd\\\\$|then\\\\W|then\\\\$|time\\\\W|time\\\\$|for\\\\W|for\\\\$|end\\\\W|end\\\\$|fi\\\\W|fi\\\\$|do\\\\W|do\\\\$|in\\\\W|in\\\\$|if\\\\W|if\\\\$))\", \"beginCaptures\": {}, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.statement.shell\", \"patterns\": [{ \"include\": \"#typical_statements\" }] }, \"normal_statement_seperator\": { \"captures\": { \"1\": { \"name\": \"punctuation.terminator.statement.semicolon.shell\" }, \"2\": { \"name\": \"punctuation.separator.statement.and.shell\" }, \"3\": { \"name\": \"punctuation.separator.statement.or.shell\" }, \"4\": { \"name\": \"punctuation.separator.statement.background.shell\" } }, \"match\": \"(?:(?:(?:(;)|(&&))|(\\\\|\\\\|))|(&))\" }, \"numeric_literal\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.shell constant.numeric.hex.shell\" }, \"2\": { \"name\": \"constant.numeric.shell constant.numeric.octal.shell\" }, \"3\": { \"name\": \"constant.numeric.shell constant.numeric.other.shell\" }, \"4\": { \"name\": \"constant.numeric.shell constant.numeric.decimal.shell\" }, \"5\": { \"name\": \"constant.numeric.shell constant.numeric.version.shell\" }, \"6\": { \"name\": \"constant.numeric.shell constant.numeric.integer.shell\" } }, \"match\": \"(?<==| |\\\\t|^|\\\\{|\\\\(|\\\\[)(?:(?:(?:(?:(?:(0[xX][0-9A-Fa-f]+)|(0\\\\d+))|(\\\\d{1,2}#[0-9a-zA-Z@_]+))|(-?\\\\d+(?:\\\\.\\\\d+)))|(-?\\\\d+(?:\\\\.\\\\d+)+))|(-?\\\\d+))(?= |\\\\t|$|\\\\}|\\\\)|;)\" }, \"option\": { \"begin\": \"(?:(?:[ \\\\t]++)(-)((?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))))\", \"beginCaptures\": { \"1\": { \"name\": \"string.unquoted.argument.shell constant.other.option.dash.shell\" }, \"2\": { \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" } }, \"contentName\": \"string.unquoted.argument constant.other.option\", \"end\": \"(?:(?=[ \\\\t])|(?:(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)))\", \"endCaptures\": {}, \"patterns\": [{ \"include\": \"#option_context\" }] }, \"option_context\": { \"patterns\": [{ \"include\": \"#misc_ranges\" }, { \"include\": \"#string\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#herestring\" }, { \"include\": \"#redirection\" }, { \"include\": \"#pathname\" }, { \"include\": \"#floating_keyword\" }, { \"include\": \"#support\" }] }, \"parenthese\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parenthese.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parenthese.shell\" } }, \"name\": \"meta.parenthese.group.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"pathname\": { \"patterns\": [{ \"match\": \"(?<=\\\\s|:|=|^)~\", \"name\": \"keyword.operator.tilde.shell\" }, { \"match\": \"\\\\*|\\\\?\", \"name\": \"keyword.operator.glob.shell\" }, { \"begin\": \"([?*+@!])(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.extglob.shell\" }, \"2\": { \"name\": \"punctuation.definition.extglob.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.extglob.shell\" } }, \"name\": \"meta.structure.extglob.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }] }, \"pipeline\": { \"patterns\": [{ \"match\": \"(?<=^|;|&|\\\\s)(time)(?=\\\\s|;|&|$)\", \"name\": \"keyword.other.shell\" }, { \"match\": \"[|!]\", \"name\": \"keyword.operator.pipe.shell\" }] }, \"redirect_fix\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.redirect.shell\" }, \"2\": { \"name\": \"string.unquoted.argument.shell\" } }, \"match\": \"(?:(>>?)(?:[ \\\\t]*+)([^ \\\\t\\\\n>&;<>()$`\\\\\\\\\\\"'<\\\\|]+))\" }, \"redirect_number\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.redirect.stdout.shell\" }, \"2\": { \"name\": \"keyword.operator.redirect.stderr.shell\" }, \"3\": { \"name\": \"keyword.operator.redirect.$3.shell\" } }, \"match\": \"(?<=[ \\\\t])(?:(?:(1)|(2)|(\\\\d+))(?=>))\" }, \"redirection\": { \"patterns\": [{ \"begin\": \"[><]\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.interpolated.process-substitution.shell\", \"patterns\": [{ \"include\": \"#initial_context\" }] }, { \"match\": \"(?<![<>])(&>|\\\\d*>&\\\\d*|\\\\d*(>>|>|<)|\\\\d*<&|\\\\d*<>)(?![<>])\", \"name\": \"keyword.operator.redirect.shell\" }] }, \"regex_comparison\": { \"match\": \"=~\", \"name\": \"keyword.operator.logical.regex.shell\" }, \"regexp\": { \"patterns\": [{ \"match\": \"(?:.+)\" }] }, \"simple_options\": { \"captures\": { \"0\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.argument.shell constant.other.option.dash.shell\" }, \"2\": { \"name\": \"string.unquoted.argument.shell constant.other.option.shell\" } }, \"match\": \"(?:[ \\\\t]++)(-)(\\\\w+)\" }] } }, \"match\": \"(?:(?:[ \\\\t]++)-(?:\\\\w+))*\" }, \"simple_unquoted\": { \"match\": \"[^ \\\\t\\\\n>&;<>()$`\\\\\\\\\\\"'<\\\\|]\", \"name\": \"string.unquoted.shell\" }, \"special_expansion\": { \"match\": \"!|:[-=?]?|\\\\*|@|##|#|%%|%|\\\\/\", \"name\": \"keyword.operator.expansion.shell\" }, \"start_of_command\": { \"match\": \"(?:(?:[ \\\\t]*+)(?:(?!(?:!|&|\\\\||\\\\(|\\\\)|\\\\{|\\\\[|<|>|#|\\\\n|$|;|[ \\\\t]))(?!nocorrect |nocorrect\t|nocorrect$|readonly |readonly\t|readonly$|function |function\t|function$|foreach |foreach\t|foreach$|coproc |coproc\t|coproc$|logout |logout\t|logout$|export |export\t|export$|select |select\t|select$|repeat |repeat\t|repeat$|pushd |pushd\t|pushd$|until |until\t|until$|while |while\t|while$|local |local\t|local$|case |case\t|case$|done |done\t|done$|elif |elif\t|elif$|else |else\t|else$|esac |esac\t|esac$|popd |popd\t|popd$|then |then\t|then$|time |time\t|time$|for |for\t|for$|end |end\t|end$|fi |fi\t|fi$|do |do\t|do$|in |in\t|in$|if |if\t|if$)(?!\\\\\\\\\\\\n?$)))\" }, \"string\": { \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.shell\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.quoted.single.shell\" }, { \"begin\": '\\\\$?\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.quoted.double.shell\", \"patterns\": [{ \"match\": '\\\\\\\\[$\\\\n`\"\\\\\\\\]', \"name\": \"constant.character.escape.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"\\\\$'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.shell\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.shell\" } }, \"name\": \"string.quoted.single.dollar.shell\", \"patterns\": [{ \"match\": \"\\\\\\\\(?:a|b|e|f|n|r|t|v|\\\\\\\\|')\", \"name\": \"constant.character.escape.ansi-c.shell\" }, { \"match\": '\\\\\\\\\\\\d{3}\"', \"name\": \"constant.character.escape.octal.shell\" }, { \"match\": '\\\\\\\\x[0-9a-fA-F]{2}\"', \"name\": \"constant.character.escape.hex.shell\" }, { \"match\": '\\\\\\\\c.\"', \"name\": \"constant.character.escape.control-char.shell\" }] }] }, \"subshell_dollar\": { \"patterns\": [{ \"begin\": \"(?:\\\\$\\\\()\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.subshell.single.shell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.subshell.single.shell\" } }, \"name\": \"meta.scope.subshell\", \"patterns\": [{ \"include\": \"#parenthese\" }, { \"include\": \"#initial_context\" }] }] }, \"support\": { \"patterns\": [{ \"match\": \"(?<=^|;|&|\\\\s)(?::|\\\\.)(?=\\\\s|;|&|$)\", \"name\": \"support.function.builtin.shell\" }] }, \"typical_statements\": { \"patterns\": [{ \"include\": \"#assignment_statement\" }, { \"include\": \"#case_statement\" }, { \"include\": \"#for_statement\" }, { \"include\": \"#while_statement\" }, { \"include\": \"#function_definition\" }, { \"include\": \"#command_statement\" }, { \"include\": \"#line_continuation\" }, { \"include\": \"#arithmetic_double\" }, { \"include\": \"#normal_context\" }] }, \"variable\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.parameter.positional.all.shell\" }, \"2\": { \"name\": \"variable.parameter.positional.all.shell\" } }, \"match\": \"(?:(\\\\$)(\\\\@(?!\\\\w)))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.parameter.positional.shell\" }, \"2\": { \"name\": \"variable.parameter.positional.shell\" } }, \"match\": \"(?:(\\\\$)(\\\\d(?!\\\\w)))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.language.special.shell\" }, \"2\": { \"name\": \"variable.language.special.shell\" } }, \"match\": \"(?:(\\\\$)([-*#?$!0_](?!\\\\w)))\" }, { \"begin\": \"(?:(\\\\$)(\\\\{)(?:[ \\\\t]*+)(?=\\\\d))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.parameter.positional.shell\" }, \"2\": { \"name\": \"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell variable.parameter.positional.shell\" } }, \"contentName\": \"meta.parameter-expansion\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell variable.parameter.positional.shell\" } }, \"patterns\": [{ \"include\": \"#special_expansion\" }, { \"include\": \"#array_access_inline\" }, { \"match\": \"\\\\d+\", \"name\": \"variable.parameter.positional.shell\" }, { \"match\": \"(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w)\", \"name\": \"variable.other.normal.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?:(\\\\$)(\\\\{))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell\" }, \"2\": { \"name\": \"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell\" } }, \"contentName\": \"meta.parameter-expansion\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell\" } }, \"patterns\": [{ \"include\": \"#special_expansion\" }, { \"include\": \"#array_access_inline\" }, { \"match\": \"(?<!\\\\w)(?:[a-zA-Z_0-9-]+)(?!\\\\w)\", \"name\": \"variable.other.normal.shell\" }, { \"include\": \"#variable\" }, { \"include\": \"#string\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.shell variable.other.normal.shell\" }, \"2\": { \"name\": \"variable.other.normal.shell\" } }, \"match\": \"(?:(\\\\$)((?:\\\\w+)(?!\\\\w)))\" }] }, \"while_statement\": { \"patterns\": [{ \"begin\": \"(\\\\bwhile\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.while.shell\" } }, \"end\": \"(?=;|\\\\||&|\\\\n|\\\\)|\\\\`|\\\\{|\\\\}|[ \\\\t]*#|\\\\])(?<!\\\\\\\\)\", \"endCaptures\": {}, \"name\": \"meta.while.shell\", \"patterns\": [{ \"include\": \"#line_continuation\" }, { \"include\": \"#math_operators\" }, { \"include\": \"#option\" }, { \"include\": \"#simple_unquoted\" }, { \"include\": \"#normal_context\" }, { \"include\": \"#string\" }] }] } }, \"scopeName\": \"source.shell\", \"aliases\": [\"bash\", \"sh\", \"shell\", \"zsh\"] });\nvar shellscript = [\n  lang\n];\n\nexport { shellscript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,QAAQ;IAAe,YAAY;QAAC;YAAE,WAAW;QAAmB;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,SAAS;YAA2P,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,QAAQ;wBAA6D;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,MAAM;oBAAE,QAAQ;gBAA6C;gBAAG,MAAM;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAqD,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAA0C,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA+C,iBAAiB,CAAC;YAAG,OAAO;YAAmC,eAAe,CAAC;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;4BAAkC,YAAY;gCAAC;oCAAE,SAAS;oCAAO,QAAQ;gCAA2C;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAmB;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAA6B;oCAAE;oCAAG,SAAS;gCAAwC;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAAyD;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAkB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,SAAS;QAA6B;QAAG,eAAe;YAAE,SAAS;YAA4L,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0E;wBAAG,KAAK;4BAAE,QAAQ;wBAA4E;oBAAE;oBAAG,SAAS;gBAA6C;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkE;wBAAG,KAAK;4BAAE,QAAQ;wBAAmD;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAyB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAA+B;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;oBAA6D,YAAY;wBAAC;4BAAE,SAAS;4BAA4C,QAAQ;wBAA2B;wBAAG;4BAAE,SAAS;4BAA2W,QAAQ;wBAAiC;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;YAAE;YAAG,SAAS,CAAC,qyBAAqyB,CAAC;YAAE,QAAQ;QAA0C;QAAG,iBAAiB;YAAE,SAAS;YAAyB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,QAAQ;QAAsB;QAAG,WAAW;YAAE,SAAS;YAAwB,QAAQ;QAA6B;QAAG,kBAAkB;YAAE,SAAS;YAA2D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAc,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,SAAS;gBAAyB;gBAAG;oBAAE,SAAS;oBAAyC,iBAAiB,CAAC;oBAAG,OAAO;oBAA4B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,iBAAiB,CAAC;oBAAG,OAAO;oBAA2B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;gBAAmM;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmM;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA2M;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAqG;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6L;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAAsC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8E;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8E;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAkC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA6D;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAO,iBAAiB,CAAC;YAAG,OAAO;YAA6C,eAAe,CAAC;YAAG,QAAQ;YAAqC,YAAY;gBAAC;oBAAE,SAAS;oBAA4C,QAAQ;gBAAqF;gBAAG;oBAAE,SAAS;oBAA2W,QAAQ;gBAA2F;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,SAAS,CAAC,8DAA8D,CAAC;gBAAC;gBAAG;oBAAE,SAAS,CAAC,+DAA+D,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyI;wBAAG,KAAK,CAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0K;wBAAG,KAAK;4BAAE,QAAQ;wBAA0K;oBAAE;oBAAG,OAAO;oBAAwB,eAAe,CAAC;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA8C;wBAAG;4BAAE,WAAW;wBAA8C;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA8nB,iBAAiB,CAAC;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;gBAAG,KAAK;oBAAE,QAAQ;gBAA+C;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,SAAS;QAA6D;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,QAAQ;oBAAuC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,8CAA8C;YAAE,SAAS;YAAiB,iBAAiB,CAAC;YAAG,eAAe;YAA+G,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+H;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,8CAA8C;YAAE,SAAS;YAAiB,iBAAiB,CAAC;YAAG,eAAe;YAA+G,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+H;YAAE;QAAE;QAAG,wBAAwB;YAAE,YAAY,EAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY,EAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,4BAA4B;YAAE,SAAS;YAAoB,QAAQ;QAAkC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuE,QAAQ;gBAA2B;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAkB,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,uBAAuB;YAAG,SAAS,CAAC,gKAAgK,CAAC;YAAE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAgB,eAAe,CAAC;YAAG,QAAQ;YAAuB,YAAY;gBAAC;oBAAE,SAAS;gBAAuB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmF;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,uFAAuF,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAmC,OAAO;oBAAqC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,YAAY,EAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,2FAA2F,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAsC,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,YAAY,EAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,gEAAgE,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAqC,OAAO;oBAAqC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA4B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS,CAAC,oEAAoE,CAAC;oBAAE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAgB;gCAAG;oCAAE,WAAW;gCAAsB;6BAAE;wBAAC;oBAAE;oBAAG,eAAe;oBAAwC,OAAO;oBAA6B,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAA4B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA8B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,eAAe;oBAA8B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAyB,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;4BAAoC,YAAY;gCAAC;oCAAE,WAAW;gCAAmB;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;oBAAmC,QAAQ;gBAAwB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiE;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAU,QAAQ;wBAA+D;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAAsB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAmE;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,QAAQ;oBAAsC,YAAY;wBAAC;4BAAE,SAAS;4BAAgB,QAAQ;wBAAkC;wBAAG;4BAAE,SAAS;4BAAuB,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA+C;4BAAE;4BAAG,OAAO;4BAAW,YAAY;gCAAC;oCAAE,SAAS;oCAAK,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAuC;oCAAE;oCAAG,OAAO;oCAAS,QAAQ;gCAAiC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyG,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAwE,QAAQ;gBAAyB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAwB,eAAe,CAAC;YAAG,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAA+B;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAe,QAAQ;QAAoD;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;oBAA2C,SAAS;oBAA4B,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAAiC;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAU,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;4BAA6B,YAAY;gCAAC;oCAAE,WAAW;gCAAU;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAuC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA4C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAuC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAyC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAiC,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA0G,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAoC,QAAQ;gBAA8B;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0G,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAiC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,iCAAiC;YAAE,SAAS;YAA8E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAkE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAA6D;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwD;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,MAAM;4BAAE,QAAQ;wBAAsD;wBAAG,MAAM;4BAAE,QAAQ;wBAAsD;wBAAG,MAAM;4BAAE,QAAQ;wBAAwD;wBAAG,MAAM;4BAAE,QAAQ;wBAAwD;wBAAG,MAAM;4BAAE,QAAQ;wBAAwD;oBAAE;oBAAG,SAAS;gBAAsV;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAA8E,QAAQ;QAA4B;QAAG,+BAA+B;YAAE,SAAS;YAA2K,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;gBAAG,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,SAAS;oBAA6B,iBAAiB,CAAC;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAmoB,iBAAiB,CAAC;YAAG,OAAO;YAAyD,eAAe,CAAC;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAA2C;gBAAG,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,SAAS;QAAoC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAAsD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;gBAAG,KAAK;oBAAE,QAAQ;gBAAwD;YAAE;YAAG,SAAS;QAA6K;QAAG,UAAU;YAAE,SAAS;YAA6E,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAkE;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,eAAe;YAAkD,OAAO;YAA4E,eAAe,CAAC;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqC,QAAQ;gBAAsB;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA8B;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAyD;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,SAAS;QAAyC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAkD,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA+D,QAAQ;gBAAkC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAM,QAAQ;QAAuC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;gBAAS;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAkE;gCAAG,KAAK;oCAAE,QAAQ;gCAA6D;4BAAE;4BAAG,SAAS;wBAAwB;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAA6B;QAAG,mBAAmB;YAAE,SAAS;YAAkC,QAAQ;QAAwB;QAAG,qBAAqB;YAAE,SAAS;YAAiC,QAAQ;QAAmC;QAAG,oBAAoB;YAAE,SAAS;QAA6nB;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAoB,QAAQ;wBAAkC;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,SAAS;4BAAkC,QAAQ;wBAAyC;wBAAG;4BAAE,SAAS;4BAAe,QAAQ;wBAAwC;wBAAG;4BAAE,SAAS;4BAAwB,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAA+C;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,QAAQ;oBAAuB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwC,QAAQ;gBAAiC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgF;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4E;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAAwB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwE;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,SAAS;oBAAqC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4E;wBAAG,KAAK;4BAAE,QAAQ;wBAAmI;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiI;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,SAAS;4BAAQ,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+F;oBAAE;oBAAG,eAAe;oBAA4B,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6F;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoE;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAA6B;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAyD,eAAe,CAAC;oBAAG,QAAQ;oBAAoB,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;IAAgB,WAAW;QAAC;QAAQ;QAAM;QAAS;KAAM;AAAC;AAC3u2C,IAAI,cAAc;IAChB;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/json.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON\", \"name\": \"json\", \"patterns\": [{ \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json\" } }, \"name\": \"meta.structure.array.json\", \"patterns\": [{ \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.json\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line.double-slash.js\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null)\\\\b\", \"name\": \"constant.language.json\" }, \"number\": { \"match\": \"-?(?:0|[1-9]\\\\d*)(?:(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)?\", \"name\": \"constant.numeric.json\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json\" } }, \"name\": \"meta.structure.dictionary.json\", \"patterns\": [{ \"comment\": \"the JSON object key\", \"include\": \"#objectkey\" }, { \"include\": \"#comments\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json\" } }, \"name\": \"meta.structure.dictionary.value.json\", \"patterns\": [{ \"comment\": \"the JSON object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json\" }] }, { \"match\": \"[^\\\\s}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json\" }] }, \"objectkey\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.begin.json\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.end.json\" } }, \"name\": \"string.json support.type.property-name.json\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json\" } }, \"name\": \"string.quoted.double.json\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"stringcontent\": { \"patterns\": [{ \"match\": '\\\\\\\\(?:[\"\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})', \"name\": \"constant.character.escape.json\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json\" }] }, \"value\": { \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.json\" });\nvar json = [\n  lang\n];\n\nexport { json as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAgD;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAqB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAA+B;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA6B,QAAQ;QAAyB;QAAG,UAAU;YAAE,SAAS;YAAwD,QAAQ;QAAwB;QAAG,UAAU;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,WAAW;oBAAuB,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,OAAO;oBAAe,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;4BAAyB,WAAW;wBAAS;wBAAG;4BAAE,SAAS;4BAAW,QAAQ;wBAAqD;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAqD;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAA+C,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAkD;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AACxiG,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/java.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Java\", \"name\": \"java\", \"patterns\": [{ \"begin\": \"\\\\b(package)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.package.java\" } }, \"contentName\": \"storage.modifier.package.java\", \"end\": \"\\\\s*(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.java\" } }, \"name\": \"meta.package.java\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"(?<=\\\\.)\\\\s*\\\\.|\\\\.(?=\\\\s*;)\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"(?<!_)_(?=\\\\s*(\\\\.|;))|\\\\b\\\\d+|-+\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"[A-Z]+\", \"name\": \"invalid.deprecated.package_name_not_lowercase.java\" }, { \"match\": \"\\\\b(?<!\\\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\\\b\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.java\" }] }, { \"begin\": \"\\\\b(import)\\\\b\\\\s*\\\\b(static)?\\\\b\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.import.java\" }, \"2\": { \"name\": \"storage.modifier.java\" } }, \"contentName\": \"storage.modifier.import.java\", \"end\": \"\\\\s*(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.java\" } }, \"name\": \"meta.import.java\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"(?<=\\\\.)\\\\s*\\\\.|\\\\.(?=\\\\s*;)\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"(?<!\\\\.)\\\\s*\\\\*\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"(?<!_)_(?=\\\\s*(\\\\.|;))|\\\\b\\\\d+|-+\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"\\\\b(?<!\\\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\\\b\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.java\" }, { \"match\": \"\\\\*\", \"name\": \"variable.language.wildcard.java\" }] }, { \"include\": \"#comments-javadoc\" }, { \"include\": \"#code\" }, { \"include\": \"#module\" }], \"repository\": { \"all-types\": { \"patterns\": [{ \"include\": \"#primitive-arrays\" }, { \"include\": \"#primitive-types\" }, { \"include\": \"#object-types\" }] }, \"annotations\": { \"patterns\": [{ \"begin\": \"((@)\\\\s*([^\\\\s(]+))(\\\\()\", \"beginCaptures\": { \"2\": { \"name\": \"punctuation.definition.annotation.java\" }, \"3\": { \"name\": \"storage.type.annotation.java\" }, \"4\": { \"name\": \"punctuation.definition.annotation-arguments.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.annotation-arguments.end.bracket.round.java\" } }, \"name\": \"meta.declaration.annotation.java\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.other.key.java\" }, \"2\": { \"name\": \"keyword.operator.assignment.java\" } }, \"match\": \"(\\\\w*)\\\\s*(=)\" }, { \"include\": \"#code\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.annotation.java\" }, \"2\": { \"name\": \"storage.modifier.java\" }, \"3\": { \"name\": \"storage.type.annotation.java\" }, \"5\": { \"name\": \"punctuation.definition.annotation.java\" }, \"6\": { \"name\": \"storage.type.annotation.java\" } }, \"match\": \"(@)(interface)\\\\s+(\\\\w*)|((@)\\\\s*(\\\\w+))\", \"name\": \"meta.declaration.annotation.java\" }] }, \"anonymous-block-and-instance-initializer\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.block.begin.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.block.end.bracket.curly.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, \"anonymous-classes-and-new\": { \"begin\": \"\\\\bnew\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.new.java\" } }, \"end\": \"(?=;|\\\\)|\\\\]|\\\\.|,|\\\\?|:|}|\\\\+|-|\\\\*|\\\\/(?!\\\\/|\\\\*)|%|!|&|\\\\||\\\\^|=)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#function-call\" }, { \"include\": \"#all-types\" }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(?=;|\\\\)|\\\\]|\\\\.|,|\\\\?|:|}|\\\\+|-|\\\\*|\\\\/(?!\\\\/|\\\\*)|%|!|&|\\\\||\\\\^|=)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.inner-class.begin.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.inner-class.end.bracket.curly.java\" } }, \"name\": \"meta.inner-class.java\", \"patterns\": [{ \"include\": \"#class-body\" }] }] }, { \"begin\": \"(?<=\\\\])\", \"end\": \"(?=;|\\\\)|\\\\]|\\\\.|,|\\\\?|:|}|\\\\+|-|\\\\*|\\\\/(?!\\\\/|\\\\*)|%|!|&|\\\\||\\\\^|=)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array-initializer.begin.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array-initializer.end.bracket.curly.java\" } }, \"name\": \"meta.array-initializer.java\", \"patterns\": [{ \"include\": \"#code\" }] }] }, { \"include\": \"#parens\" }] }, \"assertions\": { \"patterns\": [{ \"begin\": \"\\\\b(assert)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.assert.java\" } }, \"end\": \"$\", \"name\": \"meta.declaration.assertion.java\", \"patterns\": [{ \"match\": \":\", \"name\": \"keyword.operator.assert.expression-separator.java\" }, { \"include\": \"#code\" }] }] }, \"class\": { \"begin\": \"(?=\\\\w?[\\\\w\\\\s-]*\\\\b(?:class|(?<!@)interface|enum)\\\\s+[\\\\w$]+)\", \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.class.end.bracket.curly.java\" } }, \"name\": \"meta.class.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"include\": \"#generics\" }, { \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.java\" }, \"2\": { \"name\": \"entity.name.type.class.java\" } }, \"match\": \"(class|(?<!@)interface|enum)\\\\s+([\\\\w$]+)\", \"name\": \"meta.class.identifier.java\" }, { \"begin\": \"extends\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.extends.java\" } }, \"end\": \"(?={|implements|permits)\", \"name\": \"meta.definition.class.inherited.classes.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"(implements)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.implements.java\" } }, \"end\": \"(?=\\\\s*extends|permits|\\\\{)\", \"name\": \"meta.definition.class.implemented.interfaces.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"(permits)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.permits.java\" } }, \"end\": \"(?=\\\\s*extends|implements|\\\\{)\", \"name\": \"meta.definition.class.permits.classes.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.class.begin.bracket.curly.java\" } }, \"contentName\": \"meta.class.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#class-body\" }] }] }, \"class-body\": { \"patterns\": [{ \"include\": \"#comments-javadoc\" }, { \"include\": \"#comments\" }, { \"include\": \"#enums\" }, { \"include\": \"#class\" }, { \"include\": \"#generics\" }, { \"include\": \"#static-initializer\" }, { \"include\": \"#class-fields-and-methods\" }, { \"include\": \"#annotations\" }, { \"include\": \"#storage-modifiers\" }, { \"include\": \"#member-variables\" }, { \"include\": \"#code\" }] }, \"class-fields-and-methods\": { \"patterns\": [{ \"begin\": \"(?==)\", \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"include\": \"#methods\" }] }, \"code\": { \"patterns\": [{ \"include\": \"#annotations\" }, { \"include\": \"#comments\" }, { \"include\": \"#enums\" }, { \"include\": \"#class\" }, { \"include\": \"#record\" }, { \"include\": \"#anonymous-block-and-instance-initializer\" }, { \"include\": \"#try-catch-finally\" }, { \"include\": \"#assertions\" }, { \"include\": \"#parens\" }, { \"include\": \"#constants-and-special-vars\" }, { \"include\": \"#numbers\" }, { \"include\": \"#anonymous-classes-and-new\" }, { \"include\": \"#lambda-expression\" }, { \"include\": \"#keywords\" }, { \"include\": \"#storage-modifiers\" }, { \"include\": \"#method-call\" }, { \"include\": \"#function-call\" }, { \"include\": \"#variables\" }, { \"include\": \"#variables-local\" }, { \"include\": \"#objects\" }, { \"include\": \"#properties\" }, { \"include\": \"#strings\" }, { \"include\": \"#all-types\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }, { \"match\": \";\", \"name\": \"punctuation.terminator.java\" }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.empty.java\" }, { \"include\": \"#comments-inline\" }] }, \"comments-inline\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.java\" }, { \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.java\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-slash.java\" }] }] }, \"comments-javadoc\": { \"patterns\": [{ \"begin\": \"^\\\\s*(/\\\\*\\\\*)(?!/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.java\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"name\": \"comment.block.javadoc.java\", \"patterns\": [{ \"match\": \"@(author|deprecated|return|see|serial|since|version)\\\\b\", \"name\": \"keyword.other.documentation.javadoc.java\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.javadoc.java\" }, \"2\": { \"name\": \"variable.parameter.java\" } }, \"match\": \"(@param)\\\\s+(\\\\S+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.javadoc.java\" }, \"2\": { \"name\": \"entity.name.type.class.java\" } }, \"match\": \"(@(?:exception|throws))\\\\s+(\\\\S+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.javadoc.java\" }, \"2\": { \"name\": \"entity.name.type.class.java\" }, \"3\": { \"name\": \"variable.parameter.java\" } }, \"match\": \"{(@link)\\\\s+(\\\\S+)?#([\\\\w$]+\\\\s*\\\\([^()]*\\\\)).*?}\" }] }] }, \"constants-and-special-vars\": { \"patterns\": [{ \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.java\" }, { \"match\": \"\\\\bthis\\\\b\", \"name\": \"variable.language.this.java\" }, { \"match\": \"\\\\bsuper\\\\b\", \"name\": \"variable.language.java\" }] }, \"enums\": { \"begin\": \"^\\\\s*([\\\\w\\\\s]*)(enum)\\\\s+(\\\\w+)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#storage-modifiers\" }] }, \"2\": { \"name\": \"storage.modifier.java\" }, \"3\": { \"name\": \"entity.name.type.enum.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.enum.end.bracket.curly.java\" } }, \"name\": \"meta.enum.java\", \"patterns\": [{ \"begin\": \"\\\\b(extends)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.extends.java\" } }, \"end\": \"(?={|\\\\bimplements\\\\b)\", \"name\": \"meta.definition.class.inherited.classes.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"\\\\b(implements)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.implements.java\" } }, \"end\": \"(?={|\\\\bextends\\\\b)\", \"name\": \"meta.definition.class.implemented.interfaces.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.enum.begin.bracket.curly.java\" } }, \"end\": \"(?=})\", \"patterns\": [{ \"begin\": \"(?<={)\", \"end\": \"(?=;|})\", \"patterns\": [{ \"include\": \"#comments-javadoc\" }, { \"include\": \"#comments\" }, { \"begin\": \"\\\\b(\\\\w+)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"constant.other.enum.java\" } }, \"end\": \"(,)|(?=;|})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.delimiter.java\" } }, \"patterns\": [{ \"include\": \"#comments-javadoc\" }, { \"include\": \"#comments\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"patterns\": [{ \"include\": \"#class-body\" }] }] }] }, { \"include\": \"#class-body\" }] }] }, \"function-call\": { \"begin\": \"([A-Za-z_$][\\\\w$]*)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.java\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.function-call.java\", \"patterns\": [{ \"include\": \"#code\" }] }, \"generics\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.angle.java\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.angle.java\" } }, \"patterns\": [{ \"match\": \"\\\\b(extends|super)\\\\b\", \"name\": \"storage.modifier.$1.java\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.java\" } }, \"match\": \"(?<!\\\\.)([a-zA-Z$_][a-zA-Z0-9$_]*)(?=\\\\s*<)\" }, { \"include\": \"#primitive-arrays\" }, { \"match\": \"[a-zA-Z$_][a-zA-Z0-9$_]*\", \"name\": \"storage.type.generic.java\" }, { \"match\": \"\\\\?\", \"name\": \"storage.type.generic.wildcard.java\" }, { \"match\": \"&\", \"name\": \"punctuation.separator.types.java\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }, { \"include\": \"#parens\" }, { \"include\": \"#generics\" }, { \"include\": \"#comments\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\bthrow\\\\b\", \"name\": \"keyword.control.throw.java\" }, { \"match\": \"\\\\?|:\", \"name\": \"keyword.control.ternary.java\" }, { \"match\": \"\\\\b(return|yield|break|case|continue|default|do|while|for|switch|if|else)\\\\b\", \"name\": \"keyword.control.java\" }, { \"match\": \"\\\\b(instanceof)\\\\b\", \"name\": \"keyword.operator.instanceof.java\" }, { \"match\": \"(<<|>>>?|~|\\\\^)\", \"name\": \"keyword.operator.bitwise.java\" }, { \"match\": \"((&|\\\\^|\\\\||<<|>>>?)=)\", \"name\": \"keyword.operator.assignment.bitwise.java\" }, { \"match\": \"(===?|!=|<=|>=|<>|<|>)\", \"name\": \"keyword.operator.comparison.java\" }, { \"match\": \"([+*/%-]=)\", \"name\": \"keyword.operator.assignment.arithmetic.java\" }, { \"match\": \"(=)\", \"name\": \"keyword.operator.assignment.java\" }, { \"match\": \"(--|\\\\+\\\\+)\", \"name\": \"keyword.operator.increment-decrement.java\" }, { \"match\": \"(-|\\\\+|\\\\*|\\\\/|%)\", \"name\": \"keyword.operator.arithmetic.java\" }, { \"match\": \"(!|&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.java\" }, { \"match\": \"(\\\\||&)\", \"name\": \"keyword.operator.bitwise.java\" }, { \"match\": \"\\\\b(const|goto)\\\\b\", \"name\": \"keyword.reserved.java\" }] }, \"lambda-expression\": { \"patterns\": [{ \"match\": \"->\", \"name\": \"storage.type.function.arrow.java\" }] }, \"member-variables\": { \"begin\": \"(?=private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)\", \"end\": \"(?==|;)\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"include\": \"#variables\" }, { \"include\": \"#primitive-arrays\" }, { \"include\": \"#object-types\" }] }, \"method-call\": { \"begin\": \"(\\\\.)\\\\s*([A-Za-z_$][\\\\w$]*)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"entity.name.function.java\" }, \"3\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.method-call.java\", \"patterns\": [{ \"include\": \"#code\" }] }, \"methods\": { \"begin\": \"(?!new)(?=[\\\\w<].*\\\\s+)(?=([^=/]|/(?!/))+\\\\()\", \"end\": \"(})|(?=;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.method.end.bracket.curly.java\" } }, \"name\": \"meta.method.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"begin\": \"(\\\\w+)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.java\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.method.identifier.java\", \"patterns\": [{ \"include\": \"#parameters\" }, { \"include\": \"#parens\" }, { \"include\": \"#comments\" }] }, { \"include\": \"#generics\" }, { \"begin\": \"(?=\\\\w.*\\\\s+\\\\w+\\\\s*\\\\()\", \"end\": \"(?=\\\\s+\\\\w+\\\\s*\\\\()\", \"name\": \"meta.method.return-type.java\", \"patterns\": [{ \"include\": \"#all-types\" }, { \"include\": \"#parens\" }, { \"include\": \"#comments\" }] }, { \"include\": \"#throws\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.method.begin.bracket.curly.java\" } }, \"contentName\": \"meta.method.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"include\": \"#comments\" }] }, \"module\": { \"begin\": \"((open)\\\\s)?(module)\\\\s+(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.java\" }, \"3\": { \"name\": \"storage.modifier.java\" }, \"4\": { \"name\": \"entity.name.type.module.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.module.end.bracket.curly.java\" } }, \"name\": \"meta.module.java\", \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.module.begin.bracket.curly.java\" } }, \"contentName\": \"meta.module.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#comments-javadoc\" }, { \"match\": \"\\\\b(requires|transitive|exports|opens|to|uses|provides|with)\\\\b\", \"name\": \"keyword.module.java\" }] }] }, \"numbers\": { \"patterns\": [{ \"match\": \"\\\\b(?<!\\\\$)0(x|X)((?<!\\\\.)[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?[Ll]?(?!\\\\.)|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?\\\\.?|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)?\\\\.[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)[Pp][+-]?\\\\d([0-9_]*\\\\d)?[FfDd]?)\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.java\" }, { \"match\": \"\\\\b(?<!\\\\$)0(b|B)[01]([01_]*[01])?[Ll]?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.binary.java\" }, { \"match\": \"\\\\b(?<!\\\\$)0[0-7]([0-7_]*[0-7])?[Ll]?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.octal.java\" }, { \"match\": \"(?<!\\\\$)(\\\\b\\\\d([0-9_]*\\\\d)?\\\\.\\\\B(?!\\\\.)|\\\\b\\\\d([0-9_]*\\\\d)?\\\\.([Ee][+-]?\\\\d([0-9_]*\\\\d)?)[FfDd]?\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?\\\\.([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?\\\\.(\\\\d([0-9_]*\\\\d)?)([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]?\\\\b|(?<!\\\\.)\\\\B\\\\.\\\\d([0-9_]*\\\\d)?([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]?\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?([Ee][+-]?\\\\d([0-9_]*\\\\d)?)[FfDd]?\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]\\\\b|\\\\b(0|[1-9]([0-9_]*\\\\d)?)(?!\\\\.)[Ll]?\\\\b)(?!\\\\$)\", \"name\": \"constant.numeric.decimal.java\" }] }, \"object-types\": { \"patterns\": [{ \"include\": \"#generics\" }, { \"begin\": \"\\\\b((?:[A-Za-z_]\\\\w*\\\\s*\\\\.\\\\s*)*)([A-Z_]\\\\w*)\\\\s*(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"[A-Za-z_]\\\\w*\", \"name\": \"storage.type.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }] }, \"2\": { \"name\": \"storage.type.object.array.java\" } }, \"end\": \"(?!\\\\s*\\\\[)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#parens\" }] }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"[A-Za-z_]\\\\w*\", \"name\": \"storage.type.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }] } }, \"match\": \"\\\\b((?:[A-Za-z_]\\\\w*\\\\s*\\\\.\\\\s*)*[A-Z_]\\\\w*)\\\\s*(?=<)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"[A-Za-z_]\\\\w*\", \"name\": \"storage.type.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }] } }, \"match\": \"\\\\b((?:[A-Za-z_]\\\\w*\\\\s*\\\\.\\\\s*)*[A-Z_]\\\\w*)\\\\b((?=\\\\s*[A-Za-z$_\\\\n])|(?=\\\\s*\\\\.\\\\.\\\\.))\" }] }, \"object-types-inherited\": { \"patterns\": [{ \"include\": \"#generics\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" } }, \"match\": \"\\\\b(?:[A-Z]\\\\w*\\\\s*(\\\\.)\\\\s*)*[A-Z]\\\\w*\\\\b\", \"name\": \"entity.other.inherited-class.java\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }] }, \"objects\": { \"match\": \"(?<![\\\\w$])[a-zA-Z_$][\\\\w$]*(?=\\\\s*\\\\.\\\\s*[\\\\w$]+)\", \"name\": \"variable.other.object.java\" }, \"parameters\": { \"patterns\": [{ \"match\": \"\\\\bfinal\\\\b\", \"name\": \"storage.modifier.java\" }, { \"include\": \"#annotations\" }, { \"include\": \"#all-types\" }, { \"include\": \"#strings\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.java\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"punctuation.definition.parameters.varargs.java\" }] }, \"parens\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.square.java\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.square.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }] }, \"primitive-arrays\": { \"patterns\": [{ \"begin\": \"\\\\b(void|boolean|byte|char|short|int|float|long|double)\\\\b\\\\s*(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.primitive.array.java\" } }, \"end\": \"(?!\\\\s*\\\\[)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#parens\" }] }] }, \"primitive-types\": { \"match\": \"\\\\b(void|boolean|byte|char|short|int|float|long|double)\\\\b\", \"name\": \"storage.type.primitive.java\" }, \"properties\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"keyword.control.new.java\" } }, \"match\": \"(\\\\.)\\\\s*(new)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"variable.other.object.property.java\" } }, \"match\": \"(\\\\.)\\\\s*([a-zA-Z_$][\\\\w$]*)(?=\\\\s*\\\\.\\\\s*[a-zA-Z_$][\\\\w$]*)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"variable.other.object.property.java\" } }, \"match\": \"(\\\\.)\\\\s*([a-zA-Z_$][\\\\w$]*)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"invalid.illegal.identifier.java\" } }, \"match\": \"(\\\\.)\\\\s*(\\\\d[\\\\w$]*)\" }] }, \"record\": { \"begin\": \"(?=\\\\w?[\\\\w\\\\s]*\\\\b(?:record)\\\\s+[\\\\w$]+)\", \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.class.end.bracket.curly.java\" } }, \"name\": \"meta.record.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"include\": \"#generics\" }, { \"include\": \"#comments\" }, { \"begin\": \"(record)\\\\s+([\\\\w$]+)(<[\\\\w$]+>)?(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.java\" }, \"2\": { \"name\": \"entity.name.type.record.java\" }, \"3\": { \"patterns\": [{ \"include\": \"#generics\" }] }, \"4\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.record.identifier.java\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"(implements)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.implements.java\" } }, \"end\": \"(?=\\\\s*\\\\{)\", \"name\": \"meta.definition.class.implemented.interfaces.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"include\": \"#record-body\" }] }, \"record-body\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.class.begin.bracket.curly.java\" } }, \"end\": \"(?=})\", \"name\": \"meta.record.body.java\", \"patterns\": [{ \"include\": \"#record-constructor\" }, { \"include\": \"#class-body\" }] }, \"record-constructor\": { \"begin\": \"(?!new)(?=[\\\\w<].*\\\\s+)(?=([^(=/]|/(?!/))+(?={))\", \"end\": \"(})|(?=;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.method.end.bracket.curly.java\" } }, \"name\": \"meta.method.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"begin\": \"(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.java\" } }, \"end\": \"(?=\\\\s*{)\", \"name\": \"meta.method.identifier.java\", \"patterns\": [{ \"include\": \"#comments\" }] }, { \"include\": \"#comments\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.method.begin.bracket.curly.java\" } }, \"contentName\": \"meta.method.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"static-initializer\": { \"patterns\": [{ \"include\": \"#anonymous-block-and-instance-initializer\" }, { \"match\": \"static\", \"name\": \"storage.modifier.java\" }] }, \"storage-modifiers\": { \"match\": \"\\\\b(public|private|protected|static|final|native|synchronized|abstract|threadsafe|transient|volatile|default|strictfp|sealed|non-sealed)\\\\b\", \"name\": \"storage.modifier.java\" }, \"strings\": { \"patterns\": [{ \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.java\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.java\" } }, \"name\": \"string.quoted.triple.java\", \"patterns\": [{ \"match\": '(\\\\\\\\\"\"\")(?!\")|(\\\\\\\\.)', \"name\": \"constant.character.escape.java\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.java\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.java\" } }, \"name\": \"string.quoted.double.java\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.java\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.java\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.java\" } }, \"name\": \"string.quoted.single.java\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.java\" }] }] }, \"throws\": { \"begin\": \"throws\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.java\" } }, \"end\": \"(?={|;)\", \"name\": \"meta.throwables.java\", \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"[a-zA-Z$_][\\\\.a-zA-Z0-9$_]*\", \"name\": \"storage.type.java\" }, { \"include\": \"#comments\" }] }, \"try-catch-finally\": { \"patterns\": [{ \"begin\": \"\\\\btry\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.try.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.try.end.bracket.curly.java\" } }, \"name\": \"meta.try.java\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.try.resources.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.try.resources.end.bracket.round.java\" } }, \"name\": \"meta.try.resources.java\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.try.begin.bracket.curly.java\" } }, \"contentName\": \"meta.try.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, { \"begin\": \"\\\\b(catch)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.catch.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.catch.end.bracket.curly.java\" } }, \"name\": \"meta.catch.java\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"contentName\": \"meta.catch.parameters.java\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#storage-modifiers\" }, { \"begin\": \"[a-zA-Z$_][\\\\.a-zA-Z0-9$_]*\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.java\" } }, \"end\": \"(\\\\|)|(?=\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.catch.separator.java\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"0\": { \"name\": \"variable.parameter.java\" } }, \"match\": \"\\\\w+\" }] }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.catch.begin.bracket.curly.java\" } }, \"contentName\": \"meta.catch.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, { \"begin\": \"\\\\bfinally\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.finally.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.finally.end.bracket.curly.java\" } }, \"name\": \"meta.finally.java\", \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.finally.begin.bracket.curly.java\" } }, \"contentName\": \"meta.finally.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }] }, \"variables\": { \"begin\": \"(?=\\\\b((void|boolean|byte|char|short|int|float|long|double)|(?>(\\\\w+\\\\.)*[A-Z_]+\\\\w*))\\\\b\\\\s*(<[\\\\w<>,\\\\.?\\\\s\\\\[\\\\]]*>)?\\\\s*((\\\\[\\\\])*)?\\\\s+[A-Za-z_$][\\\\w$]*([\\\\w\\\\[\\\\],$][\\\\w\\\\[\\\\],\\\\s]*)?\\\\s*(=|:|;))\", \"end\": \"(?==|:|;)\", \"name\": \"meta.definition.variable.java\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.definition.java\" } }, \"match\": \"([A-Za-z$_][\\\\w$]*)(?=\\\\s*(\\\\[\\\\])*\\\\s*(;|:|=|,))\" }, { \"include\": \"#all-types\" }, { \"include\": \"#code\" }] }, \"variables-local\": { \"begin\": \"(?=\\\\b(var)\\\\b\\\\s+[A-Za-z_$][\\\\w$]*\\\\s*(=|:|;))\", \"end\": \"(?==|:|;)\", \"name\": \"meta.definition.variable.local.java\", \"patterns\": [{ \"match\": \"\\\\bvar\\\\b\", \"name\": \"storage.type.local.java\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.definition.java\" } }, \"match\": \"([A-Za-z$_][\\\\w$]*)(?=\\\\s*(\\\\[\\\\])*\\\\s*(=|:|;))\" }, { \"include\": \"#code\" }] } }, \"scopeName\": \"source.java\" });\nvar java = [\n  lang\n];\n\nexport { java as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,eAAe;YAAiC,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAqZ,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAwC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,eAAe;YAAgC,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAqZ,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAkC;aAAE;QAAC;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAU;KAAE;IAAE,cAAc;QAAE,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuE;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqE;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS;wBAAgB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;oBAA4C,QAAQ;gBAAmC;aAAE;QAAC;QAAG,4CAA4C;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAwE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAAwE,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAyD;4BAAE;4BAAG,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAAwE,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiE;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA+D;4BAAE;4BAAG,QAAQ;4BAA+B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAK,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,SAAS;4BAAK,QAAQ;wBAAoD;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAkE,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;oBAA6C,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA4B,QAAQ;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAA+B,QAAQ;oBAAqD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAkC,QAAQ;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA4C;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA8B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAY,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAiC;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAA2D,QAAQ;wBAA2C;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;4BAAE;4BAAG,SAAS;wBAAqB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAA8B;4BAAE;4BAAG,SAAS;wBAAoC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;4BAAE;4BAAG,SAAS;wBAAoD;qBAAE;gBAAC;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAyB;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA0B,QAAQ;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAuB,QAAQ;oBAAqD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,SAAS;4BAAU,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,SAAS;oCAAgB,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA2B;oCAAE;oCAAG,OAAO;oCAAe,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAuC;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,WAAW;wCAAoB;wCAAG;4CAAE,WAAW;wCAAY;wCAAG;4CAAE,SAAS;4CAAO,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAK,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,OAAO;4CAAK,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAgC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;gBAA8C;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAgF,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA8C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAwB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAAmC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA+F,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAiD,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA4B,OAAO;oBAAuB,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,eAAe;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAkC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,eAAe;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,SAAS;4BAAmE,QAAQ;wBAAsB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2P,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAqD,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAmD,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAA4d,QAAQ;gBAAgC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA6D,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiB,QAAQ;gCAAoB;gCAAG;oCAAE,SAAS;oCAAO,QAAQ;gCAAoC;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiB,QAAQ;gCAAoB;gCAAG;oCAAE,SAAS;oCAAO,QAAQ;gCAAoC;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAAwD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiB,QAAQ;gCAAoB;gCAAG;oCAAE,SAAS;oCAAO,QAAQ;gCAAoC;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAA2F;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA8C,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAsD,QAAQ;QAA6B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAiD;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA8D,QAAQ;QAA8B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAiB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA+D;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAAwB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAA6C,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA0C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAe,QAAQ;oBAAqD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,OAAO;YAAS,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAoD,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,eAAe;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA4C;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAwB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA+I,QAAQ;QAAwB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAA0B,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAW,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6D;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmD;4BAAE;4BAAG,eAAe;4BAAsB,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6D;4BAAE;4BAAG,eAAe;4BAA8B,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,SAAS;oCAA+B,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAoB;oCAAE;oCAAG,OAAO;oCAAiB,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,WAAW;wCAAY;wCAAG;4CAAE,YAAY;gDAAE,KAAK;oDAAE,QAAQ;gDAA0B;4CAAE;4CAAG,SAAS;wCAAO;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,eAAe;4BAAwB,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,eAAe;4BAA0B,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAA6M,OAAO;YAAa,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAmD,OAAO;YAAa,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,SAAS;oBAAa,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAkD;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AAC594B,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/xml.mjs"], "sourcesContent": ["import java from './java.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"XML\", \"name\": \"xml\", \"patterns\": [{ \"begin\": \"(<\\\\?)\\\\s*([-_a-zA-Z0-9]+)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.xml\" } }, \"end\": \"(\\\\?>)\", \"name\": \"meta.tag.preprocessor.xml\", \"patterns\": [{ \"match\": \" ([a-zA-Z-]+)\", \"name\": \"entity.other.attribute-name.xml\" }, { \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] }, { \"begin\": \"(<!)(DOCTYPE)\\\\s+([:a-zA-Z_][:a-zA-Z0-9_.-]*)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"keyword.other.doctype.xml\" }, \"3\": { \"name\": \"variable.language.documentroot.xml\" } }, \"end\": \"\\\\s*(>)\", \"name\": \"meta.tag.sgml.doctype.xml\", \"patterns\": [{ \"include\": \"#internalSubset\" }] }, { \"include\": \"#comments\" }, { \"begin\": \"(<)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(?=(\\\\s[^>]*)?></\\\\2>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.xml\" }, \"3\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(>)(</)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"punctuation.definition.tag.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"5\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"6\": { \"name\": \"entity.name.tag.localname.xml\" }, \"7\": { \"name\": \"punctuation.definition.tag.xml\" } }, \"name\": \"meta.tag.no-content.xml\", \"patterns\": [{ \"include\": \"#tagStuff\" }] }, { \"begin\": \"(</?)(?:([-\\\\w\\\\.]+)((:)))?([-\\\\w\\\\.:]+)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(/?>)\", \"name\": \"meta.tag.xml\", \"patterns\": [{ \"include\": \"#tagStuff\" }] }, { \"include\": \"#entity\" }, { \"include\": \"#bare-ampersand\" }, { \"begin\": \"<%@\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.xml\" } }, \"end\": \"%>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.xml\" } }, \"name\": \"source.java-props.embedded.xml\", \"patterns\": [{ \"match\": \"page|include|taglib\", \"name\": \"keyword.other.page-props.xml\" }] }, { \"begin\": \"<%[!=]?(?!--)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.xml\" } }, \"end\": \"(?!--)%>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.xml\" } }, \"name\": \"source.java.embedded.xml\", \"patterns\": [{ \"include\": \"source.java\" }] }, { \"begin\": \"<!\\\\[CDATA\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"]]>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.unquoted.cdata.xml\" }], \"repository\": { \"EntityDecl\": { \"begin\": \"(<!)(ENTITY)\\\\s+(%\\\\s+)?([:a-zA-Z_][:a-zA-Z0-9_.-]*)(\\\\s+(?:SYSTEM|PUBLIC)\\\\s+)?\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"keyword.other.entity.xml\" }, \"3\": { \"name\": \"punctuation.definition.entity.xml\" }, \"4\": { \"name\": \"variable.language.entity.xml\" }, \"5\": { \"name\": \"keyword.other.entitytype.xml\" } }, \"end\": \"(>)\", \"patterns\": [{ \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] }, \"bare-ampersand\": { \"match\": \"&\", \"name\": \"invalid.illegal.bad-ampersand.xml\" }, \"comments\": { \"patterns\": [{ \"begin\": \"<%--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.xml\" }, \"end\": \"--%>\", \"name\": \"comment.block.xml\" } }, { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.xml\" } }, \"end\": \"-->\", \"name\": \"comment.block.xml\", \"patterns\": [{ \"begin\": \"--(?!>)\", \"captures\": { \"0\": { \"name\": \"invalid.illegal.bad-comments-or-CDATA.xml\" } } }] }] }, \"doublequotedString\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.double.xml\", \"patterns\": [{ \"include\": \"#entity\" }, { \"include\": \"#bare-ampersand\" }] }, \"entity\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" }, \"3\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"match\": \"(&)([:a-zA-Z_][:a-zA-Z0-9_.-]*|#\\\\d+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.xml\" }, \"internalSubset\": { \"begin\": \"(\\\\[)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"end\": \"(\\\\])\", \"name\": \"meta.internalsubset.xml\", \"patterns\": [{ \"include\": \"#EntityDecl\" }, { \"include\": \"#parameterEntity\" }, { \"include\": \"#comments\" }] }, \"parameterEntity\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" }, \"3\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"match\": \"(%)([:a-zA-Z_][:a-zA-Z0-9_.-]*)(;)\", \"name\": \"constant.character.parameter-entity.xml\" }, \"singlequotedString\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.single.xml\", \"patterns\": [{ \"include\": \"#entity\" }, { \"include\": \"#bare-ampersand\" }] }, \"tagStuff\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.xml\" }, \"2\": { \"name\": \"entity.other.attribute-name.xml\" }, \"3\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"4\": { \"name\": \"entity.other.attribute-name.localname.xml\" } }, \"match\": \"(?:^|\\\\s+)(?:([-\\\\w.]+)((:)))?([-\\\\w.:]+)\\\\s*=\" }, { \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] } }, \"scopeName\": \"text.xml\", \"embeddedLangs\": [\"java\"] });\nvar xml = [\n  ...java,\n  lang\n];\n\nexport { xml as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAA8B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAU,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiD,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAW,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAwE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAyD,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,SAAS;YAA4C,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAS,QAAQ;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAM,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,QAAQ;gBAA+B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAY,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA4B;KAAE;IAAE,cAAc;QAAE,cAAc;YAAE,SAAS;YAAoF,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,QAAQ;QAAoC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,OAAO;wBAAQ,QAAQ;oBAAoB;gBAAE;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,SAAS;4BAAW,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;4BAAE;wBAAE;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA2D,QAAQ;QAAgC;QAAG,kBAAkB;YAAE,SAAS;YAAS,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAS,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAsC,QAAQ;QAA0C;QAAG,sBAAsB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;gBAAiD;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAY,iBAAiB;QAAC;KAAO;AAAC;AAC1tL,IAAI,MAAM;OACL,kMAAA,CAAA,UAAI;IACP;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/graphql.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport jsx from './jsx.mjs';\nimport tsx from './tsx.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"GraphQL\", \"fileTypes\": [\"graphql\", \"graphqls\", \"gql\", \"graphcool\"], \"name\": \"graphql\", \"patterns\": [{ \"include\": \"#graphql\" }], \"repository\": { \"graphql\": { \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-fragment-definition\" }, { \"include\": \"#graphql-directive-definition\" }, { \"include\": \"#graphql-type-interface\" }, { \"include\": \"#graphql-enum\" }, { \"include\": \"#graphql-scalar\" }, { \"include\": \"#graphql-union\" }, { \"include\": \"#graphql-schema\" }, { \"include\": \"#graphql-operation-def\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-ampersand\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.graphql\" } }, \"match\": \"\\\\s*(&)\" }, \"graphql-arguments\": { \"begin\": \"\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.round.directive.graphql\" } }, \"end\": \"\\\\s*(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.round.directive.graphql\" } }, \"name\": \"meta.arguments.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\\\s*(:))\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.graphql\" }, \"2\": { \"name\": \"punctuation.colon.graphql\" } }, \"end\": \"(?=\\\\s*(?:(?:([_A-Za-z][_0-9A-Za-z]*)\\\\s*(:))|\\\\)))|\\\\s*(,)\", \"endCaptures\": { \"3\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-value\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-boolean-value\": { \"captures\": { \"1\": { \"name\": \"constant.language.boolean.graphql\" } }, \"match\": \"\\\\s*\\\\b(true|false)\\\\b\" }, \"graphql-colon\": { \"captures\": { \"1\": { \"name\": \"punctuation.colon.graphql\" } }, \"match\": \"\\\\s*(:)\" }, \"graphql-comma\": { \"captures\": { \"1\": { \"name\": \"punctuation.comma.graphql\" } }, \"match\": \"\\\\s*(,)\" }, \"graphql-comment\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"comment\": \"need to prefix comment space with a scope else Atom's reflow cmd doesn't work\", \"match\": \"(\\\\s*)(#).*\", \"name\": \"comment.line.graphql.js\" }, { \"begin\": '(\"\"\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"end\": '(\"\"\")', \"name\": \"comment.line.graphql.js\" }, { \"begin\": '(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.graphql\" } }, \"end\": '(\")', \"name\": \"comment.line.graphql.js\" }] }, \"graphql-description-docstring\": { \"begin\": '\"\"\"', \"end\": '\"\"\"', \"name\": \"comment.block.graphql\" }, \"graphql-description-singleline\": { \"match\": '#(?=([^\"]*\"[^\"]*\")*[^\"]*$).*$', \"name\": \"comment.line.number-sign.graphql\" }, \"graphql-directive\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*((@)\\\\s*([_A-Za-z][_0-9A-Za-z]*))\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.directive.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-arguments\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-directive-definition\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\bdirective\\\\b)\\\\s*(@[_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.directive.graphql\" }, \"2\": { \"name\": \"entity.name.function.directive.graphql\" }, \"3\": { \"name\": \"keyword.on.graphql\" }, \"4\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-variable-definitions\" }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\bon\\\\b)\\\\s*([_A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.on.graphql\" }, \"2\": { \"name\": \"support.type.location.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"captures\": { \"2\": { \"name\": \"support.type.location.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\\\\s*([_A-Za-z]*)\" }] }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-enum\": { \"begin\": \"\\\\s*+\\\\b(enum)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.enum.graphql\" }, \"2\": { \"name\": \"support.type.enum.graphql\" } }, \"end\": \"(?<=})\", \"name\": \"meta.enum.graphql\", \"patterns\": [{ \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.type.object.graphql\", \"patterns\": [{ \"include\": \"#graphql-object-type\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-enum-value\" }, { \"include\": \"#literal-quasi-embedded\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }] }, \"graphql-enum-value\": { \"match\": \"\\\\s*(?!=\\\\b(true|false|null)\\\\b)([_A-Za-z][_0-9A-Za-z]*)\", \"name\": \"constant.character.enum.graphql\" }, \"graphql-field\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"string.unquoted.alias.graphql\" }, \"2\": { \"name\": \"punctuation.colon.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\\\\s*(:)\" }, { \"captures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-arguments\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-float-value\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.float.graphql\" } }, \"match\": \"\\\\s*(-?(0|[1-9]\\\\d*)(\\\\.\\\\d+)?((e|E)(\\\\+|-)?\\\\d+)?)\" }, \"graphql-fragment-definition\": { \"begin\": \"\\\\s*(?:(\\\\bfragment\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*)?\\\\s*(?:(\\\\bon\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*)))\", \"captures\": { \"1\": { \"name\": \"keyword.fragment.graphql\" }, \"2\": { \"name\": \"entity.name.fragment.graphql\" }, \"3\": { \"name\": \"keyword.on.graphql\" }, \"4\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?<=})\", \"name\": \"meta.fragment.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-fragment-spread\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?!\\\\bon\\\\b)([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"keyword.operator.spread.graphql\" }, \"2\": { \"name\": \"variable.fragment.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-ignore-spaces\": { \"match\": \"\\\\s*\" }, \"graphql-inline-fragment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?:(\\\\bon\\\\b)\\\\s*([_A-Za-z][_0-9A-Za-z]*))?\", \"captures\": { \"1\": { \"name\": \"keyword.operator.spread.graphql\" }, \"2\": { \"name\": \"keyword.on.graphql\" }, \"3\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-selection-set\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-input-types\": { \"patterns\": [{ \"include\": \"#graphql-scalar-type\" }, { \"captures\": { \"1\": { \"name\": \"support.type.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?:\\\\s*(!))?\" }, { \"begin\": \"\\\\s*(\\\\[)\", \"captures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"end\": \"\\\\s*(\\\\])(?:\\\\s*(!))?\", \"name\": \"meta.type.list.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#graphql-comma\" }, { \"include\": \"#literal-quasi-embedded\" }] }] }, \"graphql-list-value\": { \"patterns\": [{ \"begin\": \"\\\\s*+(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" } }, \"end\": \"\\\\s*(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.square.graphql\" } }, \"name\": \"meta.listvalues.graphql\", \"patterns\": [{ \"include\": \"#graphql-value\" }] }] }, \"graphql-name\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, \"graphql-null-value\": { \"captures\": { \"1\": { \"name\": \"constant.language.null.graphql\" } }, \"match\": \"\\\\s*\\\\b(null)\\\\b\" }, \"graphql-object-field\": { \"captures\": { \"1\": { \"name\": \"constant.object.key.graphql\" }, \"2\": { \"name\": \"string.unquoted.graphql\" }, \"3\": { \"name\": \"punctuation.graphql\" } }, \"match\": \"\\\\s*(([_A-Za-z][_0-9A-Za-z]*))\\\\s*(:)\" }, \"graphql-object-value\": { \"patterns\": [{ \"begin\": \"\\\\s*+({)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.curly.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.curly.graphql\" } }, \"name\": \"meta.objectvalues.graphql\", \"patterns\": [{ \"include\": \"#graphql-object-field\" }, { \"include\": \"#graphql-value\" }] }] }, \"graphql-operation-def\": { \"patterns\": [{ \"include\": \"#graphql-query-mutation\" }, { \"include\": \"#graphql-name\" }, { \"include\": \"#graphql-variable-definitions\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-selection-set\" }] }, \"graphql-query-mutation\": { \"captures\": { \"1\": { \"name\": \"keyword.operation.graphql\" } }, \"match\": \"\\\\s*\\\\b(query|mutation)\\\\b\" }, \"graphql-scalar\": { \"captures\": { \"1\": { \"name\": \"keyword.scalar.graphql\" }, \"2\": { \"name\": \"entity.scalar.graphql\" } }, \"match\": \"\\\\s*\\\\b(scalar)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, \"graphql-scalar-type\": { \"captures\": { \"1\": { \"name\": \"support.type.builtin.graphql\" }, \"2\": { \"name\": \"keyword.operator.nulltype.graphql\" } }, \"match\": \"\\\\s*\\\\b(Int|Float|String|Boolean|ID)\\\\b(?:\\\\s*(!))?\" }, \"graphql-schema\": { \"begin\": \"\\\\s*\\\\b(schema)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.schema.graphql\" } }, \"end\": \"(?<=})\", \"patterns\": [{ \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"patterns\": [{ \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.arguments.graphql\" } }, \"end\": \"(?=\\\\s*(([_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(})))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-selection-set\": { \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.selectionset.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-field\" }, { \"include\": \"#graphql-fragment-spread\" }, { \"include\": \"#graphql-inline-fragment\" }, { \"include\": \"#graphql-comma\" }, { \"include\": \"#native-interpolation\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-skip-newlines\": { \"match\": \"\\\\s*\\n\" }, \"graphql-string-content\": { \"patterns\": [{ \"match\": `\\\\\\\\[/'\"\\\\\\\\nrtbf]`, \"name\": \"constant.character.escape.graphql\" }, { \"match\": \"\\\\\\\\u([0-9a-fA-F]{4})\", \"name\": \"constant.character.escape.graphql\" }] }, \"graphql-string-value\": { \"begin\": '\\\\s*+((\"))', \"beginCaptures\": { \"1\": { \"name\": \"string.quoted.double.graphql\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.graphql\" } }, \"contentName\": \"string.quoted.double.graphql\", \"end\": '\\\\s*+(?:((\"))|(\\n))', \"endCaptures\": { \"1\": { \"name\": \"string.quoted.double.graphql\" }, \"2\": { \"name\": \"punctuation.definition.string.end.graphql\" }, \"3\": { \"name\": \"invalid.illegal.newline.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-string-content\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-type-definition\": { \"begin\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"comment\": \"key (optionalArgs): Type\", \"end\": \"(?=\\\\s*(([_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(})))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-variable-definitions\" }, { \"include\": \"#graphql-type-object\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-type-interface\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*\\\\b(?:(extends?)?\\\\b\\\\s*\\\\b(type)|(interface)|(input))\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)?\", \"captures\": { \"1\": { \"name\": \"keyword.type.graphql\" }, \"2\": { \"name\": \"keyword.type.graphql\" }, \"3\": { \"name\": \"keyword.interface.graphql\" }, \"4\": { \"name\": \"keyword.input.graphql\" }, \"5\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"name\": \"meta.type.interface.graphql\", \"patterns\": [{ \"begin\": \"\\\\s*\\\\b(implements)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.implements.graphql\" } }, \"end\": \"\\\\s*(?={)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-ampersand\" }, { \"include\": \"#graphql-comma\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-type-object\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-ignore-spaces\" }] }, \"graphql-type-object\": { \"begin\": \"\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"end\": \"\\\\s*(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.operation.graphql\" } }, \"name\": \"meta.type.object.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-object-type\" }, { \"include\": \"#graphql-type-definition\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-union\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*\\\\b(union)\\\\b\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"keyword.union.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"applyEndPatternLast\": 1, \"begin\": \"\\\\s*(=)\\\\s*([_A-Za-z][_0-9A-Za-z]*)\", \"captures\": { \"1\": { \"name\": \"punctuation.assignment.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"end\": \"(?=.)\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.or.graphql\" }, \"2\": { \"name\": \"support.type.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\\\\s*([_A-Za-z][_0-9A-Za-z]*)\" }] }, { \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-skip-newlines\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-union-mark\": { \"captures\": { \"1\": { \"name\": \"punctuation.union.graphql\" } }, \"match\": \"\\\\s*(\\\\|)\" }, \"graphql-value\": { \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-variable-name\" }, { \"include\": \"#graphql-float-value\" }, { \"include\": \"#graphql-string-value\" }, { \"include\": \"#graphql-boolean-value\" }, { \"include\": \"#graphql-null-value\" }, { \"include\": \"#graphql-enum-value\" }, { \"include\": \"#graphql-list-value\" }, { \"include\": \"#graphql-object-value\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-variable-assignment\": { \"applyEndPatternLast\": 1, \"begin\": \"\\\\s(=)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.assignment.graphql\" } }, \"end\": \"(?=[\\n,)])\", \"patterns\": [{ \"include\": \"#graphql-value\" }] }, \"graphql-variable-definition\": { \"begin\": \"\\\\s*(\\\\$?[_A-Za-z][_0-9A-Za-z]*)(?=\\\\s*\\\\(|:)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.parameter.graphql\" } }, \"comment\": \"variable: type = value,.... which may be a list\", \"end\": \"(?=\\\\s*((\\\\$?[_A-Za-z][_0-9A-Za-z]*)\\\\s*(\\\\(|:)|(}|\\\\))))|\\\\s*(,)\", \"endCaptures\": { \"5\": { \"name\": \"punctuation.comma.graphql\" } }, \"name\": \"meta.variables.graphql\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-directive\" }, { \"include\": \"#graphql-colon\" }, { \"include\": \"#graphql-input-types\" }, { \"include\": \"#graphql-variable-assignment\" }, { \"include\": \"#literal-quasi-embedded\" }, { \"include\": \"#graphql-skip-newlines\" }] }, \"graphql-variable-definitions\": { \"begin\": \"\\\\s*(\\\\()\", \"captures\": { \"1\": { \"name\": \"meta.brace.round.graphql\" } }, \"end\": \"\\\\s*(\\\\))\", \"patterns\": [{ \"include\": \"#graphql-comment\" }, { \"include\": \"#graphql-description-docstring\" }, { \"include\": \"#graphql-description-singleline\" }, { \"include\": \"#graphql-variable-definition\" }, { \"include\": \"#literal-quasi-embedded\" }] }, \"graphql-variable-name\": { \"captures\": { \"1\": { \"name\": \"variable.graphql\" } }, \"match\": \"\\\\s*(\\\\$[_A-Za-z][_0-9A-Za-z]*)\" }, \"native-interpolation\": { \"begin\": \"\\\\s*(\\\\${)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.substitution.begin\" } }, \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.substitution.end\" } }, \"name\": \"native.interpolation\", \"patterns\": [{ \"include\": \"source.js\" }, { \"include\": \"source.ts\" }, { \"include\": \"source.js.jsx\" }, { \"include\": \"source.tsx\" }] } }, \"scopeName\": \"source.graphql\", \"embeddedLangs\": [\"javascript\", \"typescript\", \"jsx\", \"tsx\"], \"aliases\": [\"gql\"] });\nvar graphql = [\n  ...javascript,\n  ...typescript,\n  ...jsx,\n  ...tsx,\n  lang\n];\n\nexport { graphql as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAW;QAAY;QAAO;KAAY;IAAE,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,SAAS;QAAU;QAAG,qBAAqB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,SAAS;oBAA2C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAA+D,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAyB;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAU;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAU;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,WAAW;oBAAiF,SAAS;oBAAe,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAS,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;gBAA0B;aAAE;QAAC;QAAG,iCAAiC;YAAE,SAAS;YAAO,OAAO;YAAO,QAAQ;QAAwB;QAAG,kCAAkC;YAAE,SAAS;YAAiC,QAAQ;QAAmC;QAAG,qBAAqB;YAAE,uBAAuB;YAAG,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,gCAAgC;YAAE,uBAAuB;YAAG,SAAS;YAAsD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,uBAAuB;oBAAG,SAAS;oBAAkC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;4BAAE;4BAAG,SAAS;wBAA4B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAiD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,QAAQ;oBAA4B,YAAY;wBAAC;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAA4D,QAAQ;QAAkC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAsC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAsD;QAAG,+BAA+B;YAAE,SAAS;YAAuG,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAU,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,2BAA2B;YAAE,uBAAuB;YAAG,SAAS;YAA2D,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;QAAO;QAAG,2BAA2B;YAAE,uBAAuB;YAAG,SAAS;YAAkE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAA2C;gBAAG;oBAAE,SAAS;oBAAa,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAyB,QAAQ;oBAA0B,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAA0B;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA+B;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,SAAS;QAAmB;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;QAAwC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAY,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAA6B;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,SAAS;QAAiD;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,SAAS;QAAsD;QAAG,kBAAkB;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,OAAO;YAAU,YAAY;gBAAC;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAA6C,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,OAAO;4BAA6D,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA4B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAuB;oCAAE;oCAAG,SAAS;gCAA+B;gCAAG;oCAAE,WAAW;gCAAmB;gCAAG;oCAAE,WAAW;gCAAiC;gCAAG;oCAAE,WAAW;gCAAkC;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAyB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;QAAS;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS,CAAC,kBAAkB,CAAC;oBAAE,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAAoC;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,eAAe;YAAgC,OAAO;YAAuB,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4C;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,2BAA2B;YAAE,SAAS;YAA6C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,WAAW;YAA4B,OAAO;YAA6D,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,0BAA0B;YAAE,uBAAuB;YAAG,SAAS;YAA8F,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;wBAA+B;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,iBAAiB;YAAE,uBAAuB;YAAG,SAAS;YAAiD,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAuB;YAAE;YAAG,OAAO;YAAS,YAAY;gBAAC;oBAAE,uBAAuB;oBAAG,SAAS;oBAAuC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAkC;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAyB;gCAAG,KAAK;oCAAE,QAAQ;gCAAuB;4BAAE;4BAAG,SAAS;wBAAwC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,SAAS;QAAY;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,+BAA+B;YAAE,uBAAuB;YAAG,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAc,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,+BAA+B;YAAE,SAAS;YAAiD,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,WAAW;YAAmD,OAAO;YAAqE,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,gCAAgC;YAAE,SAAS;YAAa,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAa,YAAY;gBAAC;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAkC;gBAAG;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,SAAS;QAAkC;QAAG,wBAAwB;YAAE,SAAS;YAAc,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;IAAE;IAAG,aAAa;IAAkB,iBAAiB;QAAC;QAAc;QAAc;QAAO;KAAM;IAAE,WAAW;QAAC;KAAM;AAAC;AAC9/lB,IAAI,UAAU;OACT,wMAAA,CAAA,UAAU;OACV,wMAAA,CAAA,UAAU;OACV,iMAAA,CAAA,UAAG;OACH,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/http.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs';\nimport json from './json.mjs';\nimport xml from './xml.mjs';\nimport graphql from './graphql.mjs';\nimport './java.mjs';\nimport './javascript.mjs';\nimport './typescript.mjs';\nimport './jsx.mjs';\nimport './tsx.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"HTTP\", \"fileTypes\": [\"http\", \"rest\"], \"name\": \"http\", \"patterns\": [{ \"begin\": \"^\\\\s*(?=curl)\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.curl\", \"patterns\": [{ \"include\": \"source.shell\" }] }, { \"begin\": \"\\\\s*(?=(\\\\[|{[^{]))\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.body.json\", \"patterns\": [{ \"include\": \"source.json\" }] }, { \"begin\": \"^\\\\s*(?=<\\\\S)\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.body.xml\", \"patterns\": [{ \"include\": \"text.xml\" }] }, { \"begin\": \"\\\\s*(?=(query|mutation))\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.body.graphql\", \"patterns\": [{ \"include\": \"source.graphql\" }] }, { \"begin\": \"\\\\s*(?=(query|mutation))\", \"end\": \"^\\\\{\\\\s*$\", \"name\": \"http.request.body.graphql\", \"patterns\": [{ \"include\": \"source.graphql\" }] }, { \"include\": \"#metadata\" }, { \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.http\" }, \"2\": { \"name\": \"variable.other.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*(@)([^\\\\s=]+)\\\\s*=\\\\s*(.*?)\\\\s*$\", \"name\": \"http.filevariable\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.http\" }, \"2\": { \"name\": \"variable.other.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*(\\\\?|&)([^=\\\\s]+)=(.*)$\", \"name\": \"http.query\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.tag.http\" }, \"2\": { \"name\": \"keyword.other.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"^([\\\\w\\\\-]+)\\\\s*(:)\\\\s*([^/].*?)\\\\s*$\", \"name\": \"http.headers\" }, { \"include\": \"#request-line\" }, { \"include\": \"#response-line\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"match\": \"^\\\\s*\\\\#{1,}.*$\", \"name\": \"comment.line.sharp.http\" }, { \"match\": \"^\\\\s*\\\\/{2,}.*$\", \"name\": \"comment.line.double-slash.http\" }] }, \"metadata\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"entity.name.type.http\" } }, \"match\": \"^\\\\s*\\\\#{1,}\\\\s+(?:((@)name)\\\\s+([^\\\\s\\\\.]+))$\", \"name\": \"comment.line.sharp.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"entity.name.type.http\" } }, \"match\": \"^\\\\s*\\\\/{2,}\\\\s+(?:((@)name)\\\\s+([^\\\\s\\\\.]+))$\", \"name\": \"comment.line.double-slash.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" } }, \"match\": \"^\\\\s*\\\\#{1,}\\\\s+((@)note)\\\\s*$\", \"name\": \"comment.line.sharp.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" } }, \"match\": \"^\\\\s*\\\\/{2,}\\\\s+((@)note)\\\\s*$\", \"name\": \"comment.line.double-slash.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"variable.other.http\" }, \"4\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*\\\\#{1,}\\\\s+(?:((@)prompt)\\\\s+([^\\\\s]+)(?:\\\\s+(.*))?\\\\s*)$\", \"name\": \"comment.line.sharp.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"variable.other.http\" }, \"4\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*\\\\/{2,}\\\\s+(?:((@)prompt)\\\\s+([^\\\\s]+)(?:\\\\s+(.*))?\\\\s*)$\", \"name\": \"comment.line.double-slash.http\" }] }, \"protocol\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.http\" }, \"2\": { \"name\": \"constant.numeric.http\" } }, \"match\": \"(HTTP)/(\\\\d+.\\\\d+)\", \"name\": \"http.version\" }] }, \"request-line\": { \"captures\": { \"1\": { \"name\": \"keyword.control.http\" }, \"2\": { \"name\": \"const.language.http\" }, \"3\": { \"patterns\": [{ \"include\": \"#protocol\" }] } }, \"match\": \"(?i)^(?:(get|post|put|delete|patch|head|options|connect|trace|lock|unlock|propfind|proppatch|copy|move|mkcol|mkcalendar|acl|search)\\\\s+)?\\\\s*(.+?)(?:\\\\s+(HTTP\\\\/\\\\S+))?$\", \"name\": \"http.requestline\" }, \"response-line\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#protocol\" }] }, \"2\": { \"name\": \"constant.numeric.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"(?i)^\\\\s*(HTTP\\\\/\\\\S+)\\\\s([1-5]\\\\d\\\\d)\\\\s(.*)$\", \"name\": \"http.responseLine\" } }, \"scopeName\": \"source.http\", \"embeddedLangs\": [\"shellscript\", \"json\", \"xml\", \"graphql\"] });\nvar http = [\n  ...shellscript,\n  ...json,\n  ...xml,\n  ...graphql,\n  lang\n];\n\nexport { http as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;;;;;AAOA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,aAAa;QAAC;QAAQ;KAAO;IAAE,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAiB,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG;YAAE,SAAS;YAAuB,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,QAAQ;YAA0B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiB,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG;YAAE,SAAS;YAA4B,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG;YAAE,SAAS;YAA4B,OAAO;YAAa,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAyC,QAAQ;QAAoB;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAgC,QAAQ;QAAa;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAyC,QAAQ;QAAe;QAAG;YAAE,WAAW;QAAgB;QAAG;YAAE,WAAW;QAAiB;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAiC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;oBAAkD,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;oBAAkD,QAAQ;gBAAiC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;oBAAkC,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;oBAAkC,QAAQ;gBAAiC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;oBAAkE,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;oBAAkE,QAAQ;gBAAiC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;oBAAsB,QAAQ;gBAAe;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;YAAE;YAAG,SAAS;YAA6K,QAAQ;QAAmB;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,SAAS;YAAkD,QAAQ;QAAoB;IAAE;IAAG,aAAa;IAAe,iBAAiB;QAAC;QAAe;QAAQ;QAAO;KAAU;AAAC;AACh+I,IAAI,OAAO;OACN,yMAAA,CAAA,UAAW;OACX,kMAAA,CAAA,UAAI;OACJ,iMAAA,CAAA,UAAG;OACH,qMAAA,CAAA,UAAO;IACV;CACD", "ignoreList": [0], "debugId": null}}]}