{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/ballerina.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Ballerina\", \"fileTypes\": [\"bal\"], \"name\": \"ballerina\", \"patterns\": [{ \"include\": \"#statements\" }], \"repository\": { \"access-modifier\": { \"patterns\": [{ \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(public|private)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.ballerina keyword.other.ballerina\" }] }, \"annotationAttachment\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.decorator.ballerina\" }, \"2\": { \"name\": \"support.type.ballerina\" }, \"3\": { \"name\": \"punctuation.decorator.ballerina\" }, \"4\": { \"name\": \"support.type.ballerina\" } }, \"match\": \"(@)((?:[_$A-Za-z][_$0-9A-Za-z]*))\\\\s*(:?)\\\\s*((?:[_$A-Za-z][_$0-9A-Za-z]*)?)\" }] }, \"annotationDefinition\": { \"patterns\": [{ \"begin\": \"\\\\bannotation\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \";\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"array-literal\": { \"begin\": \"\\\\s*(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.ballerina\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.ballerina\" } }, \"name\": \"meta.array.literal.ballerina\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }] }, \"booleans\": { \"patterns\": [{ \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.ballerina\" }] }, \"butClause\": { \"patterns\": [{ \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"meta.arrow.ballerina storage.type.function.arrow.ballerina\" } }, \"end\": \",|(?=\\\\})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"butExp\": { \"patterns\": [{ \"begin\": \"\\\\bbut\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"patterns\": [{ \"include\": \"#butExpBody\" }, { \"include\": \"#comment\" }] }] }, \"butExpBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"end\": \"(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"patterns\": [{ \"include\": \"#parameter\" }, { \"include\": \"#butClause\" }, { \"include\": \"#comment\" }] }] }, \"call\": { \"patterns\": [{ \"match\": \"(?:\\\\')?([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=\\\\()\", \"name\": \"entity.name.function.ballerina\" }] }, \"callableUnitBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"patterns\": [{ \"include\": \"#workerDef\" }, { \"include\": \"#service-decl\" }, { \"include\": \"#objectDec\" }, { \"include\": \"#function-defn\" }, { \"include\": \"#forkStatement\" }, { \"include\": \"#code\" }] }] }, \"class-body\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"name\": \"meta.class.body.ballerina\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#mdDocumentation\" }, { \"include\": \"#function-defn\" }, { \"include\": \"#var-expr\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#access-modifier\" }, { \"include\": \"#keywords\" }, { \"begin\": \"(?<=:)\\\\s*\", \"end\": \"(?=\\\\s|[;),}\\\\]:\\\\-+]|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|class|const|declare|enum|export|function|import|interface|let|module|namespace|return|service|type|var)\\\\b))\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#punctuation-semicolon\" }] }, \"class-defn\": { \"begin\": \"(\\\\s+)(class\\\\b)|^class\\\\b(?=\\\\s+|/[/*])\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.class.ballerina keyword.other.ballerina\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.class.ballerina\", \"patterns\": [{ \"include\": \"#keywords\" }, { \"captures\": { \"0\": { \"name\": \"entity.name.type.class.ballerina\" } }, \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\" }, { \"include\": \"#class-body\" }] }, \"code\": { \"patterns\": [{ \"include\": \"#booleans\" }, { \"include\": \"#matchStatement\" }, { \"include\": \"#butExp\" }, { \"include\": \"#xml\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#keywords\" }, { \"include\": \"#strings\" }, { \"include\": \"#comment\" }, { \"include\": \"#mdDocumentation\" }, { \"include\": \"#annotationAttachment\" }, { \"include\": \"#numbers\" }, { \"include\": \"#maps\" }, { \"include\": \"#paranthesised\" }, { \"include\": \"#paranthesisedBracket\" }, { \"include\": \"#regex\" }] }, \"comment\": { \"patterns\": [{ \"match\": \"\\\\/\\\\/.*\", \"name\": \"comment.ballerina\" }] }, \"constrainType\": { \"patterns\": [{ \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.ballerina\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.ballerina\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#constrainType\" }, { \"match\": \"\\\\b([_$A-Za-z][_$0-9A-Za-z]*)\\\\b\", \"name\": \"storage.type.ballerina\" }] }] }, \"control-statement\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(return)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.flow.ballerina\" } }, \"end\": \"(?=[;}]|$|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|class|const|declare|enum|export|function|import|interface|let|module|namespace|return|service|type|var)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"include\": \"#for-loop\" }, { \"include\": \"#if-statement\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(else|if)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.conditional.ballerina\" }] }, \"decl-block\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"(?=\\\\} external;)|(\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"name\": \"meta.block.ballerina\", \"patterns\": [{ \"include\": \"#statements\" }, { \"include\": \"#mdDocumentation\" }] }, \"declaration\": { \"patterns\": [{ \"include\": \"#import-declaration\" }, { \"include\": \"#var-expr\" }, { \"include\": \"#typeDefinition\" }, { \"include\": \"#function-defn\" }, { \"include\": \"#service-decl\" }, { \"include\": \"#class-defn\" }, { \"include\": \"#enum-decl\" }, { \"include\": \"#source\" }, { \"include\": \"#keywords\" }] }, \"defaultValue\": { \"patterns\": [{ \"begin\": \"[=:]\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.ballerina\" } }, \"end\": \"(?=[,)])\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"defaultWithParentheses\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } } }] }, \"documentationBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"end\": \"(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.ballerina.documentation\" }, \"2\": { \"name\": \"keyword.other.ballerina.documentation\" }, \"3\": { \"name\": \"variable.parameter.ballerina.documentation\" }, \"4\": { \"name\": \"keyword.other.ballerina.documentation\" } }, \"match\": \"(P|R|T|F|V)({{)(.*)(}})\" }, { \"begin\": \"\\\\```\", \"end\": \"\\\\```\", \"name\": \"comment.block.code.ballerina.documentation\" }, { \"begin\": \"\\\\``\", \"end\": \"\\\\``\", \"name\": \"comment.block.code.ballerina.documentation\" }, { \"begin\": \"\\\\`\", \"end\": \"\\\\`\", \"name\": \"comment.block.code.ballerina.documentation\" }, { \"match\": \".\", \"name\": \"comment.block.ballerina.documentation\" }] }] }, \"documentationDef\": { \"patterns\": [{ \"begin\": \"\\\\b(?:documentation|deprecated)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"delimiter.curly\" } }, \"patterns\": [{ \"include\": \"#documentationBody\" }, { \"include\": \"#comment\" }] }] }, \"enum-decl\": { \"begin\": \"(?:\\\\b(const)\\\\s+)?\\\\b(enum)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.ballerina\" }, \"2\": { \"name\": \"keyword.other.ballerina\" }, \"3\": { \"name\": \"entity.name.type.enum.ballerina\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.enum.declaration.ballerina\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#mdDocumentation\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#mdDocumentation\" }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.enummember.ballerina\" } }, \"end\": \"(?=,|\\\\}|$)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, { \"begin\": \"(?=((\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])+\\\\])))\", \"end\": \"(?=,|\\\\}|$)\", \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#comment\" }, { \"include\": \"#variable-initializer\" }] }, { \"include\": \"#punctuation-comma\" }] }] }, \"errorDestructure\": { \"patterns\": [{ \"begin\": \"error\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?==>)\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"expression\": { \"patterns\": [{ \"include\": \"#keywords\" }, { \"include\": \"#expressionWithoutIdentifiers\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#regex\" }] }, \"expression-operators\": { \"patterns\": [{ \"match\": \"\\\\*=|(?<!\\\\()/=|%=|\\\\+=|-=\", \"name\": \"keyword.operator.assignment.compound.ballerina\" }, { \"match\": \"\\\\&=|\\\\^=|<<=|>>=|>>>=|\\\\|=\", \"name\": \"keyword.operator.assignment.compound.bitwise.ballerina\" }, { \"match\": \"<<|>>>|>>\", \"name\": \"keyword.operator.bitwise.shift.ballerina\" }, { \"match\": \"===|!==|==|!=\", \"name\": \"keyword.operator.comparison.ballerina\" }, { \"match\": \"<=|>=|<>|<|>\", \"name\": \"keyword.operator.relational.ballerina\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.ballerina\" }, \"2\": { \"name\": \"keyword.operator.assignment.compound.ballerina\" }, \"3\": { \"name\": \"keyword.operator.arithmetic.ballerina\" } }, \"match\": \"(?<=[_$0-9A-Za-z])(!)\\\\s*(?:(/=)|(?:(/)(?![/*])))\" }, { \"match\": \"!|&&|\\\\|\\\\||\\\\?\\\\?\", \"name\": \"keyword.operator.logical.ballerina\" }, { \"match\": \"\\\\&|~|\\\\^|\\\\|\", \"name\": \"keyword.operator.bitwise.ballerina\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.ballerina\" }, { \"match\": \"--\", \"name\": \"keyword.operator.decrement.ballerina\" }, { \"match\": \"\\\\+\\\\+\", \"name\": \"keyword.operator.increment.ballerina\" }, { \"match\": \"%|\\\\*|/|-|\\\\+\", \"name\": \"keyword.operator.arithmetic.ballerina\" }] }, \"expressionWithoutIdentifiers\": { \"patterns\": [{ \"include\": \"#xml\" }, { \"include\": \"#string\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#comment\" }, { \"include\": \"#object-literal\" }, { \"include\": \"#ternary-expression\" }, { \"include\": \"#expression-operators\" }, { \"include\": \"#literal\" }, { \"include\": \"#paranthesised\" }, { \"include\": \"#regex\" }] }, \"flags-on-off\": { \"name\": \"meta.flags.regexp.ballerina\", \"patterns\": [{ \"begin\": \"(\\\\??)([imsx]*)(-?)([imsx]*)(:)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.other.non-capturing-group-begin.regexp.ballerina\" }, \"2\": { \"name\": \"keyword.other.non-capturing-group.flags-on.regexp.ballerina\" }, \"3\": { \"name\": \"punctuation.other.non-capturing-group.off.regexp.ballerina\" }, \"4\": { \"name\": \"keyword.other.non-capturing-group.flags-off.regexp.ballerina\" }, \"5\": { \"name\": \"punctuation.other.non-capturing-group-end.regexp.ballerina\" } }, \"end\": \"()\", \"name\": \"constant.other.flag.regexp.ballerina\", \"patterns\": [{ \"include\": \"#regexp\" }, { \"include\": \"#template-substitution-element\" }] }] }, \"for-loop\": { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))foreach\\\\s*\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.loop.ballerina\" }, \"1\": { \"name\": \"support.type.primitive.ballerina\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"match\": \"\\\\bin\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#comment\" }, { \"include\": \"#var-expr\" }, { \"include\": \"#expression\" }] }, \"forkBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#workerDef\" }] }] }, \"forkStatement\": { \"patterns\": [{ \"begin\": \"\\\\bfork\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"patterns\": [{ \"include\": \"#forkBody\" }] }] }, \"function-body\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#functionParameters\" }, { \"include\": \"#decl-block\" }, { \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"meta.arrow.ballerina storage.type.function.arrow.ballerina\" } }, \"end\": \"(?=\\\\;)|(?=\\\\,)|(?=)(?=\\\\);)\", \"name\": \"meta.block.ballerina\", \"patterns\": [{ \"include\": \"#statements\" }, { \"include\": \"#punctuation-comma\" }] }, { \"match\": \"\\\\*\", \"name\": \"keyword.generator.asterisk.ballerina\" }] }, \"function-defn\": { \"begin\": \"(?:(public|private)\\\\s+)?(function\\\\b)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.ballerina\" }, \"2\": { \"name\": \"keyword.other.ballerina\" } }, \"end\": \"(?<=\\\\;)|(?<=\\\\})|(?<=\\\\,)|(?=)(?=\\\\);)\", \"name\": \"meta.function.ballerina\", \"patterns\": [{ \"match\": \"\\\\bexternal\\\\b\", \"name\": \"keyword.ballerina\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#annotationAttachment\" }, { \"include\": \"#functionReturns\" }, { \"include\": \"#functionName\" }, { \"include\": \"#functionParameters\" }, { \"include\": \"#punctuation-semicolon\" }, { \"include\": \"#function-body\" }, { \"include\": \"#regex\" }] }, \"function-parameters-body\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#numbers\" }, { \"include\": \"#string\" }, { \"include\": \"#annotationAttachment\" }, { \"include\": \"#recordLiteral\" }, { \"include\": \"#keywords\" }, { \"include\": \"#parameter-name\" }, { \"include\": \"#array-literal\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#regex\" }, { \"match\": \"\\\\,\", \"name\": \"punctuation.separator.parameter.ballerina\" }] }, \"functionName\": { \"patterns\": [{ \"match\": \"\\\\bfunction\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#self-literal\" }, { \"include\": \"#string\" }, { \"captures\": { \"2\": { \"name\": \"variable.language.this.ballerina\" }, \"3\": { \"name\": \"keyword.other.ballerina\" }, \"4\": { \"name\": \"support.type.primitive.ballerina\" }, \"5\": { \"name\": \"storage.type.ballerina\" }, \"6\": { \"name\": \"meta.definition.function.ballerina entity.name.function.ballerina\" } }, \"match\": \"\\\\s+(\\\\b(self)|\\\\b(is|new|isolated|null|function|in)\\\\b|(string|int|boolean|float|byte|decimal|json|xml|anydata)\\\\b|\\\\b(readonly|error|map)\\\\b|([_$A-Za-z][_$0-9A-Za-z]*))\" }] }, \"functionParameters\": { \"begin\": \"\\\\(|\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.ballerina\" } }, \"end\": \"\\\\)|\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.ballerina\" } }, \"name\": \"meta.parameters.ballerina\", \"patterns\": [{ \"include\": \"#function-parameters-body\" }] }, \"functionReturns\": { \"begin\": \"\\\\s*(returns)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.ballerina\" } }, \"end\": \"(?==>)|(=)|(?=\\\\{)|(\\\\))|(?=\\\\;)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.ballerina\" } }, \"name\": \"meta.type.function.return.ballerina\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#numbers\" }, { \"include\": \"#keywords\" }, { \"include\": \"#type-primitive\" }, { \"captures\": { \"1\": { \"name\": \"support.type.primitive.ballerina\" } }, \"match\": \"\\\\s*\\\\b(var)(?=\\\\s+|\\\\[|\\\\?)\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.ballerina\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.optional.ballerina\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#type-tuple\" }, { \"include\": \"#keywords\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"variable.other.readwrite.ballerina\" }] }, \"functionType\": { \"patterns\": [{ \"begin\": \"\\\\bfunction\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \"(?=\\\\,)|(?=\\\\|)|(?=:)|(?==>)|(?=\\\\))|(?=\\\\])\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#functionTypeParamList\" }, { \"include\": \"#functionTypeReturns\" }] }] }, \"functionTypeParamList\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"delimiter.parenthesis\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"delimiter.parenthesis\" } }, \"patterns\": [{ \"match\": \"public\", \"name\": \"keyword\" }, { \"include\": \"#annotationAttachment\" }, { \"include\": \"#recordLiteral\" }, { \"include\": \"#record\" }, { \"include\": \"#objectDec\" }, { \"include\": \"#functionType\" }, { \"include\": \"#constrainType\" }, { \"include\": \"#parameterTuple\" }, { \"include\": \"#functionTypeType\" }, { \"include\": \"#comment\" }] }] }, \"functionTypeReturns\": { \"patterns\": [{ \"begin\": \"\\\\breturns\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=\\\\,)|(?:\\\\|)|(?=\\\\])|(?=\\\\))\", \"patterns\": [{ \"include\": \"#functionTypeReturnsParameter\" }, { \"include\": \"#comment\" }] }] }, \"functionTypeReturnsParameter\": { \"patterns\": [{ \"begin\": \"((?=record|object|function)|(?:[_$A-Za-z][_$0-9A-Za-z]*))\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?=\\\\,)|(?:\\\\|)|(?::)|(?==>)|(?=\\\\))|(?=\\\\])\", \"patterns\": [{ \"include\": \"#record\" }, { \"include\": \"#objectDec\" }, { \"include\": \"#functionType\" }, { \"include\": \"#constrainType\" }, { \"include\": \"#defaultValue\" }, { \"include\": \"#comment\" }, { \"include\": \"#parameterTuple\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"default.variable.parameter.ballerina\" }] }] }, \"functionTypeType\": { \"patterns\": [{ \"begin\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?=\\\\,)|(?:\\\\|)|(?=\\\\])|(?=\\\\))\" }] }, \"identifiers\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ballerina\" }, \"2\": { \"name\": \"punctuation.accessor.optional.ballerina\" }, \"3\": { \"name\": \"entity.name.function.ballerina\" } }, \"match\": \"(?:(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*)?([_$A-Za-z][_$0-9A-Za-z]*)(?=\\\\s*=\\\\s*((((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((((<\\\\s*$)|((<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|((<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>)))))\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ballerina\" }, \"2\": { \"name\": \"punctuation.accessor.optional.ballerina\" }, \"3\": { \"name\": \"entity.name.function.ballerina\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=\\\\()\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ballerina\" }, \"2\": { \"name\": \"punctuation.accessor.optional.ballerina\" }, \"3\": { \"name\": \"variable.other.property.ballerina\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\\\\s*(\\\\#?[_$A-Za-z][_$0-9A-Za-z]*)\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#self-literal\" }, { \"match\": \"\\\\b(check|foreach|if|checkpanic)\\\\b\", \"name\": \"keyword.control.ballerina\" }, { \"include\": \"#call\" }, { \"match\": \"\\\\b(var)\\\\b\", \"name\": \"support.type.primitive.ballerina\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.readwrite.ballerina\" }, \"3\": { \"name\": \"punctuation.accessor.ballerina\" }, \"4\": { \"name\": \"entity.name.function.ballerina\" }, \"5\": { \"name\": \"punctuation.definition.parameters.begin.ballerina\" }, \"6\": { \"name\": \"punctuation.definition.parameters.end.ballerina\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)((\\\\.)([_$A-Za-z][_$0-9A-Za-z]*)(\\\\()(\\\\)))?\" }, { \"match\": \"(\\\\')([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"variable.other.property.ballerina\" }, { \"include\": \"#type-annotation\" }] }, \"if-statement\": { \"patterns\": [{ \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?=\\\\bif\\\\b\\\\s*(?!\\\\{))\", \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(if)\\\\s*(\\\\()?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.ballerina\" }, \"2\": { \"name\": \"meta.brace.round.ballerina\" } }, \"end\": \"(\\\\))|(?=\\\\{)\", \"endCaptures\": { \"1\": { \"name\": \"meta.brace.round.ballerina\" } }, \"patterns\": [{ \"include\": \"#decl-block\" }, { \"include\": \"#keywords\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#xml\" }, { \"include\": \"#string\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#comment\" }, { \"include\": \"#ternary-expression\" }, { \"include\": \"#expression-operators\" }, { \"include\": \"#literal\" }, { \"include\": \"#paranthesised\" }, { \"include\": \"#regex\" }] }, { \"begin\": \"(?<=\\\\))(?=\\\\s|=)\", \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#literal\" }, { \"include\": \"#keywords\" }] }, { \"include\": \"#decl-block\" }] }] }, \"import-clause\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.default.ballerina\" }, \"3\": { \"name\": \"variable.other.readwrite.ballerina meta.import.module.ballerina\" }, \"5\": { \"name\": \"keyword.control.default.ballerina\" }, \"6\": { \"name\": \"variable.other.readwrite.alias.ballerina\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(\\\\bdefault)|(\\\\*)|(\\\\b[_$A-Za-z][_$0-9A-Za-z]*))\" }, { \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"variable.other.readwrite.alias.ballerina\" }] }, \"import-declaration\": { \"begin\": \"\\\\bimport\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.import.ballerina\" } }, \"end\": \"\\\\;\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.statement.ballerina\" } }, \"name\": \"meta.import.ballerina\", \"patterns\": [{ \"match\": \"(\\\\')([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"variable.other.property.ballerina\" }, { \"include\": \"#keywords\" }, { \"include\": \"#comment\" }, { \"include\": \"#import-clause\" }, { \"include\": \"#punctuation-accessor\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(fork|join|while|returns|transaction|transactional|retry|commit|rollback|typeof|enum|wait|match)\\\\b\", \"name\": \"keyword.control.ballerina\" }, { \"match\": \"\\\\b(return|break|continue|check|checkpanic|panic|trap|from|where)\\\\b\", \"name\": \"keyword.control.flow.ballerina\" }, { \"match\": \"\\\\b(public|private|external|return|record|object|remote|abstract|client|true|false|fail|import|version)\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"match\": \"\\\\b(as|on|function|resource|listener|const|final|is|null|lock|annotation|source|worker|parameter|field|isolated|in)\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"match\": \"\\\\b(xmlns|table|key|let|new|select|start|flush|default|do|base16|base64|conflict)\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"match\": \"\\\\b(limit|outer|equals|order|by|ascending|descending|class|configurable|variable|module|service|group|collect)\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"match\": \"(=>)\", \"name\": \"meta.arrow.ballerina storage.type.function.arrow.ballerina\" }, { \"match\": \"(!|%|\\\\+|-|~=|===|==|=|!=|!==|<|>|&|\\\\||\\\\?:|\\\\.\\\\.\\\\.|<=|>=|&&|\\\\|\\\\||~|>>|>>>)\", \"name\": \"keyword.operator.ballerina\" }, { \"include\": \"#types\" }, { \"include\": \"#self-literal\" }, { \"include\": \"#type-primitive\" }] }, \"literal\": { \"patterns\": [{ \"include\": \"#booleans\" }, { \"include\": \"#numbers\" }, { \"include\": \"#strings\" }, { \"include\": \"#maps\" }, { \"include\": \"#self-literal\" }, { \"include\": \"#array-literal\" }] }, \"maps\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"matchBindingPattern\": { \"patterns\": [{ \"begin\": \"var\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?==>)|,\", \"patterns\": [{ \"include\": \"#errorDestructure\" }, { \"include\": \"#code\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"variable.parameter.ballerina\" }] }] }, \"matchStatement\": { \"patterns\": [{ \"begin\": \"\\\\bmatch\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.ballerina\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#matchStatementBody\" }, { \"include\": \"#comment\" }, { \"include\": \"#code\" }] }] }, \"matchStatementBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"end\": \"(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina.documentation\" } }, \"patterns\": [{ \"include\": \"#literal\" }, { \"include\": \"#matchBindingPattern\" }, { \"include\": \"#matchStatementPatternClause\" }, { \"include\": \"#comment\" }, { \"include\": \"#code\" }] }] }, \"matchStatementPatternClause\": { \"patterns\": [{ \"begin\": \"=>\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \"((\\\\})|;|,)\", \"patterns\": [{ \"include\": \"#callableUnitBody\" }, { \"include\": \"#code\" }] }] }, \"mdDocumentation\": { \"begin\": \"\\\\#\", \"end\": \"[\\\\r\\\\n]+\", \"name\": \"comment.mddocs.ballerina\", \"patterns\": [{ \"include\": \"#mdDocumentationReturnParamDescription\" }, { \"include\": \"#mdDocumentationParamDescription\" }] }, \"mdDocumentationParamDescription\": { \"patterns\": [{ \"begin\": \"(\\\\+\\\\s+)(\\\\'?[_$A-Za-z][_$0-9A-Za-z]*)(\\\\s*-\\\\s+)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.ballerina\" }, \"2\": { \"name\": \"variable.other.readwrite.ballerina\" }, \"3\": { \"name\": \"keyword.operator.ballerina\" } }, \"end\": \"(?=[^#\\\\r\\\\n]|(?:# *?\\\\+))\", \"patterns\": [{ \"match\": \"#.*\", \"name\": \"comment.mddocs.paramdesc.ballerina\" }] }] }, \"mdDocumentationReturnParamDescription\": { \"patterns\": [{ \"begin\": \"(#)(?: *?)(\\\\+)(?: *)(return)(?: *)(-)?(.*)\", \"beginCaptures\": { \"1\": { \"name\": \"comment.mddocs.ballerina\" }, \"2\": { \"name\": \"keyword.ballerina\" }, \"3\": { \"name\": \"keyword.ballerina\" }, \"4\": { \"name\": \"keyword.ballerina\" }, \"5\": { \"name\": \"comment.mddocs.returnparamdesc.ballerina\" } }, \"end\": \"(?=[^#\\\\r\\\\n]|(?:# *?\\\\+))\", \"patterns\": [{ \"match\": \"#.*\", \"name\": \"comment.mddocs.returnparamdesc.ballerina\" }] }] }, \"multiType\": { \"patterns\": [{ \"match\": \"(?<=\\\\|)([_$A-Za-z][_$0-9A-Za-z]*)|([_$A-Za-z][_$0-9A-Za-z]*)(?=\\\\|)\", \"name\": \"storage.type.ballerina\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.ballerina\" }] }, \"numbers\": { \"patterns\": [{ \"match\": \"\\\\b0[xX][\\\\da-fA-F]+\\\\b|\\\\b\\\\d+(?:\\\\.(?:\\\\d+|$))?\", \"name\": \"constant.numeric.decimal.ballerina\" }] }, \"object-literal\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"name\": \"meta.objectliteral.ballerina\", \"patterns\": [{ \"include\": \"#object-member\" }, { \"include\": \"#punctuation-comma\" }] }, \"object-member\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#function-defn\" }, { \"include\": \"#literal\" }, { \"include\": \"#keywords\" }, { \"include\": \"#expression\" }, { \"begin\": \"(?=\\\\[)\", \"end\": \"(?=:)|((?<=[\\\\]])(?=\\\\s*[(<]))\", \"name\": \"meta.object.member.ballerina meta.object-literal.key.ballerina\", \"patterns\": [{ \"include\": \"#comment\" }] }, { \"begin\": \"(?=[\\\\'\\\\\\\"\\\\`])\", \"end\": \"(?=:)|((?<=[\\\\'\\\\\\\"\\\\`])(?=((\\\\s*[(<,}])|(\\\\n*})|(\\\\s+(as)\\\\s+))))\", \"name\": \"meta.object.member.ballerina meta.object-literal.key.ballerina\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }] }, { \"begin\": \"(?=(\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$))|(\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$))|((?<!\\\\$)(?:(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\B(\\\\.)\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*[eE][+-]?\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(\\\\.)(n)?\\\\B)|(?:\\\\B(\\\\.)\\\\d[0-9_]*(n)?\\\\b)|(?:\\\\b\\\\d[0-9_]*(n)?\\\\b(?!\\\\.)))(?!\\\\$)))\", \"end\": \"(?=:)|(?=\\\\s*([(<,}])|(\\\\s+as\\\\s+))\", \"name\": \"meta.object.member.ballerina meta.object-literal.key.ballerina\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#numbers\" }] }, { \"begin\": \"(?<=[\\\\]\\\\'\\\\\\\"\\\\`])(?=\\\\s*[(<])\", \"end\": \"(?=\\\\}|;|,)|(?<=\\\\})\", \"name\": \"meta.method.declaration.ballerina\", \"patterns\": [{ \"include\": \"#function-body\" }] }, { \"captures\": { \"0\": { \"name\": \"meta.object-literal.key.ballerina\" }, \"1\": { \"name\": \"constant.numeric.decimal.ballerina\" } }, \"match\": \"(?![_$A-Za-z])([\\\\d]+)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:)\", \"name\": \"meta.object.member.ballerina\" }, { \"captures\": { \"0\": { \"name\": \"meta.object-literal.key.ballerina\" }, \"1\": { \"name\": \"entity.name.function.ballerina\" } }, \"match\": \"(?:([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:(\\\\s*\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/)*\\\\s*((((function\\\\s*[(<*])|(function\\\\s+)|([_$A-Za-z][_$0-9A-Za-z]*\\\\s*=>)))|((((<\\\\s*$)|((<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*((([{\\\\[]\\\\s*)?$)|((\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})\\\\s*((:\\\\s*\\\\{?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*)))|((\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])\\\\s*((:\\\\s*\\\\[?$)|((\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+\\\\s*)?=\\\\s*))))))|((<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?[(]\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([)]\\\\s*:)|((\\\\.\\\\.\\\\.\\\\s*)?[_$A-Za-z][_$0-9A-Za-z]*\\\\s*:)))|((<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<]|<\\\\s*([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\]))([^=<>]|=[^<])*>)*>)*>\\\\s*)?\\\\(\\\\s*(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*(([_$A-Za-z]|(\\\\{([^{}]|(\\\\{([^{}]|\\\\{[^{}]*\\\\})*\\\\}))*\\\\})|(\\\\[([^\\\\[\\\\]]|(\\\\[([^\\\\[\\\\]]|\\\\[[^\\\\[\\\\]]*\\\\])*\\\\]))*\\\\])|(\\\\.\\\\.\\\\.\\\\s*[_$A-Za-z]))([^()\\\\'\\\\\\\"\\\\`]|(\\\\(([^()]|(\\\\(([^()]|\\\\([^()]*\\\\))*\\\\)))*\\\\))|(\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`))*)?\\\\)(\\\\s*:\\\\s*([^<>(){}]|<([^<>]|<([^<>]|<[^<>]+>)+>)+>|\\\\([^()]+\\\\)|\\\\{[^{}]+\\\\})+)?\\\\s*=>))))))\", \"name\": \"meta.object.member.ballerina\" }, { \"captures\": { \"0\": { \"name\": \"meta.object-literal.key.ballerina\" } }, \"match\": \"(?:[_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=(\\\\/\\\\*([^\\\\*]|(\\\\*[^\\\\/]))*\\\\*\\\\/\\\\s*)*:)\", \"name\": \"meta.object.member.ballerina\" }, { \"begin\": \"\\\\.\\\\.\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.spread.ballerina\" } }, \"end\": \"(?=,|\\\\})\", \"name\": \"meta.object.member.ballerina\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"captures\": { \"1\": { \"name\": \"variable.other.readwrite.ballerina\" } }, \"match\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=,|\\\\}|$|\\\\/\\\\/|\\\\/\\\\*)\", \"name\": \"meta.object.member.ballerina\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.as.ballerina\" }, \"2\": { \"name\": \"storage.modifier.ballerina\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as)\\\\s+(const)(?=\\\\s*([,}]|$))\", \"name\": \"meta.object.member.ballerina\" }, { \"begin\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.as.ballerina\" } }, \"end\": \"(?=[;),}\\\\]:?\\\\-+>]|\\\\|\\\\||\\\\&\\\\&|!==|$|^|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(as)\\\\s+))\", \"name\": \"meta.object.member.ballerina\" }, { \"begin\": \"(?=[_$A-Za-z][_$0-9A-Za-z]*\\\\s*=)\", \"end\": \"(?=,|\\\\}|$|\\\\/\\\\/|\\\\/\\\\*)\", \"name\": \"meta.object.member.ballerina\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"objectDec\": { \"patterns\": [{ \"begin\": \"\\\\bobject\\\\b(?!:)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.ballerina\" } }, \"end\": \"(?<=\\\\})\", \"patterns\": [{ \"include\": \"#decl-block\" }] }] }, \"objectInitBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#code\" }] }] }, \"objectInitParameters\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.ballerina\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.ballerina\" } }, \"patterns\": [{ \"include\": \"#code\" }, { \"match\": \"\\\\b([_$A-Za-z][_$0-9A-Za-z]*)\\\\b\", \"name\": \"variable.parameter.ballerina\" }] }] }, \"objectMemberFunctionDec\": { \"patterns\": [{ \"begin\": \"\\\\bfunction\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"patterns\": [{ \"include\": \"#functionParameters\" }, { \"match\": \"\\\\breturns\\\\b\", \"name\": \"keyword.ballerina\" }, { \"include\": \"#code\" }] }] }, \"parameter\": { \"patterns\": [{ \"begin\": \"((?=record|object|function)|([_$A-Za-z][_$0-9A-Za-z]*)(?=\\\\|)|(?:[_$A-Za-z][_$0-9A-Za-z]*))\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?:\\\\,)|(?:\\\\|)|(?::)|(?==>)|(?=\\\\))|(?=\\\\])\", \"patterns\": [{ \"include\": \"#parameterWithDescriptor\" }, { \"include\": \"#record\" }, { \"include\": \"#objectDec\" }, { \"include\": \"#functionType\" }, { \"include\": \"#constrainType\" }, { \"include\": \"#defaultValue\" }, { \"include\": \"#comment\" }, { \"include\": \"#parameterTuple\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"default.variable.parameter.ballerina\" }] }] }, \"parameter-name\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.type.primitive.ballerina\" } }, \"match\": \"\\\\s*\\\\b(var)\\\\s+\" }, { \"captures\": { \"2\": { \"name\": \"keyword.operator.rest.ballerina\" }, \"3\": { \"name\": \"support.type.primitive.ballerina\" }, \"4\": { \"name\": \"keyword.other.ballerina\" }, \"5\": { \"name\": \"constant.language.boolean.ballerina\" }, \"6\": { \"name\": \"keyword.control.flow.ballerina\" }, \"7\": { \"name\": \"storage.type.ballerina\" }, \"8\": { \"name\": \"variable.parameter.ballerina\" }, \"9\": { \"name\": \"variable.parameter.ballerina\" }, \"10\": { \"name\": \"keyword.operator.optional.ballerina\" } }, \"match\": \"(?:(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))\\\\s+)?(?:(\\\\.\\\\.\\\\.)\\\\s*)?(?<!=|:)(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?:(this)|(string|int|boolean|float|byte|decimal|json|xml|anydata)|\\\\b(is|new|isolated|null|function|in)\\\\b|\\\\b(true|false)\\\\b|\\\\b(check|foreach|if|checkpanic)\\\\b|\\\\b(readonly|error|map)\\\\b|([_$A-Za-z][_$0-9A-Za-z]*))(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\\\\s*(\\\\??)\" }] }, \"parameterTuple\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"end\": \"(?=\\\\,)|(?=\\\\|)|(?=:)|(?==>)|(?=\\\\))\", \"patterns\": [{ \"include\": \"#record\" }, { \"include\": \"#objectDec\" }, { \"include\": \"#parameterTupleType\" }, { \"include\": \"#parameterTupleEnd\" }, { \"include\": \"#comment\" }] }] }, \"parameterTupleEnd\": { \"patterns\": [{ \"begin\": \"\\\\]\", \"end\": \"(?=\\\\,)|(?=\\\\|)|(?=:)|(?==>)|(?=\\\\))\", \"patterns\": [{ \"include\": \"#defaultWithParentheses\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"default.variable.parameter.ballerina\" }] }] }, \"parameterTupleType\": { \"patterns\": [{ \"begin\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?:\\\\,)|(?:\\\\|)|(?=\\\\])\" }] }, \"parameterWithDescriptor\": { \"patterns\": [{ \"begin\": \"\\\\&\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.ballerina\" } }, \"end\": \"(?=\\\\,)|(?=\\\\|)|(?=\\\\))\", \"patterns\": [{ \"include\": \"#parameter\" }] }] }, \"parameters\": { \"patterns\": [{ \"match\": \"\\\\s*(return|break|continue|check|checkpanic|panic|trap|from|where)\\\\b\", \"name\": \"keyword.control.flow.ballerina\" }, { \"match\": \"\\\\s*(let|select)\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"match\": \"\\\\,\", \"name\": \"punctuation.separator.parameter.ballerina\" }] }, \"paranthesised\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.ballerina\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.ballerina\" } }, \"name\": \"meta.brace.round.block.ballerina\", \"patterns\": [{ \"include\": \"#self-literal\" }, { \"include\": \"#function-defn\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#parameters\" }, { \"include\": \"#annotationAttachment\" }, { \"include\": \"#recordLiteral\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#parameter-name\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#expression\" }, { \"include\": \"#regex\" }] }, \"paranthesisedBracket\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#code\" }] }] }, \"punctuation-accessor\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.accessor.ballerina\" }, \"2\": { \"name\": \"punctuation.accessor.optional.ballerina\" } }, \"match\": \"(?:(\\\\.)|(\\\\?\\\\.(?!\\\\s*[\\\\d])))\" }] }, \"punctuation-comma\": { \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.separator.comma.ballerina\" }] }, \"punctuation-semicolon\": { \"patterns\": [{ \"match\": \";\", \"name\": \"punctuation.terminator.statement.ballerina\" }] }, \"record\": { \"begin\": \"\\\\brecord\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.ballerina\" } }, \"end\": \"(?<=\\\\})\", \"name\": \"meta.record.ballerina\", \"patterns\": [{ \"include\": \"#recordBody\" }] }, \"recordBody\": { \"patterns\": [{ \"include\": \"#decl-block\" }] }, \"recordLiteral\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.ballerina\" } }, \"patterns\": [{ \"include\": \"#code\" }] }] }, \"regex\": { \"patterns\": [{ \"begin\": \"(\\\\bre)(\\\\s*)(`)\", \"beginCaptures\": { \"1\": { \"name\": \"support.type.primitive.ballerina\" }, \"3\": { \"name\": \"punctuation.definition.regexp.template.begin.ballerina\" } }, \"end\": \"`\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.regexp.template.end.ballerina\" } }, \"name\": \"regexp.template.ballerina\", \"patterns\": [{ \"include\": \"#template-substitution-element\" }, { \"include\": \"#regexp\" }] }] }, \"regex-character-class\": { \"patterns\": [{ \"match\": \"\\\\\\\\[wWsSdDtrn]|\\\\.\", \"name\": \"keyword.other.character-class.regexp.ballerina\" }, { \"match\": \"\\\\\\\\[^pPu]\", \"name\": \"constant.character.escape.backslash.regexp\" }] }, \"regex-unicode-properties-general-category\": { \"patterns\": [{ \"match\": \"(Lu|Ll|Lt|Lm|Lo|L|Mn|Mc|Me|M|Nd|Nl|No|N|Pc|Pd|Ps|Pe|Pi|Pf|Po|P|Sm|Sc|Sk|So|S|Zs|Zl|Zp|Z|Cf|Cc|Cn|Co|C)\", \"name\": \"constant.other.unicode-property-general-category.regexp.ballerina\" }] }, \"regex-unicode-property-key\": { \"patterns\": [{ \"begin\": \"(sc=|gc=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.unicode-property-key.regexp.ballerina\" } }, \"end\": \"()\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.other.unicode-property.end.regexp.ballerina\" } }, \"name\": \"keyword.other.unicode-property-key.regexp.ballerina\", \"patterns\": [{ \"include\": \"#regex-unicode-properties-general-category\" }] }] }, \"regexp\": { \"patterns\": [{ \"match\": \"\\\\^|\\\\$\", \"name\": \"keyword.control.assertion.regexp.ballerina\" }, { \"match\": \"[?+*]|\\\\{(\\\\d+,\\\\d+|\\\\d+,|,\\\\d+|\\\\d+)\\\\}\\\\??\", \"name\": \"keyword.operator.quantifier.regexp.ballerina\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.or.regexp.ballerina\" }, { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp.ballerina\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp.ballerina\" } }, \"name\": \"meta.group.assertion.regexp.ballerina\", \"patterns\": [{ \"include\": \"#template-substitution-element\" }, { \"include\": \"#regexp\" }, { \"include\": \"#flags-on-off\" }, { \"include\": \"#unicode-property-escape\" }] }, { \"begin\": \"(\\\\[)(\\\\^)?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.start.regexp.ballerina\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp.ballerina\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.end.regexp.ballerina\" } }, \"name\": \"constant.other.character-class.set.regexp.ballerina\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.numeric.regexp\" }, \"2\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"3\": { \"name\": \"constant.character.numeric.regexp\" }, \"4\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"match\": \"(?:.|(\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\[^pPu]))-(?:[^\\\\]\\\\\\\\]|(\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\[^pPu]))\", \"name\": \"constant.other.character-class.range.regexp.ballerina\" }, { \"include\": \"#regex-character-class\" }, { \"include\": \"#unicode-values\" }, { \"include\": \"#unicode-property-escape\" }] }, { \"include\": \"#template-substitution-element\" }, { \"include\": \"#regex-character-class\" }, { \"include\": \"#unicode-values\" }, { \"include\": \"#unicode-property-escape\" }] }, \"self-literal\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.language.this.ballerina\" }, \"2\": { \"name\": \"punctuation.accessor.ballerina\" }, \"3\": { \"name\": \"entity.name.function.ballerina\" } }, \"match\": \"(\\\\bself\\\\b)\\\\s*(.)\\\\s*([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(?=\\\\()\" }, { \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))self\\\\b(?!\\\\$)\", \"name\": \"variable.language.this.ballerina\" }] }, \"service-decl\": { \"begin\": \"\\\\bservice\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \"(?=;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|class|const|declare|enum|export|function|import|interface|let|module|namespace|return|service|type|var)\\\\b))|(?<=\\\\})|(?<=\\\\,)\", \"name\": \"meta.service.declaration.ballerina\", \"patterns\": [{ \"include\": \"#class-defn\" }, { \"include\": \"#serviceName\" }, { \"include\": \"#serviceOn\" }, { \"include\": \"#serviceBody\" }, { \"include\": \"#objectDec\" }] }, \"serviceBody\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#mdDocumentation\" }, { \"include\": \"#documentationDef\" }, { \"include\": \"#decl-block\" }] }, \"serviceName\": { \"patterns\": [{ \"include\": \"#string\" }, { \"match\": '(\\\\/([_$A-Za-z][_$0-9A-Za-z]*)|\\\\\"[_$A-Za-z][_$0-9A-Za-z]*\\\\\")', \"name\": \"entity.service.path.ballerina\" }] }, \"serviceOn\": { \"patterns\": [{ \"begin\": \"on\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.ballerina\" } }, \"end\": \"(?={)\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"source\": { \"patterns\": [{ \"begin\": \"(\\\\bsource\\\\b)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.ballerina\" }, \"2\": { \"name\": \"variable.other.readwrite.ballerina\" } }, \"end\": \"(?=\\\\,)|(?=\\\\;)\" }] }, \"statements\": { \"patterns\": [{ \"include\": \"#stringTemplate\" }, { \"include\": \"#declaration\" }, { \"include\": \"#control-statement\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-semicolon\" }, { \"include\": \"#string\" }, { \"include\": \"#comment\" }, { \"include\": \"#mdDocumentation\" }, { \"include\": \"#keywords\" }, { \"include\": \"#annotationAttachment\" }, { \"include\": \"#regex\" }] }, \"string\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ballerina\" } }, \"end\": '(\")|((?:[^\\\\\\\\\\\\n])$)', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.ballerina\" }, \"2\": { \"name\": \"invalid.illegal.newline.ballerina\" } }, \"name\": \"string.quoted.double.ballerina\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }] }, \"string-character-escape\": { \"patterns\": [{ \"match\": \"\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|u\\\\{[0-9A-Fa-f]+\\\\}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\", \"name\": \"constant.character.escape.ballerina\" }] }, \"stringTemplate\": { \"patterns\": [{ \"begin\": \"((string)|([_$A-Za-z][_$0-9A-Za-z]*))?(`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tagged-template.ballerina\" }, \"2\": { \"name\": \"support.type.primitive.ballerina\" }, \"4\": { \"name\": \"punctuation.definition.string.template.begin.ballerina\" } }, \"end\": \"\\\\\\\\?`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.template.end.ballerina\" } }, \"name\": \"string.template.ballerina\", \"patterns\": [{ \"include\": \"#template-substitution-element\" }, { \"include\": \"#string-character-escape\" }] }] }, \"strings\": { \"patterns\": [{ \"begin\": '\\\\\"', \"beginCaptures\": { \"0\": { \"name\": \"string.begin.ballerina\" } }, \"end\": '\\\\\"', \"endCaptures\": { \"0\": { \"name\": \"string.end.ballerina\" } }, \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.ballerina\" }, { \"match\": \".\", \"name\": \"string\" }] }] }, \"template-substitution-element\": { \"patterns\": [{ \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.ballerina\" } }, \"contentName\": \"meta.embedded.line.ballerina\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.ballerina\" } }, \"name\": \"meta.template.expression.ballerina\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"templateVariable\": { \"patterns\": [{ \"begin\": \"\\\\${\", \"beginCaptures\": { \"0\": { \"name\": \"constant.character.escape.ballerina\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"constant.character.escape.ballerina\" } }, \"patterns\": [{ \"include\": \"#code\" }] }] }, \"ternary-expression\": { \"begin\": \"(?!\\\\?\\\\.\\\\s*[^\\\\d])(\\\\?)(?!\\\\?)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.ballerina\" } }, \"end\": \"\\\\s*\", \"endCaptures\": { \"1\": { \"name\": \"keyword.operator.ternary.ballerina\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"tupleType\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"end\": \"(?=\\\\]|;)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#constrainType\" }, { \"include\": \"#paranthesisedBracket\" }, { \"match\": \"\\\\b([_$A-Za-z][_$0-9A-Za-z]*)\\\\b\", \"name\": \"storage.type.ballerina\" }] }] }, \"type\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#numbers\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#type-tuple\" }] }, \"type-annotation\": { \"patterns\": [{ \"begin\": \"(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.type.annotation.ballerina\" } }, \"end\": \"(?<![:|&])((?=$|^|[,);}\\\\]?>=>]|//)|(?==[^>])|((?<=[}>\\\\])]|[_$A-Za-z])\\\\s*(?=\\\\{)))(\\\\?)?\", \"name\": \"meta.type.annotation.ballerina\", \"patterns\": [{ \"include\": \"#booleans\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#regex\" }, { \"include\": \"#self-literal\" }, { \"include\": \"#xml\" }, { \"include\": \"#call\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.ballerina\" }, \"2\": { \"name\": \"constant.language.boolean.ballerina\" }, \"3\": { \"name\": \"keyword.control.ballerina\" }, \"4\": { \"name\": \"storage.type.ballerina\" }, \"5\": { \"name\": \"support.type.primitive.ballerina\" }, \"6\": { \"name\": \"variable.other.readwrite.ballerina\" }, \"8\": { \"name\": \"punctuation.accessor.ballerina\" }, \"9\": { \"name\": \"entity.name.function.ballerina\" }, \"10\": { \"name\": \"punctuation.definition.parameters.begin.ballerina\" }, \"11\": { \"name\": \"punctuation.definition.parameters.end.ballerina\" } }, \"match\": \"\\\\b(is|new|isolated|null|function|in)\\\\b|\\\\b(true|false)\\\\b|\\\\b(check|foreach|if|checkpanic)\\\\b|\\\\b(readonly|error|map)\\\\b|\\\\b(var)\\\\b|([_$A-Za-z][_$0-9A-Za-z]*)((\\\\.)([_$A-Za-z][_$0-9A-Za-z]*)(\\\\()(\\\\)))?\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.optional.ballerina\" }, { \"include\": \"#multiType\" }, { \"include\": \"#type\" }, { \"include\": \"#paranthesised\" }] }] }, \"type-primitive\": { \"patterns\": [{ \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(string|int|boolean|float|byte|decimal|json|xml|anydata)(?![_$0-9A-Za-z])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"support.type.primitive.ballerina\" }] }, \"type-tuple\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.square.ballerina\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.ballerina\" } }, \"name\": \"meta.type.tuple.ballerina\", \"patterns\": [{ \"include\": \"#self-literal\" }, { \"include\": \"#booleans\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.rest.ballerina\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.label.ballerina\" }, \"2\": { \"name\": \"keyword.operator.optional.ballerina\" }, \"3\": { \"name\": \"punctuation.separator.label.ballerina\" } }, \"match\": \"(?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))([_$A-Za-z][_$0-9A-Za-z]*)\\\\s*(\\\\?)?\\\\s*(:)\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#type\" }, { \"include\": \"#punctuation-comma\" }] }, \"typeDefinition\": { \"patterns\": [{ \"begin\": \"(\\\\btype\\\\b)\\\\s+([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.ballerina\" }, \"2\": { \"name\": \"entity.name.type.ballerina\" } }, \"end\": \"\\\\;\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.statement.ballerina\" } }, \"patterns\": [{ \"include\": \"#functionParameters\" }, { \"include\": \"#functionReturns\" }, { \"include\": \"#mdDocumentation\" }, { \"include\": \"#record\" }, { \"include\": \"#string\" }, { \"include\": \"#keywords\" }, { \"include\": \"#multiType\" }, { \"include\": \"#type-primitive\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"variable.other.readwrite.ballerina\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#typeDescription\" }, { \"include\": \"#decl-block\" }] }] }, \"typeDescription\": { \"patterns\": [{ \"begin\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#numbers\" }, { \"include\": \"#decl-block\" }, { \"include\": \"#type-primitive\" }, { \"match\": \"[_$A-Za-z][_$0-9A-Za-z]*\", \"name\": \"storage.type.ballerina\" }] }] }, \"types\": { \"patterns\": [{ \"match\": \"\\\\b(handle|any|future|typedesc)\\\\b\", \"name\": \"storage.type.ballerina\" }, { \"match\": \"\\\\b(boolean|int|string|float|decimal|byte|json|xml|anydata)\\\\b\", \"name\": \"support.type.primitive.ballerina\" }, { \"match\": \"\\\\b(map|error|never|readonly|distinct)\\\\b\", \"name\": \"storage.type.ballerina\" }, { \"match\": \"\\\\b(stream)\\\\b\", \"name\": \"storage.type.ballerina\" }] }, \"unicode-property-escape\": { \"patterns\": [{ \"begin\": \"(\\\\\\\\p|\\\\\\\\P)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.unicode-property.regexp.ballerina\" }, \"2\": { \"name\": \"punctuation.other.unicode-property.begin.regexp.ballerina\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.other.unicode-property.end.regexp.ballerina\" } }, \"name\": \"keyword.other.unicode-property.regexp.ballerina\", \"patterns\": [{ \"include\": \"#regex-unicode-properties-general-category\" }, { \"include\": \"#regex-unicode-property-key\" }] }] }, \"unicode-values\": { \"patterns\": [{ \"begin\": \"(\\\\\\\\u)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.unicode-value.regexp.ballerina\" }, \"2\": { \"name\": \"punctuation.other.unicode-value.begin.regexp.ballerina\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.other.unicode-value.end.regexp.ballerina\" } }, \"name\": \"keyword.other.unicode-value.ballerina\", \"patterns\": [{ \"match\": \"([0-9A-Fa-f]{1,6})\", \"name\": \"constant.other.unicode-value.regexp.ballerina\" }] }] }, \"var-expr\": { \"patterns\": [{ \"begin\": \"(?=\\\\b(var))\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.ballerina support.type.primitive.ballerina\" } }, \"end\": \"(?!\\\\b(var))((?=;|}|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|class|const|declare|enum|export|function|import|interface|let|module|namespace|return|service|type|var)\\\\b))|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(?=(if)\\\\s+))|((?<!^string|[^\\\\._$0-9A-Za-z]string|^int|[^\\\\._$0-9A-Za-z]int)(?=\\\\s*$)))\", \"name\": \"meta.var.expr.ballerina\", \"patterns\": [{ \"begin\": \"\\\\b(var)(?=\\\\s+|\\\\[|\\\\?|\\\\||:)\", \"beginCaptures\": { \"0\": { \"name\": \"support.type.primitive.ballerina\" } }, \"end\": \"(?=\\\\S)\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.type.annotation.ballerina\" }, { \"match\": \"\\\\bin\\\\b\", \"name\": \"keyword.other.ballerina\" }, { \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#numbers\" }, { \"include\": \"#multiType\" }, { \"include\": \"#self-literal\" }, { \"include\": \"#var-single-variable\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#keywords\" }, { \"include\": \"#type-tuple\" }, { \"include\": \"#regex\" }] }, { \"include\": \"#punctuation-comma\" }, { \"begin\": \"(?=\\\\b(const(?!\\\\s+enum\\\\b)))\", \"end\": \"(?!\\\\b(const(?!\\\\s+enum\\\\b)))((?=\\\\bannotation\\\\b|;|}|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|class|const|declare|enum|export|function|import|interface|let|module|namespace|return|service|type|var)\\\\b))|((?<!^string|[^\\\\._$0-9A-Za-z]string|^int|[^\\\\._$0-9A-Za-z]int)(?=\\\\s*$)))\", \"name\": \"meta.var.expr.ballerina\", \"patterns\": [{ \"begin\": \"\\\\b(const(?!\\\\s+enum\\\\b))\\\\s+\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.ballerina\" } }, \"end\": \"(?=\\\\S)\" }, { \"include\": \"#comment\" }, { \"include\": \"#string\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#var-single-const\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#type-annotation\" }] }, { \"include\": \"#punctuation-comma\" }, { \"begin\": \"(string|int|boolean|float|byte|decimal|json|xml|anydata)(?=\\\\s+|\\\\[|\\\\?|\\\\||:)\", \"beginCaptures\": { \"0\": { \"name\": \"support.type.primitive.ballerina\" } }, \"end\": \"(?!\\\\b(var))((?=;|}|;|^\\\\s*$|(?:^\\\\s*(?:abstract|async|class|const|declare|enum|export|function|import|interface|let|module|namespace|return|service|type|var)\\\\b))|((?<!^string|[^\\\\._$0-9A-Za-z]string|^int|[^\\\\._$0-9A-Za-z]int)(?=\\\\s*$)))\", \"name\": \"meta.var.expr.ballerina\", \"patterns\": [{ \"include\": \"#xml\" }, { \"begin\": \"(string|int|boolean|float|byte|decimal|json|xml|anydata)(?=\\\\s+|\\\\[|\\\\?|\\\\||:)\", \"beginCaptures\": { \"0\": { \"name\": \"support.type.primitive.ballerina\" } }, \"end\": \"(?=\\\\S)\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.type.annotation.ballerina\" }, { \"include\": \"#string\" }, { \"include\": \"#stringTemplate\" }, { \"include\": \"#numbers\" }, { \"include\": \"#multiType\" }, { \"include\": \"#var-single-variable\" }, { \"include\": \"#variable-initializer\" }, { \"include\": \"#punctuation-comma\" }, { \"include\": \"#type-annotation\" }, { \"include\": \"#keywords\" }, { \"include\": \"#type-tuple\" }, { \"include\": \"#regex\" }] }, { \"include\": \"#punctuation-comma\" }] }, \"var-single-const\": { \"patterns\": [{ \"name\": \"meta.var-single-variable.expr.ballerina\" }, { \"begin\": \"\\\\b(var)\\\\s*\", \"beginCaptures\": { \"0\": { \"name\": \"support.type.primitive.ballerina\" } }, \"end\": \"(?=\\\\S)\" }, { \"include\": \"#types\" }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.ballerina variable.other.constant.ballerina\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))\\\\s+))\" }] }, \"var-single-variable\": { \"patterns\": [{ \"begin\": \"((string|int|boolean|float|byte|decimal|json|xml|anydata)|\\\\b(readonly|error|map)\\\\b|([_$A-Za-z][_$0-9A-Za-z]*))(?=\\\\s+|\\\\;|>|\\\\|)\", \"beginCaptures\": { \"2\": { \"name\": \"support.type.primitive.ballerina\" }, \"3\": { \"name\": \"storage.type.ballerina\" }, \"4\": { \"name\": \"meta.definition.variable.ballerina variable.other.readwrite.ballerina\" } }, \"end\": \"(?=$|^|[;,=}])\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.statement.ballerina\" } }, \"name\": \"meta.var-single-variable.expr.ballerina\", \"patterns\": [{ \"include\": \"#call\" }, { \"include\": \"#self-literal\" }, { \"include\": \"#if-statement\" }, { \"include\": \"#string\" }, { \"include\": \"#numbers\" }, { \"include\": \"#keywords\" }] }, { \"begin\": \"([_$A-Za-z][_$0-9A-Za-z]*)\\\\s+(!)?\", \"beginCaptures\": { \"1\": { \"name\": \"meta.definition.variable.ballerina variable.other.readwrite.ballerina\" }, \"2\": { \"name\": \"keyword.operator.definiteassignment.ballerina\" } }, \"end\": \"(?=$|^|[;,=}]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))\\\\s+))\", \"name\": \"meta.var-single-variable.expr.ballerina\" }] }, \"variable-initializer\": { \"patterns\": [{ \"begin\": \"(?<!=|!)(=)(?!=|>)(?=\\\\s*\\\\S)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.ballerina\" } }, \"end\": \"(?=$|[,);}\\\\]])\", \"patterns\": [{ \"match\": \"(\\\\')([_$A-Za-z][_$0-9A-Za-z]*)\", \"name\": \"variable.other.property.ballerina\" }, { \"include\": \"#xml\" }, { \"include\": \"#function-defn\" }, { \"include\": \"#expression\" }, { \"include\": \"#punctuation-accessor\" }, { \"include\": \"#regex\" }] }, { \"begin\": \"(?<!=|!)(=)(?!=|>)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.assignment.ballerina\" } }, \"end\": \"(?=[,);}\\\\]]|((?<![_$0-9A-Za-z])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))\\\\s+))|(?=^\\\\s*$)|(?<=\\\\S)(?<!=)(?=\\\\s*$)\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"variableDef\": { \"patterns\": [{ \"begin\": \"(?:(?!\\\\+)[_$A-Za-z][_$0-9A-Za-z]*)(?: |\\\\t)|(?=\\\\()\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.ballerina\" } }, \"end\": \"(?:[_$A-Za-z][_$0-9A-Za-z]*)|(?=\\\\,)|(?=;)|\\\\.\\\\.\\\\.\", \"patterns\": [{ \"include\": \"#tupleType\" }, { \"include\": \"#constrainType\" }, { \"include\": \"#comment\" }] }] }, \"variableDefInline\": { \"patterns\": [{ \"begin\": \"(?=record)|(?=object)\", \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#record\" }, { \"include\": \"#objectDec\" }] }] }, \"workerBody\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"workerDef\": { \"patterns\": [{ \"begin\": \"\\\\bworker\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.ballerina\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#functionReturns\" }, { \"include\": \"#workerBody\" }] }] }, \"xml\": { \"patterns\": [{ \"begin\": \"(\\\\bxml)(\\\\s*)(`)\", \"beginCaptures\": { \"1\": { \"name\": \"support.type.primitive.ballerina\" }, \"3\": { \"name\": \"punctuation.definition.string.template.begin.ballerina\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.template.end.ballerina\" } }, \"name\": \"string.template.ballerina\", \"patterns\": [{ \"include\": \"#xmlTag\" }, { \"include\": \"#xmlComment\" }, { \"include\": \"#templateVariable\" }, { \"match\": \".\", \"name\": \"string\" }] }] }, \"xmlComment\": { \"patterns\": [{ \"begin\": \"<!--\", \"beginCaptures\": { \"0\": { \"name\": \"comment.block.xml.ballerina\" } }, \"end\": \"-->\", \"endCaptures\": { \"0\": { \"name\": \"comment.block.xml.ballerina\" } }, \"name\": \"comment.block.xml.ballerina\" }] }, \"xmlDoubleQuotedString\": { \"patterns\": [{ \"begin\": '\\\\\"', \"beginCaptures\": { \"0\": { \"name\": \"string.begin.ballerina\" } }, \"end\": '\\\\\"', \"endCaptures\": { \"0\": { \"name\": \"string.end.ballerina\" } }, \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.ballerina\" }, { \"match\": \".\", \"name\": \"string\" }] }] }, \"xmlSingleQuotedString\": { \"patterns\": [{ \"begin\": \"\\\\'\", \"beginCaptures\": { \"0\": { \"name\": \"string.begin.ballerina\" } }, \"end\": \"\\\\'\", \"endCaptures\": { \"0\": { \"name\": \"string.end.ballerina\" } }, \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.ballerina\" }, { \"match\": \".\", \"name\": \"string\" }] }] }, \"xmlTag\": { \"patterns\": [{ \"begin\": \"(<\\\\/?\\\\??)\\\\s*([-_a-zA-Z0-9]+)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.xml.ballerina\" }, \"2\": { \"name\": \"entity.name.tag.xml.ballerina\" } }, \"end\": \"\\\\??\\\\/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.xml.ballerina\" } }, \"patterns\": [{ \"include\": \"#xmlSingleQuotedString\" }, { \"include\": \"#xmlDoubleQuotedString\" }, { \"match\": \"xmlns\", \"name\": \"keyword.other.ballerina\" }, { \"match\": \"([a-zA-Z0-9-]+)\", \"name\": \"entity.other.attribute-name.xml.ballerina\" }] }] } }, \"scopeName\": \"source.ballerina\" });\nvar ballerina = [\n  lang\n];\n\nexport { ballerina as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAa,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAa,YAAY;QAAC;YAAE,WAAW;QAAc;KAAE;IAAE,cAAc;QAAE,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2G,QAAQ;gBAAqD;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;gBAA+E;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAK,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,QAAQ;gBAAsC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiD,QAAQ;gBAAiC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAc,OAAO;gBAAwK;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAyB;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAA4C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuD;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAA2B;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAY,QAAQ;gBAAoB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmG,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAA6J,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAoG,QAAQ;gBAAwC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAA2B,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;gBAAE;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;gCAAG,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,SAAS;wBAA0B;wBAAG;4BAAE,SAAS;4BAAS,OAAO;4BAAS,QAAQ;wBAA6C;wBAAG;4BAAE,SAAS;4BAAQ,OAAO;4BAAQ,QAAQ;wBAA6C;wBAAG;4BAAE,SAAS;4BAAO,OAAO;4BAAO,QAAQ;wBAA6C;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAwC;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAA8D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAAkC;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAmC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,SAAS;4BAA8B,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAsI,OAAO;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAwB;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAU,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,QAAQ;gBAAiD;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAyD;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAwC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAwC;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,gBAAgB;YAAE,QAAQ;YAA+B,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+D;wBAAG,KAAK;4BAAE,QAAQ;wBAA8D;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;wBAAG,KAAK;4BAAE,QAAQ;wBAA+D;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAM,QAAQ;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAiC;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA4D,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAW,YAAY;gBAAC;oBAAE,SAAS;oBAAY,QAAQ;gBAA0B;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAgC,QAAQ;oBAAwB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA0C,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;gBAAG,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAA2C,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,QAAQ;gBAAoB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA4C;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,QAAQ;gBAA0B;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoE;oBAAE;oBAAG,SAAS;gBAA6K;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAW,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAA4B;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAqB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAoC,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAsC;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAqC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAuB;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAU,QAAQ;wBAAU;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAU;oBAAE;oBAAG,OAAO;oBAAmC,YAAY;wBAAC;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,gCAAgC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAuC;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;gBAAkC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAktF;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAA+E;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;gBAAoE;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAmC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAoD;wBAAG,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,SAAS;gBAAyE;gBAAG;oBAAE,SAAS;oBAAmC,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwE,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,SAAS;4BAA+D,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,OAAO;4BAAiB,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA6B;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAe;gCAAG;oCAAE,WAAW;gCAAkB;gCAAG;oCAAE,WAAW;gCAAO;gCAAG;oCAAE,WAAW;gCAAU;gCAAG;oCAAE,WAAW;gCAAkB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAsB;gCAAG;oCAAE,WAAW;gCAAwB;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAS;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAqB,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkE;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,SAAS;gBAAoG;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA2C;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6C;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAwB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0G,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAwE,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAA8G,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAA0H,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAwF,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAqH,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA6D;gBAAG;oBAAE,SAAS;oBAAoF,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAA+B;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAA+B;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAO,OAAO;YAAa,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAyC;gBAAG;oBAAE,WAAW;gBAAmC;aAAE;QAAC;QAAG,mCAAmC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAAqC;qBAAE;gBAAC;aAAE;QAAC;QAAG,yCAAyC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAA2C;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwE,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqD,QAAQ;gBAAqC;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,SAAS;oBAAW,OAAO;oBAAkC,QAAQ;oBAAkE,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoB,OAAO;oBAAsE,QAAQ;oBAAkE,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAogB,OAAO;oBAAuC,QAAQ;oBAAkE,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAoC,OAAO;oBAAwB,QAAQ;oBAAqC,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAA2E,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;oBAA+vF,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAAiF,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,SAAS;oBAA2D,QAAQ;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;oBAAgF,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAyD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAsG,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAqC,OAAO;oBAA6B,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAW,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAA+B;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,SAAS;4BAAiB,QAAQ;wBAAoB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+F,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAuC;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;gBAAmB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,MAAM;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA+Y;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAwC,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAuC;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;gBAA0B;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyE,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA4C;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,QAAQ;YAAoC,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAA0C;oBAAE;oBAAG,SAAS;gBAAkC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAwC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAA6C;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAgB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0B;YAAE;YAAG,OAAO;YAAY,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,QAAQ;gBAAiD;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA6C;aAAE;QAAC;QAAG,6CAA6C;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0G,QAAQ;gBAAoE;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,OAAO;oBAAM,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,QAAQ;oBAAuD,YAAY;wBAAC;4BAAE,WAAW;wBAA6C;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,QAAQ;gBAA6C;gBAAG;oBAAE,SAAS;oBAAgD,QAAQ;gBAA+C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAS,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,QAAQ;oBAAyC,YAAY;wBAAC;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAe,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgE;wBAAG,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8D;oBAAE;oBAAG,QAAQ;oBAAuD,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;gCAAG,KAAK;oCAAE,QAAQ;gCAA6C;4BAAE;4BAAG,SAAS;4BAAwJ,QAAQ;wBAAwD;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAiC;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAA2B;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAA+D;gBAAG;oBAAE,SAAS;oBAA+D,QAAQ;gBAAmC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAwK,QAAQ;YAAsC,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAAgC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;gBAAkB;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAS;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAyB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmH,QAAQ;gBAAsC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAiC;wBAAG;4BAAE,WAAW;wBAA2B;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,iCAAiC;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,eAAe;oBAAgC,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAAsC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAQ,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,OAAO;oBAA8F,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAS;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAA4B;gCAAG,KAAK;oCAAE,QAAQ;gCAAyB;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;gCAAG,KAAK;oCAAE,QAAQ;gCAAqC;gCAAG,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,KAAK;oCAAE,QAAQ;gCAAiC;gCAAG,MAAM;oCAAE,QAAQ;gCAAoD;gCAAG,MAAM;oCAAE,QAAQ;gCAAkD;4BAAE;4BAAG,SAAS;wBAAgN;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAAsC;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmJ,QAAQ;gBAAmC;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAkC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,SAAS;gBAA2F;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAqC;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,SAAS;4BAA4B,QAAQ;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsC,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAA6C,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAAyB;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkD;wBAAG,KAAK;4BAAE,QAAQ;wBAA4D;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA0D;oBAAE;oBAAG,QAAQ;oBAAmD,YAAY;wBAAC;4BAAE,WAAW;wBAA6C;wBAAG;4BAAE,WAAW;wBAA8B;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;wBAAG,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAAS,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAAyC,YAAY;wBAAC;4BAAE,SAAS;4BAAsB,QAAQ;wBAAgD;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8D;oBAAE;oBAAG,OAAO;oBAA8S,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAAkC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,OAAO;wBAAU;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAA6C;wBAAG;4BAAE,SAAS;4BAAY,QAAQ;wBAA0B;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAiC,OAAO;oBAAoR,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,SAAS;4BAAiC,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;4BAAE;4BAAG,OAAO;wBAAU;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAmB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAkF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAkP,QAAQ;oBAA2B,YAAY;wBAAC;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,SAAS;4BAAkF,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,OAAO;wBAAU;wBAAG;4BAAE,SAAS;4BAAO,QAAQ;wBAA6C;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAuB;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;gBAAU;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuE;oBAAE;oBAAG,OAAO;gBAAqE;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsI,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwE;oBAAE;oBAAG,OAAO;oBAAkB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA6C;oBAAE;oBAAG,QAAQ;oBAA2C,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwE;wBAAG,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAsE,QAAQ;gBAA0C;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAmB,YAAY;wBAAC;4BAAE,SAAS;4BAAmC,QAAQ;wBAAoC;wBAAG;4BAAE,WAAW;wBAAO;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAS;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwC;oBAAE;oBAAG,OAAO;oBAAwG,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAwD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAwD,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAmB;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,OAAO;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAyD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuD;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,QAAQ;gBAA8B;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAS;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAa,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA+C;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAA0B;wBAAG;4BAAE,SAAS;4BAAmB,QAAQ;wBAA4C;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAmB;AAC9z4D,IAAI,YAAY;IACd;CACD", "ignoreList": [0], "debugId": null}}]}