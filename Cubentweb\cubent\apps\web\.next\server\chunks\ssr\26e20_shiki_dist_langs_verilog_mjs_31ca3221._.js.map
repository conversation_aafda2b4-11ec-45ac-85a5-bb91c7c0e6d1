{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/verilog.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Verilog\", \"fileTypes\": [\"v\", \"vh\"], \"name\": \"verilog\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#module_pattern\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }, { \"include\": \"#operators\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.verilog\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.verilog\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-slash.verilog\" }] }, { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.c-style.verilog\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"`(?!(celldefine|endcelldefine|default_nettype|define|undef|ifdef|ifndef|else|endif|include|resetall|timescale|unconnected_drive|nounconnected_drive))[a-z_A-Z][a-zA-Z0-9_$]*\", \"name\": \"variable.other.constant.verilog\" }, { \"match\": \"\\\\d*'[bBoOdDhH][a-fA-F0-9_xXzZ]+\\\\b\", \"name\": \"constant.numeric.sized_integer.verilog\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.verilog\" }, \"2\": { \"name\": \"punctuation.separator.range.verilog\" }, \"3\": { \"name\": \"constant.numeric.integer.verilog\" } }, \"match\": \"\\\\b(\\\\d+)(:)(\\\\d+)\\\\b\", \"name\": \"meta.block.numeric.range.verilog\" }, { \"match\": \"\\\\b\\\\d[\\\\d_]*(?i:e\\\\d+)?\\\\b\", \"name\": \"constant.numeric.integer.verilog\" }, { \"match\": \"\\\\b\\\\d+\\\\.\\\\d+(?i:e\\\\d+)?\\\\b\", \"name\": \"constant.numeric.real.verilog\" }, { \"match\": \"#\\\\d+\", \"name\": \"constant.numeric.delay.verilog\" }, { \"match\": \"\\\\b[01xXzZ]+\\\\b\", \"name\": \"constant.numeric.logic.verilog\" }] }, \"instantiation_patterns\": { \"patterns\": [{ \"include\": \"#keywords\" }, { \"begin\": \"^\\\\s*([a-zA-Z]\\\\w*)\\\\s+([a-zA-Z]\\\\w*)(?<!begin|if)\\\\s*(?=\\\\(|$)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.module.reference.verilog\" }, \"2\": { \"name\": \"entity.name.tag.module.identifier.verilog\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.expression.verilog\" } }, \"name\": \"meta.block.instantiation.parameterless.verilog\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }] }, { \"begin\": \"^\\\\s*([a-zA-Z]\\\\w*)\\\\s*(#)(?=\\\\s*\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.module.reference.verilog\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.expression.verilog\" } }, \"name\": \"meta.block.instantiation.with.parameters.verilog\", \"patterns\": [{ \"include\": \"#parenthetical_list\" }, { \"match\": \"[a-zA-Z]\\\\w*\", \"name\": \"entity.name.tag.module.identifier.verilog\" }] }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(always|and|assign|attribute|begin|buf|bufif0|bufif1|case[xz]?|cmos|deassign|default|defparam|disable|edge|else|end(attribute|case|function|generate|module|primitive|specify|table|task)?|event|for|force|forever|fork|function|generate|genvar|highz(01)|if(none)?|initial|inout|input|integer|join|localparam|medium|module|large|macromodule|nand|negedge|nmos|nor|not|notif(01)|or|output|parameter|pmos|posedge|primitive|pull0|pull1|pulldown|pullup|rcmos|real|realtime|reg|release|repeat|rnmos|rpmos|rtran|rtranif(01)|scalared|signed|small|specify|specparam|strength|strong0|strong1|supply0|supply1|table|task|time|tran|tranif(01)|tri(01)?|tri(and|or|reg)|unsigned|vectored|wait|wand|weak(01)|while|wire|wor|xnor|xor)\\\\b\", \"name\": \"keyword.other.verilog\" }, { \"match\": \"^\\\\s*`((cell)?define|default_(decay_time|nettype|trireg_strength)|delay_mode_(path|unit|zero)|ifdef|ifndef|include|end(if|celldefine)|else|(no)?unconnected_drive|resetall|timescale|undef)\\\\b\", \"name\": \"keyword.other.compiler.directive.verilog\" }, { \"match\": \"\\\\$(f(open|close)|readmem(b|h)|timeformat|printtimescale|stop|finish|(s|real)?time|realtobits|bitstoreal|rtoi|itor|(f)?(display|write(h|b)))\\\\b\", \"name\": \"support.function.system.console.tasks.verilog\" }, { \"match\": \"\\\\$(random|dist_(chi_square|erlang|exponential|normal|poisson|t|uniform))\\\\b\", \"name\": \"support.function.system.random_number.tasks.verilog\" }, { \"match\": \"\\\\$((a)?sync\\\\$((n)?and|(n)or)\\\\$(array|plane))\\\\b\", \"name\": \"support.function.system.pld_modeling.tasks.verilog\" }, { \"match\": \"\\\\$(q_(initialize|add|remove|full|exam))\\\\b\", \"name\": \"support.function.system.stochastic.tasks.verilog\" }, { \"match\": \"\\\\$(hold|nochange|period|recovery|setup(hold)?|skew|width)\\\\b\", \"name\": \"support.function.system.timing.tasks.verilog\" }, { \"match\": \"\\\\$(dump(file|vars|off|on|all|limit|flush))\\\\b\", \"name\": \"support.function.system.vcd.tasks.verilog\" }, { \"match\": \"\\\\$(countdrivers|list|input|scope|showscopes|(no)?(key|log)|reset(_count|_value)?|(inc)?save|restart|showvars|getpattern|sreadmem(b|h)|scale)\", \"name\": \"support.function.non-standard.tasks.verilog\" }] }, \"module_pattern\": { \"patterns\": [{ \"begin\": \"\\\\b(module)\\\\s+([a-zA-Z]\\\\w*)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.module.verilog\" }, \"2\": { \"name\": \"entity.name.type.module.verilog\" } }, \"end\": \"\\\\bendmodule\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"storage.type.module.verilog\" } }, \"name\": \"meta.block.module.verilog\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }, { \"include\": \"#instantiation_patterns\" }, { \"include\": \"#operators\" }] }] }, \"operators\": { \"patterns\": [{ \"match\": \"\\\\+|-|\\\\*|/|%|(<|>)=?|(!|=)?==?|!|&&?|\\\\|\\\\|?|\\\\^?~|~\\\\^?\", \"name\": \"keyword.operator.verilog\" }] }, \"parenthetical_list\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.list.verilog\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.list.verilog\" } }, \"name\": \"meta.block.parenthetical_list.verilog\", \"patterns\": [{ \"include\": \"#parenthetical_list\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }] }] }, \"strings\": { \"patterns\": [{ \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.verilog\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.verilog\" }] }] } }, \"scopeName\": \"source.verilog\" });\nvar verilog = [\n  lang\n];\n\nexport { verilog as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,aAAa;QAAC;QAAK;KAAK;IAAE,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAa;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAyC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAoC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,OAAO;oBAAQ,QAAQ;gBAAgC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgL,QAAQ;gBAAkC;gBAAG;oBAAE,SAAS;oBAAuC,QAAQ;gBAAyC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,SAAS;oBAAyB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAiC;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAmE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,QAAQ;oBAAkD,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAyC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,QAAQ;oBAAoD,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA4C;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAktB,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAkM,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAAmJ,QAAQ;gBAAgD;gBAAG;oBAAE,SAAS;oBAAgF,QAAQ;gBAAsD;gBAAG;oBAAE,SAAS;oBAAsD,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAA+C,QAAQ;gBAAmD;gBAAG;oBAAE,SAAS;oBAAiE,QAAQ;gBAA+C;gBAAG;oBAAE,SAAS;oBAAkD,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAiJ,QAAQ;gBAA8C;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAmB,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA6D,QAAQ;gBAA2B;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,QAAQ;oBAAyC,YAAY;wBAAC;4BAAE,WAAW;wBAAsB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,OAAO;oBAAK,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAoC;qBAAE;gBAAC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAiB;AACv8L,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}