{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/zenscript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"ZenScript\", \"fileTypes\": [\"zs\"], \"name\": \"zenscript\", \"patterns\": [{ \"comment\": \"numbers\", \"match\": \"\\\\b((0(x|X)[0-9a-fA-F]*)|((\\\\d+\\\\.?\\\\d*)|(\\\\.\\\\d+))((e|E)(\\\\+|-)?\\\\d+)?)([LlFfUuDd]|UL|ul)?\\\\b\", \"name\": \"constant.numeric.zenscript\" }, { \"comment\": \"prefixedNumbers\", \"match\": \"\\\\b-?(0b|0x|0o|0B|0X|0O)(0|[1-9a-fA-F][0-9a-fA-F_]*)[a-zA-Z_]*\\\\b\", \"name\": \"constant.numeric.zenscript\" }, { \"include\": \"#code\" }, { \"comment\": \"arrays\", \"match\": \"\\\\b((?:[a-z]\\\\w*\\\\.)*[A-Z]+\\\\w*)(?=\\\\[)\", \"name\": \"storage.type.object.array.zenscript\" }], \"repository\": { \"brackets\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.zenscript\" }, \"2\": { \"name\": \"keyword.other.zenscript\" }, \"3\": { \"name\": \"keyword.control.zenscript\" }, \"4\": { \"name\": \"variable.other.zenscript\" }, \"5\": { \"name\": \"keyword.control.zenscript\" }, \"6\": { \"name\": \"constant.numeric.zenscript\" }, \"7\": { \"name\": \"keyword.control.zenscript\" } }, \"comment\": \"items and blocks\", \"match\": \"(<)\\\\b(.*?)(:(.*?(:(\\\\*|\\\\d+)?)?)?)(>)\", \"name\": \"keyword.other.zenscript\" }] }, \"class\": { \"captures\": { \"1\": { \"name\": \"storage.type.zenscript\" }, \"2\": { \"name\": \"entity.name.type.class.zenscript\" } }, \"comment\": \"class\", \"match\": \"(zenClass)\\\\s+(\\\\w+)\", \"name\": \"meta.class.zenscript\" }, \"code\": { \"patterns\": [{ \"include\": \"#class\" }, { \"include\": \"#functions\" }, { \"include\": \"#dots\" }, { \"include\": \"#quotes\" }, { \"include\": \"#brackets\" }, { \"include\": \"#comments\" }, { \"include\": \"#var\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#operators\" }] }, \"comments\": { \"patterns\": [{ \"comment\": \"inline comments\", \"match\": \"//[^\\n]*\", \"name\": \"comment.line.double=slash\" }, { \"begin\": \"\\\\/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"comment.block\" } }, \"comment\": \"block comments\", \"end\": \"\\\\*\\\\/\", \"endCaptures\": { \"0\": { \"name\": \"comment.block\" } }, \"name\": \"comment.block\" }] }, \"dots\": { \"captures\": { \"1\": { \"name\": \"storage.type.zenscript\" }, \"2\": { \"name\": \"keyword.control.zenscript\" }, \"5\": { \"name\": \"keyword.control.zenscript\" } }, \"comment\": \"dots\", \"match\": \"\\\\b(\\\\w+)(\\\\.)(\\\\w+)((\\\\.)(\\\\w+))*\", \"name\": \"plain.text.zenscript\" }, \"functions\": { \"captures\": { \"0\": { \"name\": \"storage.type.function.zenscript\" }, \"1\": { \"name\": \"entity.name.function.zenscript\" } }, \"comment\": \"functions\", \"match\": \"function\\\\s+([A-Za-z_$][\\\\w$]*)\\\\s*(?=\\\\()\", \"name\": \"meta.function.zenscript\" }, \"keywords\": { \"patterns\": [{ \"comment\": \"statement keywords\", \"match\": \"\\\\b(instanceof|get|implements|set|import|function|override|const|if|else|do|while|for|throw|panic|lock|try|catch|finally|return|break|continue|switch|case|default|in|is|as|match|throws|super|new)\\\\b\", \"name\": \"keyword.control.zenscript\" }, { \"comment\": \"storage keywords\", \"match\": \"\\\\b(zenClass|zenConstructor|alias|class|interface|enum|struct|expand|variant|set|void|bool|byte|sbyte|short|ushort|int|uint|long|ulong|usize|float|double|char|string)\\\\b\", \"name\": \"storage.type.zenscript\" }, { \"comment\": \"modifier keywords\", \"match\": \"\\\\b(variant|abstract|final|private|public|export|internal|static|protected|implicit|virtual|extern|immutable)\\\\b\", \"name\": \"storage.modifier.zenscript\" }, { \"comment\": \"annotation keywords\", \"match\": \"\\\\b(Native|Precondition)\\\\b\", \"name\": \"entity.other.attribute-name\" }, { \"comment\": \"language keywords\", \"match\": \"\\\\b(null|true|false)\\\\b\", \"name\": \"constant.language\" }] }, \"operators\": { \"patterns\": [{ \"comment\": \"math operators\", \"match\": \"\\\\b(\\\\.|\\\\.\\\\.|\\\\.\\\\.\\\\.|,|\\\\+|\\\\+=|\\\\+\\\\+|-|-=|--|~|~=|\\\\*|\\\\*=|/|/=|%|%=|\\\\||\\\\|=|\\\\|\\\\||&|&=|&&|\\\\^|\\\\^=|\\\\?|\\\\?\\\\.|\\\\?\\\\?|<|<=|<<|<<=|>|>=|>>|>>=|>>>|>>>=|=>|=|==|===|!|!=|!==|\\\\$|`)\\\\b\", \"name\": \"keyword.control\" }, { \"comment\": \"colons\", \"match\": \"\\\\b(;|:)\\\\b\", \"name\": \"keyword.control\" }] }, \"quotes\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.zenscript\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.zenscript\" } }, \"name\": \"string.quoted.double.zenscript\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.zenscript\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.zenscript\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.zenscript\" } }, \"name\": \"string.quoted.single.zenscript\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.zenscript\" }] }] }, \"var\": { \"comment\": \"var\", \"match\": \"\\\\b(val|var)\\\\b\", \"name\": \"storage.type\" } }, \"scopeName\": \"source.zenscript\" });\nvar zenscript = [\n  lang\n];\n\nexport { zenscript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAa,aAAa;QAAC;KAAK;IAAE,QAAQ;IAAa,YAAY;QAAC;YAAE,WAAW;YAAW,SAAS;YAAkG,QAAQ;QAA6B;QAAG;YAAE,WAAW;YAAmB,SAAS;YAAqE,QAAQ;QAA6B;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;YAAU,SAAS;YAA2C,QAAQ;QAAsC;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,WAAW;oBAAoB,SAAS;oBAA0C,QAAQ;gBAA0B;aAAE;QAAC;QAAG,SAAS;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAmC;YAAE;YAAG,WAAW;YAAS,SAAS;YAAwB,QAAQ;QAAuB;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAmB,SAAS;oBAAY,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgB;oBAAE;oBAAG,WAAW;oBAAkB,OAAO;oBAAU,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAgB;oBAAE;oBAAG,QAAQ;gBAAgB;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,WAAW;YAAQ,SAAS;YAAsC,QAAQ;QAAuB;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAkC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,WAAW;YAAa,SAAS;YAA8C,QAAQ;QAA0B;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAsB,SAAS;oBAA0M,QAAQ;gBAA4B;gBAAG;oBAAE,WAAW;oBAAoB,SAAS;oBAA6K,QAAQ;gBAAyB;gBAAG;oBAAE,WAAW;oBAAqB,SAAS;oBAAoH,QAAQ;gBAA6B;gBAAG;oBAAE,WAAW;oBAAuB,SAAS;oBAA+B,QAAQ;gBAA8B;gBAAG;oBAAE,WAAW;oBAAqB,SAAS;oBAA2B,QAAQ;gBAAoB;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;oBAAkB,SAAS;oBAAiM,QAAQ;gBAAkB;gBAAG;oBAAE,WAAW;oBAAU,SAAS;oBAAe,QAAQ;gBAAkB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgD;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,QAAQ;oBAAkC,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAsC;qBAAE;gBAAC;aAAE;QAAC;QAAG,OAAO;YAAE,WAAW;YAAO,SAAS;YAAmB,QAAQ;QAAe;IAAE;IAAG,aAAa;AAAmB;AAC19I,IAAI,YAAY;IACd;CACD", "ignoreList": [0], "debugId": null}}]}