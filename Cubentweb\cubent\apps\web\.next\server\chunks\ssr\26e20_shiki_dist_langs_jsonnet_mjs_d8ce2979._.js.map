{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/jsonnet.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Jsonnet\", \"name\": \"jsonnet\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#keywords\" }], \"repository\": { \"builtin-functions\": { \"patterns\": [{ \"match\": \"\\\\bstd[.](acos|asin|atan|ceil|char|codepoint|cos|exp|exponent)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](filter|floor|force|length|log|makeArray|mantissa)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](objectFields|objectHas|pow|sin|sqrt|tan|type|thisFile)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](acos|asin|atan|ceil|char|codepoint|cos|exp|exponent)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](abs|assertEqual|escapeString(Bash|Dollars|Json|Python))\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](filterMap|flattenArrays|foldl|foldr|format|join)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](lines|manifest(Ini|Python(Vars)?)|map|max|min|mod)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](set|set(Diff|Inter|Member|Union)|sort)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](range|split|stringChars|substr|toString|uniq)\\\\b\", \"name\": \"support.function.jsonnet\" }] }, \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.jsonnet\" }, { \"match\": \"//.*$\", \"name\": \"comment.line.jsonnet\" }, { \"match\": \"#.*$\", \"name\": \"comment.block.jsonnet\" }] }, \"double-quoted-strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.jsonnet\", \"patterns\": [{ \"match\": '\\\\\\\\([\"\\\\\\\\/bfnrt]|(u[0-9a-fA-F]{4}))', \"name\": \"constant.character.escape.jsonnet\" }, { \"match\": '\\\\\\\\[^\"\\\\\\\\/bfnrtu]', \"name\": \"invalid.illegal.jsonnet\" }] }, \"expression\": { \"patterns\": [{ \"include\": \"#literals\" }, { \"include\": \"#comment\" }, { \"include\": \"#single-quoted-strings\" }, { \"include\": \"#double-quoted-strings\" }, { \"include\": \"#triple-quoted-strings\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#functions\" }] }, \"functions\": { \"patterns\": [{ \"begin\": \"\\\\b([a-zA-Z_][a-z0-9A-Z_]*)\\\\s*\\\\(\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.jsonnet\" } }, \"end\": \"\\\\)\", \"name\": \"meta.function\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"keywords\": { \"patterns\": [{ \"match\": \"[!:~+\\\\-&\\\\|\\\\^=<>\\\\*\\\\/%]\", \"name\": \"keyword.operator.jsonnet\" }, { \"match\": \"\\\\$\", \"name\": \"keyword.other.jsonnet\" }, { \"match\": \"\\\\b(self|super|import|importstr|local|tailstrict)\\\\b\", \"name\": \"keyword.other.jsonnet\" }, { \"match\": \"\\\\b(if|then|else|for|in|error|assert)\\\\b\", \"name\": \"keyword.control.jsonnet\" }, { \"match\": \"\\\\b(function)\\\\b\", \"name\": \"storage.type.jsonnet\" }, { \"match\": \"[a-zA-Z_][a-z0-9A-Z_]*\\\\s*(:::|\\\\+:::)\", \"name\": \"variable.parameter.jsonnet\" }, { \"match\": \"[a-zA-Z_][a-z0-9A-Z_]*\\\\s*(::|\\\\+::)\", \"name\": \"entity.name.type\" }, { \"match\": \"[a-zA-Z_][a-z0-9A-Z_]*\\\\s*(:|\\\\+:)\", \"name\": \"variable.parameter.jsonnet\" }] }, \"literals\": { \"patterns\": [{ \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.jsonnet\" }, { \"match\": \"\\\\b(\\\\d+([Ee][+-]?\\\\d+)?)\\\\b\", \"name\": \"constant.numeric.jsonnet\" }, { \"match\": \"\\\\b\\\\d+[.]\\\\d*([Ee][+-]?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.jsonnet\" }, { \"match\": \"\\\\b[.]\\\\d+([Ee][+-]?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.jsonnet\" }] }, \"single-quoted-strings\": { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.double.jsonnet\", \"patterns\": [{ \"match\": \"\\\\\\\\(['\\\\\\\\/bfnrt]|(u[0-9a-fA-F]{4}))\", \"name\": \"constant.character.escape.jsonnet\" }, { \"match\": \"\\\\\\\\[^'\\\\\\\\/bfnrtu]\", \"name\": \"invalid.illegal.jsonnet\" }] }, \"triple-quoted-strings\": { \"patterns\": [{ \"begin\": \"\\\\|\\\\|\\\\|\", \"end\": \"\\\\|\\\\|\\\\|\", \"name\": \"string.quoted.triple.jsonnet\" }] } }, \"scopeName\": \"source.jsonnet\" });\nvar jsonnet = [\n  lang\n];\n\nexport { jsonnet as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAW,QAAQ;IAAW,YAAY;QAAC;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAkE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAuE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAqE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAwE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAiE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAmE,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAuD,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAA8D,QAAQ;gBAA2B;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,OAAO;oBAAQ,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAwB;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA0B;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAwD,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAA4C,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAA0C,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAwC,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAAsC,QAAQ;gBAA6B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAA2B;aAAE;QAAC;QAAG,yBAAyB;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAAgC,YAAY;gBAAC;oBAAE,SAAS;oBAAyC,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAuB,QAAQ;gBAA0B;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,OAAO;oBAAa,QAAQ;gBAA+B;aAAE;QAAC;IAAE;IAAG,aAAa;AAAiB;AACloH,IAAI,UAAU;IACZ;CACD", "ignoreList": [0], "debugId": null}}]}