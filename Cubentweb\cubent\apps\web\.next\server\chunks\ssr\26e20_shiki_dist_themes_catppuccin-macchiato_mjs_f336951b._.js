module.exports = {

"[project]/node_modules/.pnpm/shiki@1.17.7/node_modules/shiki/dist/themes/catppuccin-macchiato.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>catppuccinMacchiato)
});
var catppuccinMacchiato = Object.freeze({
    "colors": {
        "activityBar.activeBackground": "#00000000",
        "activityBar.activeBorder": "#00000000",
        "activityBar.activeFocusBorder": "#00000000",
        "activityBar.background": "#181926",
        "activityBar.border": "#00000000",
        "activityBar.dropBorder": "#c6a0f633",
        "activityBar.foreground": "#c6a0f6",
        "activityBar.inactiveForeground": "#6e738d",
        "activityBarBadge.background": "#c6a0f6",
        "activityBarBadge.foreground": "#181926",
        "activityBarTop.activeBorder": "#00000000",
        "activityBarTop.dropBorder": "#c6a0f633",
        "activityBarTop.foreground": "#c6a0f6",
        "activityBarTop.inactiveForeground": "#6e738d",
        "badge.background": "#494d64",
        "badge.foreground": "#cad3f5",
        "banner.background": "#494d64",
        "banner.foreground": "#cad3f5",
        "banner.iconForeground": "#cad3f5",
        "breadcrumb.activeSelectionForeground": "#c6a0f6",
        "breadcrumb.background": "#24273a",
        "breadcrumb.focusForeground": "#c6a0f6",
        "breadcrumb.foreground": "#cad3f5cc",
        "breadcrumbPicker.background": "#1e2030",
        "button.background": "#c6a0f6",
        "button.border": "#00000000",
        "button.foreground": "#181926",
        "button.hoverBackground": "#dac1f9",
        "button.secondaryBackground": "#5b6078",
        "button.secondaryBorder": "#c6a0f6",
        "button.secondaryForeground": "#cad3f5",
        "button.secondaryHoverBackground": "#6a708c",
        "button.separator": "#00000000",
        "charts.blue": "#8aadf4",
        "charts.foreground": "#cad3f5",
        "charts.green": "#a6da95",
        "charts.lines": "#b8c0e0",
        "charts.orange": "#f5a97f",
        "charts.purple": "#c6a0f6",
        "charts.red": "#ed8796",
        "charts.yellow": "#eed49f",
        "checkbox.background": "#494d64",
        "checkbox.border": "#00000000",
        "checkbox.foreground": "#c6a0f6",
        "commandCenter.activeBackground": "#5b607833",
        "commandCenter.activeBorder": "#c6a0f6",
        "commandCenter.activeForeground": "#c6a0f6",
        "commandCenter.background": "#1e2030",
        "commandCenter.border": "#00000000",
        "commandCenter.foreground": "#b8c0e0",
        "commandCenter.inactiveBorder": "#00000000",
        "commandCenter.inactiveForeground": "#b8c0e0",
        "debugConsole.errorForeground": "#ed8796",
        "debugConsole.infoForeground": "#8aadf4",
        "debugConsole.sourceForeground": "#f4dbd6",
        "debugConsole.warningForeground": "#f5a97f",
        "debugConsoleInputIcon.foreground": "#cad3f5",
        "debugExceptionWidget.background": "#181926",
        "debugExceptionWidget.border": "#c6a0f6",
        "debugIcon.breakpointCurrentStackframeForeground": "#5b6078",
        "debugIcon.breakpointDisabledForeground": "#ed879699",
        "debugIcon.breakpointForeground": "#ed8796",
        "debugIcon.breakpointStackframeForeground": "#5b6078",
        "debugIcon.breakpointUnverifiedForeground": "#a47487",
        "debugIcon.continueForeground": "#a6da95",
        "debugIcon.disconnectForeground": "#5b6078",
        "debugIcon.pauseForeground": "#8aadf4",
        "debugIcon.restartForeground": "#8bd5ca",
        "debugIcon.startForeground": "#a6da95",
        "debugIcon.stepBackForeground": "#5b6078",
        "debugIcon.stepIntoForeground": "#cad3f5",
        "debugIcon.stepOutForeground": "#cad3f5",
        "debugIcon.stepOverForeground": "#c6a0f6",
        "debugIcon.stopForeground": "#ed8796",
        "debugTokenExpression.boolean": "#c6a0f6",
        "debugTokenExpression.error": "#ed8796",
        "debugTokenExpression.number": "#f5a97f",
        "debugTokenExpression.string": "#a6da95",
        "debugToolBar.background": "#181926",
        "debugToolBar.border": "#00000000",
        "descriptionForeground": "#cad3f5",
        "diffEditor.border": "#5b6078",
        "diffEditor.diagonalFill": "#5b607899",
        "diffEditor.insertedLineBackground": "#a6da9526",
        "diffEditor.insertedTextBackground": "#a6da951a",
        "diffEditor.removedLineBackground": "#ed879626",
        "diffEditor.removedTextBackground": "#ed87961a",
        "diffEditorOverview.insertedForeground": "#a6da95cc",
        "diffEditorOverview.removedForeground": "#ed8796cc",
        "disabledForeground": "#a5adcb",
        "dropdown.background": "#1e2030",
        "dropdown.border": "#c6a0f6",
        "dropdown.foreground": "#cad3f5",
        "dropdown.listBackground": "#5b6078",
        "editor.background": "#24273a",
        "editor.findMatchBackground": "#604456",
        "editor.findMatchBorder": "#ed879633",
        "editor.findMatchHighlightBackground": "#455c6d",
        "editor.findMatchHighlightBorder": "#91d7e333",
        "editor.findRangeHighlightBackground": "#455c6d",
        "editor.findRangeHighlightBorder": "#91d7e333",
        "editor.focusedStackFrameHighlightBackground": "#a6da9526",
        "editor.foldBackground": "#91d7e340",
        "editor.foreground": "#cad3f5",
        "editor.hoverHighlightBackground": "#91d7e340",
        "editor.lineHighlightBackground": "#cad3f512",
        "editor.lineHighlightBorder": "#00000000",
        "editor.rangeHighlightBackground": "#91d7e340",
        "editor.rangeHighlightBorder": "#00000000",
        "editor.selectionBackground": "#939ab740",
        "editor.selectionHighlightBackground": "#939ab733",
        "editor.selectionHighlightBorder": "#939ab733",
        "editor.stackFrameHighlightBackground": "#eed49f26",
        "editor.wordHighlightBackground": "#939ab733",
        "editorBracketHighlight.foreground1": "#ed8796",
        "editorBracketHighlight.foreground2": "#f5a97f",
        "editorBracketHighlight.foreground3": "#eed49f",
        "editorBracketHighlight.foreground4": "#a6da95",
        "editorBracketHighlight.foreground5": "#7dc4e4",
        "editorBracketHighlight.foreground6": "#c6a0f6",
        "editorBracketHighlight.unexpectedBracket.foreground": "#ee99a0",
        "editorBracketMatch.background": "#939ab71a",
        "editorBracketMatch.border": "#939ab7",
        "editorCodeLens.foreground": "#8087a2",
        "editorCursor.background": "#24273a",
        "editorCursor.foreground": "#f4dbd6",
        "editorError.background": "#00000000",
        "editorError.border": "#00000000",
        "editorError.foreground": "#ed8796",
        "editorGroup.border": "#5b6078",
        "editorGroup.dropBackground": "#c6a0f633",
        "editorGroup.emptyBackground": "#24273a",
        "editorGroupHeader.tabsBackground": "#181926",
        "editorGutter.addedBackground": "#a6da95",
        "editorGutter.background": "#24273a",
        "editorGutter.commentGlyphForeground": "#c6a0f6",
        "editorGutter.commentRangeForeground": "#363a4f",
        "editorGutter.deletedBackground": "#ed8796",
        "editorGutter.foldingControlForeground": "#939ab7",
        "editorGutter.modifiedBackground": "#eed49f",
        "editorHoverWidget.background": "#1e2030",
        "editorHoverWidget.border": "#5b6078",
        "editorHoverWidget.foreground": "#cad3f5",
        "editorIndentGuide.activeBackground": "#5b6078",
        "editorIndentGuide.background": "#494d64",
        "editorInfo.background": "#00000000",
        "editorInfo.border": "#00000000",
        "editorInfo.foreground": "#8aadf4",
        "editorInlayHint.background": "#1e2030bf",
        "editorInlayHint.foreground": "#5b6078",
        "editorInlayHint.parameterBackground": "#1e2030bf",
        "editorInlayHint.parameterForeground": "#a5adcb",
        "editorInlayHint.typeBackground": "#1e2030bf",
        "editorInlayHint.typeForeground": "#b8c0e0",
        "editorLightBulb.foreground": "#eed49f",
        "editorLineNumber.activeForeground": "#c6a0f6",
        "editorLineNumber.foreground": "#8087a2",
        "editorLink.activeForeground": "#c6a0f6",
        "editorMarkerNavigation.background": "#1e2030",
        "editorMarkerNavigationError.background": "#ed8796",
        "editorMarkerNavigationInfo.background": "#8aadf4",
        "editorMarkerNavigationWarning.background": "#f5a97f",
        "editorOverviewRuler.background": "#1e2030",
        "editorOverviewRuler.border": "#cad3f512",
        "editorOverviewRuler.modifiedForeground": "#eed49f",
        "editorRuler.foreground": "#5b6078",
        "editorStickyScrollHover.background": "#363a4f",
        "editorSuggestWidget.background": "#1e2030",
        "editorSuggestWidget.border": "#5b6078",
        "editorSuggestWidget.foreground": "#cad3f5",
        "editorSuggestWidget.highlightForeground": "#c6a0f6",
        "editorSuggestWidget.selectedBackground": "#363a4f",
        "editorWarning.background": "#00000000",
        "editorWarning.border": "#00000000",
        "editorWarning.foreground": "#f5a97f",
        "editorWhitespace.foreground": "#939ab766",
        "editorWidget.background": "#1e2030",
        "editorWidget.foreground": "#cad3f5",
        "editorWidget.resizeBorder": "#5b6078",
        "errorForeground": "#ed8796",
        "errorLens.errorBackground": "#ed879626",
        "errorLens.errorBackgroundLight": "#ed879626",
        "errorLens.errorForeground": "#ed8796",
        "errorLens.errorForegroundLight": "#ed8796",
        "errorLens.errorMessageBackground": "#ed879626",
        "errorLens.hintBackground": "#a6da9526",
        "errorLens.hintBackgroundLight": "#a6da9526",
        "errorLens.hintForeground": "#a6da95",
        "errorLens.hintForegroundLight": "#a6da95",
        "errorLens.hintMessageBackground": "#a6da9526",
        "errorLens.infoBackground": "#8aadf426",
        "errorLens.infoBackgroundLight": "#8aadf426",
        "errorLens.infoForeground": "#8aadf4",
        "errorLens.infoForegroundLight": "#8aadf4",
        "errorLens.infoMessageBackground": "#8aadf426",
        "errorLens.statusBarErrorForeground": "#ed8796",
        "errorLens.statusBarHintForeground": "#a6da95",
        "errorLens.statusBarIconErrorForeground": "#ed8796",
        "errorLens.statusBarIconWarningForeground": "#f5a97f",
        "errorLens.statusBarInfoForeground": "#8aadf4",
        "errorLens.statusBarWarningForeground": "#f5a97f",
        "errorLens.warningBackground": "#f5a97f26",
        "errorLens.warningBackgroundLight": "#f5a97f26",
        "errorLens.warningForeground": "#f5a97f",
        "errorLens.warningForegroundLight": "#f5a97f",
        "errorLens.warningMessageBackground": "#f5a97f26",
        "extensionBadge.remoteBackground": "#8aadf4",
        "extensionBadge.remoteForeground": "#181926",
        "extensionButton.prominentBackground": "#c6a0f6",
        "extensionButton.prominentForeground": "#181926",
        "extensionButton.prominentHoverBackground": "#dac1f9",
        "extensionButton.separator": "#24273a",
        "extensionIcon.preReleaseForeground": "#5b6078",
        "extensionIcon.sponsorForeground": "#f5bde6",
        "extensionIcon.starForeground": "#eed49f",
        "extensionIcon.verifiedForeground": "#a6da95",
        "focusBorder": "#c6a0f6",
        "foreground": "#cad3f5",
        "gitDecoration.addedResourceForeground": "#a6da95",
        "gitDecoration.conflictingResourceForeground": "#c6a0f6",
        "gitDecoration.deletedResourceForeground": "#ed8796",
        "gitDecoration.ignoredResourceForeground": "#6e738d",
        "gitDecoration.modifiedResourceForeground": "#eed49f",
        "gitDecoration.stageDeletedResourceForeground": "#ed8796",
        "gitDecoration.stageModifiedResourceForeground": "#eed49f",
        "gitDecoration.submoduleResourceForeground": "#8aadf4",
        "gitDecoration.untrackedResourceForeground": "#a6da95",
        "gitlens.closedAutolinkedIssueIconColor": "#c6a0f6",
        "gitlens.closedPullRequestIconColor": "#ed8796",
        "gitlens.decorations.branchAheadForegroundColor": "#a6da95",
        "gitlens.decorations.branchBehindForegroundColor": "#f5a97f",
        "gitlens.decorations.branchDivergedForegroundColor": "#eed49f",
        "gitlens.decorations.branchMissingUpstreamForegroundColor": "#f5a97f",
        "gitlens.decorations.branchUnpublishedForegroundColor": "#a6da95",
        "gitlens.decorations.statusMergingOrRebasingConflictForegroundColor": "#ee99a0",
        "gitlens.decorations.statusMergingOrRebasingForegroundColor": "#eed49f",
        "gitlens.decorations.workspaceCurrentForegroundColor": "#c6a0f6",
        "gitlens.decorations.workspaceRepoMissingForegroundColor": "#a5adcb",
        "gitlens.decorations.workspaceRepoOpenForegroundColor": "#c6a0f6",
        "gitlens.decorations.worktreeHasUncommittedChangesForegroundColor": "#f5a97f",
        "gitlens.decorations.worktreeMissingForegroundColor": "#ee99a0",
        "gitlens.graphChangesColumnAddedColor": "#a6da95",
        "gitlens.graphChangesColumnDeletedColor": "#ed8796",
        "gitlens.graphLane10Color": "#f5bde6",
        "gitlens.graphLane1Color": "#c6a0f6",
        "gitlens.graphLane2Color": "#eed49f",
        "gitlens.graphLane3Color": "#8aadf4",
        "gitlens.graphLane4Color": "#f0c6c6",
        "gitlens.graphLane5Color": "#a6da95",
        "gitlens.graphLane6Color": "#b7bdf8",
        "gitlens.graphLane7Color": "#f4dbd6",
        "gitlens.graphLane8Color": "#ed8796",
        "gitlens.graphLane9Color": "#8bd5ca",
        "gitlens.graphMinimapMarkerHeadColor": "#a6da95",
        "gitlens.graphMinimapMarkerHighlightsColor": "#eed49f",
        "gitlens.graphMinimapMarkerLocalBranchesColor": "#8aadf4",
        "gitlens.graphMinimapMarkerRemoteBranchesColor": "#739df2",
        "gitlens.graphMinimapMarkerStashesColor": "#c6a0f6",
        "gitlens.graphMinimapMarkerTagsColor": "#f0c6c6",
        "gitlens.graphMinimapMarkerUpstreamColor": "#96d382",
        "gitlens.graphScrollMarkerHeadColor": "#a6da95",
        "gitlens.graphScrollMarkerHighlightsColor": "#eed49f",
        "gitlens.graphScrollMarkerLocalBranchesColor": "#8aadf4",
        "gitlens.graphScrollMarkerRemoteBranchesColor": "#739df2",
        "gitlens.graphScrollMarkerStashesColor": "#c6a0f6",
        "gitlens.graphScrollMarkerTagsColor": "#f0c6c6",
        "gitlens.graphScrollMarkerUpstreamColor": "#96d382",
        "gitlens.gutterBackgroundColor": "#363a4f4d",
        "gitlens.gutterForegroundColor": "#cad3f5",
        "gitlens.gutterUncommittedForegroundColor": "#c6a0f6",
        "gitlens.lineHighlightBackgroundColor": "#c6a0f626",
        "gitlens.lineHighlightOverviewRulerColor": "#c6a0f6cc",
        "gitlens.mergedPullRequestIconColor": "#c6a0f6",
        "gitlens.openAutolinkedIssueIconColor": "#a6da95",
        "gitlens.openPullRequestIconColor": "#a6da95",
        "gitlens.trailingLineBackgroundColor": "#00000000",
        "gitlens.trailingLineForegroundColor": "#cad3f54d",
        "gitlens.unpublishedChangesIconColor": "#a6da95",
        "gitlens.unpublishedCommitIconColor": "#a6da95",
        "gitlens.unpulledChangesIconColor": "#f5a97f",
        "icon.foreground": "#c6a0f6",
        "input.background": "#363a4f",
        "input.border": "#00000000",
        "input.foreground": "#cad3f5",
        "input.placeholderForeground": "#cad3f573",
        "inputOption.activeBackground": "#5b6078",
        "inputOption.activeBorder": "#c6a0f6",
        "inputOption.activeForeground": "#cad3f5",
        "inputValidation.errorBackground": "#ed8796",
        "inputValidation.errorBorder": "#18192633",
        "inputValidation.errorForeground": "#181926",
        "inputValidation.infoBackground": "#8aadf4",
        "inputValidation.infoBorder": "#18192633",
        "inputValidation.infoForeground": "#181926",
        "inputValidation.warningBackground": "#f5a97f",
        "inputValidation.warningBorder": "#18192633",
        "inputValidation.warningForeground": "#181926",
        "issues.closed": "#c6a0f6",
        "issues.newIssueDecoration": "#f4dbd6",
        "issues.open": "#a6da95",
        "list.activeSelectionBackground": "#363a4f",
        "list.activeSelectionForeground": "#cad3f5",
        "list.dropBackground": "#c6a0f633",
        "list.focusAndSelectionBackground": "#494d64",
        "list.focusBackground": "#363a4f",
        "list.focusForeground": "#cad3f5",
        "list.focusOutline": "#00000000",
        "list.highlightForeground": "#c6a0f6",
        "list.hoverBackground": "#363a4f80",
        "list.hoverForeground": "#cad3f5",
        "list.inactiveSelectionBackground": "#363a4f",
        "list.inactiveSelectionForeground": "#cad3f5",
        "list.warningForeground": "#f5a97f",
        "listFilterWidget.background": "#494d64",
        "listFilterWidget.noMatchesOutline": "#ed8796",
        "listFilterWidget.outline": "#00000000",
        "menu.background": "#24273a",
        "menu.border": "#24273a80",
        "menu.foreground": "#cad3f5",
        "menu.selectionBackground": "#5b6078",
        "menu.selectionBorder": "#00000000",
        "menu.selectionForeground": "#cad3f5",
        "menu.separatorBackground": "#5b6078",
        "menubar.selectionBackground": "#494d64",
        "menubar.selectionForeground": "#cad3f5",
        "merge.commonContentBackground": "#494d64",
        "merge.commonHeaderBackground": "#5b6078",
        "merge.currentContentBackground": "#a6da9533",
        "merge.currentHeaderBackground": "#a6da9566",
        "merge.incomingContentBackground": "#8aadf433",
        "merge.incomingHeaderBackground": "#8aadf466",
        "minimap.background": "#1e203080",
        "minimap.errorHighlight": "#ed8796bf",
        "minimap.findMatchHighlight": "#91d7e34d",
        "minimap.selectionHighlight": "#5b6078bf",
        "minimap.selectionOccurrenceHighlight": "#5b6078bf",
        "minimap.warningHighlight": "#f5a97fbf",
        "minimapGutter.addedBackground": "#a6da95bf",
        "minimapGutter.deletedBackground": "#ed8796bf",
        "minimapGutter.modifiedBackground": "#eed49fbf",
        "minimapSlider.activeBackground": "#c6a0f699",
        "minimapSlider.background": "#c6a0f633",
        "minimapSlider.hoverBackground": "#c6a0f666",
        "notificationCenter.border": "#c6a0f6",
        "notificationCenterHeader.background": "#1e2030",
        "notificationCenterHeader.foreground": "#cad3f5",
        "notificationLink.foreground": "#8aadf4",
        "notificationToast.border": "#c6a0f6",
        "notifications.background": "#1e2030",
        "notifications.border": "#c6a0f6",
        "notifications.foreground": "#cad3f5",
        "notificationsErrorIcon.foreground": "#ed8796",
        "notificationsInfoIcon.foreground": "#8aadf4",
        "notificationsWarningIcon.foreground": "#f5a97f",
        "panel.background": "#24273a",
        "panel.border": "#5b6078",
        "panelSection.border": "#5b6078",
        "panelSection.dropBackground": "#c6a0f633",
        "panelTitle.activeBorder": "#c6a0f6",
        "panelTitle.activeForeground": "#cad3f5",
        "panelTitle.inactiveForeground": "#a5adcb",
        "peekView.border": "#c6a0f6",
        "peekViewEditor.background": "#1e2030",
        "peekViewEditor.matchHighlightBackground": "#91d7e34d",
        "peekViewEditor.matchHighlightBorder": "#00000000",
        "peekViewEditorGutter.background": "#1e2030",
        "peekViewResult.background": "#1e2030",
        "peekViewResult.fileForeground": "#cad3f5",
        "peekViewResult.lineForeground": "#cad3f5",
        "peekViewResult.matchHighlightBackground": "#91d7e34d",
        "peekViewResult.selectionBackground": "#363a4f",
        "peekViewResult.selectionForeground": "#cad3f5",
        "peekViewTitle.background": "#24273a",
        "peekViewTitleDescription.foreground": "#b8c0e0b3",
        "peekViewTitleLabel.foreground": "#cad3f5",
        "pickerGroup.border": "#c6a0f6",
        "pickerGroup.foreground": "#c6a0f6",
        "problemsErrorIcon.foreground": "#ed8796",
        "problemsInfoIcon.foreground": "#8aadf4",
        "problemsWarningIcon.foreground": "#f5a97f",
        "progressBar.background": "#c6a0f6",
        "pullRequests.closed": "#ed8796",
        "pullRequests.draft": "#939ab7",
        "pullRequests.merged": "#c6a0f6",
        "pullRequests.notification": "#cad3f5",
        "pullRequests.open": "#a6da95",
        "sash.hoverBorder": "#c6a0f6",
        "scrollbar.shadow": "#181926",
        "scrollbarSlider.activeBackground": "#363a4f66",
        "scrollbarSlider.background": "#5b607880",
        "scrollbarSlider.hoverBackground": "#6e738d",
        "selection.background": "#c6a0f666",
        "settings.dropdownBackground": "#494d64",
        "settings.dropdownListBorder": "#00000000",
        "settings.focusedRowBackground": "#5b607833",
        "settings.headerForeground": "#cad3f5",
        "settings.modifiedItemIndicator": "#c6a0f6",
        "settings.numberInputBackground": "#494d64",
        "settings.numberInputBorder": "#00000000",
        "settings.textInputBackground": "#494d64",
        "settings.textInputBorder": "#00000000",
        "sideBar.background": "#1e2030",
        "sideBar.border": "#00000000",
        "sideBar.dropBackground": "#c6a0f633",
        "sideBar.foreground": "#cad3f5",
        "sideBarSectionHeader.background": "#1e2030",
        "sideBarSectionHeader.foreground": "#cad3f5",
        "sideBarTitle.foreground": "#c6a0f6",
        "statusBar.background": "#181926",
        "statusBar.border": "#00000000",
        "statusBar.debuggingBackground": "#f5a97f",
        "statusBar.debuggingBorder": "#00000000",
        "statusBar.debuggingForeground": "#181926",
        "statusBar.foreground": "#cad3f5",
        "statusBar.noFolderBackground": "#181926",
        "statusBar.noFolderBorder": "#00000000",
        "statusBar.noFolderForeground": "#cad3f5",
        "statusBarItem.activeBackground": "#5b607866",
        "statusBarItem.errorBackground": "#00000000",
        "statusBarItem.errorForeground": "#ed8796",
        "statusBarItem.hoverBackground": "#5b607833",
        "statusBarItem.prominentBackground": "#00000000",
        "statusBarItem.prominentForeground": "#c6a0f6",
        "statusBarItem.prominentHoverBackground": "#5b607833",
        "statusBarItem.remoteBackground": "#8aadf4",
        "statusBarItem.remoteForeground": "#181926",
        "statusBarItem.warningBackground": "#00000000",
        "statusBarItem.warningForeground": "#f5a97f",
        "symbolIcon.arrayForeground": "#f5a97f",
        "symbolIcon.booleanForeground": "#c6a0f6",
        "symbolIcon.classForeground": "#eed49f",
        "symbolIcon.colorForeground": "#f5bde6",
        "symbolIcon.constantForeground": "#f5a97f",
        "symbolIcon.constructorForeground": "#b7bdf8",
        "symbolIcon.enumeratorForeground": "#eed49f",
        "symbolIcon.enumeratorMemberForeground": "#eed49f",
        "symbolIcon.eventForeground": "#f5bde6",
        "symbolIcon.fieldForeground": "#cad3f5",
        "symbolIcon.fileForeground": "#c6a0f6",
        "symbolIcon.folderForeground": "#c6a0f6",
        "symbolIcon.functionForeground": "#8aadf4",
        "symbolIcon.interfaceForeground": "#eed49f",
        "symbolIcon.keyForeground": "#8bd5ca",
        "symbolIcon.keywordForeground": "#c6a0f6",
        "symbolIcon.methodForeground": "#8aadf4",
        "symbolIcon.moduleForeground": "#cad3f5",
        "symbolIcon.namespaceForeground": "#eed49f",
        "symbolIcon.nullForeground": "#ee99a0",
        "symbolIcon.numberForeground": "#f5a97f",
        "symbolIcon.objectForeground": "#eed49f",
        "symbolIcon.operatorForeground": "#8bd5ca",
        "symbolIcon.packageForeground": "#f0c6c6",
        "symbolIcon.propertyForeground": "#ee99a0",
        "symbolIcon.referenceForeground": "#eed49f",
        "symbolIcon.snippetForeground": "#f0c6c6",
        "symbolIcon.stringForeground": "#a6da95",
        "symbolIcon.structForeground": "#8bd5ca",
        "symbolIcon.textForeground": "#cad3f5",
        "symbolIcon.typeParameterForeground": "#ee99a0",
        "symbolIcon.unitForeground": "#cad3f5",
        "symbolIcon.variableForeground": "#cad3f5",
        "tab.activeBackground": "#24273a",
        "tab.activeBorder": "#00000000",
        "tab.activeBorderTop": "#c6a0f6",
        "tab.activeForeground": "#c6a0f6",
        "tab.activeModifiedBorder": "#eed49f",
        "tab.border": "#1e2030",
        "tab.hoverBackground": "#2e324a",
        "tab.hoverBorder": "#00000000",
        "tab.hoverForeground": "#c6a0f6",
        "tab.inactiveBackground": "#1e2030",
        "tab.inactiveForeground": "#6e738d",
        "tab.inactiveModifiedBorder": "#eed49f4d",
        "tab.lastPinnedBorder": "#c6a0f6",
        "tab.unfocusedActiveBackground": "#1e2030",
        "tab.unfocusedActiveBorder": "#00000000",
        "tab.unfocusedActiveBorderTop": "#c6a0f64d",
        "tab.unfocusedInactiveBackground": "#141620",
        "table.headerBackground": "#363a4f",
        "table.headerForeground": "#cad3f5",
        "terminal.ansiBlack": "#a5adcb",
        "terminal.ansiBlue": "#8aadf4",
        "terminal.ansiBrightBlack": "#5b6078",
        "terminal.ansiBrightBlue": "#8aadf4",
        "terminal.ansiBrightCyan": "#91d7e3",
        "terminal.ansiBrightGreen": "#a6da95",
        "terminal.ansiBrightMagenta": "#f5bde6",
        "terminal.ansiBrightRed": "#ed8796",
        "terminal.ansiBrightWhite": "#494d64",
        "terminal.ansiBrightYellow": "#eed49f",
        "terminal.ansiCyan": "#91d7e3",
        "terminal.ansiGreen": "#a6da95",
        "terminal.ansiMagenta": "#f5bde6",
        "terminal.ansiRed": "#ed8796",
        "terminal.ansiWhite": "#b8c0e0",
        "terminal.ansiYellow": "#eed49f",
        "terminal.border": "#5b6078",
        "terminal.dropBackground": "#c6a0f633",
        "terminal.foreground": "#cad3f5",
        "terminal.inactiveSelectionBackground": "#5b607880",
        "terminal.selectionBackground": "#5b6078",
        "terminal.tab.activeBorder": "#c6a0f6",
        "terminalCommandDecoration.defaultBackground": "#5b6078",
        "terminalCommandDecoration.errorBackground": "#ed8796",
        "terminalCommandDecoration.successBackground": "#a6da95",
        "terminalCursor.background": "#24273a",
        "terminalCursor.foreground": "#f4dbd6",
        "textBlockQuote.background": "#1e2030",
        "textBlockQuote.border": "#181926",
        "textCodeBlock.background": "#24273a",
        "textLink.activeForeground": "#91d7e3",
        "textLink.foreground": "#8aadf4",
        "textPreformat.foreground": "#cad3f5",
        "textSeparator.foreground": "#c6a0f6",
        "titleBar.activeBackground": "#181926",
        "titleBar.activeForeground": "#cad3f5",
        "titleBar.border": "#00000000",
        "titleBar.inactiveBackground": "#181926",
        "titleBar.inactiveForeground": "#cad3f580",
        "tree.inactiveIndentGuidesStroke": "#494d64",
        "tree.indentGuidesStroke": "#939ab7",
        "walkThrough.embeddedEditorBackground": "#24273a4d",
        "welcomePage.progress.background": "#181926",
        "welcomePage.progress.foreground": "#c6a0f6",
        "welcomePage.tileBackground": "#1e2030",
        "widget.shadow": "#1e203080",
        "window.activeBorder": "#00000000",
        "window.inactiveBorder": "#00000000"
    },
    "displayName": "Catppuccin Macchiato",
    "name": "catppuccin-macchiato",
    "semanticHighlighting": true,
    "semanticTokenColors": {
        "boolean": {
            "foreground": "#f5a97f"
        },
        "builtinAttribute.attribute.library:rust": {
            "foreground": "#8aadf4"
        },
        "class.builtin:python": {
            "foreground": "#c6a0f6"
        },
        "class:python": {
            "foreground": "#eed49f"
        },
        "constant.builtin.readonly:nix": {
            "foreground": "#c6a0f6"
        },
        "enumMember": {
            "foreground": "#8bd5ca"
        },
        "function.decorator:python": {
            "foreground": "#f5a97f"
        },
        "generic.attribute:rust": {
            "foreground": "#cad3f5"
        },
        "heading": {
            "foreground": "#ed8796"
        },
        "number": {
            "foreground": "#f5a97f"
        },
        "pol": {
            "foreground": "#f0c6c6"
        },
        "property.readonly:javascript": {
            "foreground": "#cad3f5"
        },
        "property.readonly:javascriptreact": {
            "foreground": "#cad3f5"
        },
        "property.readonly:typescript": {
            "foreground": "#cad3f5"
        },
        "property.readonly:typescriptreact": {
            "foreground": "#cad3f5"
        },
        "selfKeyword": {
            "foreground": "#ed8796"
        },
        "text.emph": {
            "fontStyle": "italic",
            "foreground": "#ed8796"
        },
        "text.math": {
            "foreground": "#f0c6c6"
        },
        "text.strong": {
            "fontStyle": "bold",
            "foreground": "#ed8796"
        },
        "tomlArrayKey": {
            "fontStyle": "",
            "foreground": "#8aadf4"
        },
        "tomlTableKey": {
            "fontStyle": "",
            "foreground": "#8aadf4"
        },
        "type.defaultLibrary:go": {
            "foreground": "#c6a0f6"
        },
        "variable.defaultLibrary": {
            "foreground": "#ee99a0"
        },
        "variable.readonly.defaultLibrary:go": {
            "foreground": "#c6a0f6"
        },
        "variable.readonly:javascript": {
            "foreground": "#cad3f5"
        },
        "variable.readonly:javascriptreact": {
            "foreground": "#cad3f5"
        },
        "variable.readonly:scala": {
            "foreground": "#cad3f5"
        },
        "variable.readonly:typescript": {
            "foreground": "#cad3f5"
        },
        "variable.readonly:typescriptreact": {
            "foreground": "#cad3f5"
        },
        "variable.typeHint:python": {
            "foreground": "#eed49f"
        }
    },
    "tokenColors": [
        {
            "scope": [
                "text",
                "source",
                "variable.other.readwrite",
                "punctuation.definition.variable"
            ],
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "punctuation",
            "settings": {
                "fontStyle": "",
                "foreground": "#939ab7"
            }
        },
        {
            "scope": [
                "comment",
                "punctuation.definition.comment"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#6e738d"
            }
        },
        {
            "scope": [
                "string",
                "punctuation.definition.string"
            ],
            "settings": {
                "foreground": "#a6da95"
            }
        },
        {
            "scope": "constant.character.escape",
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": [
                "constant.numeric",
                "variable.other.constant",
                "entity.name.constant",
                "constant.language.boolean",
                "constant.language.false",
                "constant.language.true",
                "keyword.other.unit.user-defined",
                "keyword.other.unit.suffix.floating-point"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "keyword",
                "keyword.operator.word",
                "keyword.operator.new",
                "variable.language.super",
                "support.type.primitive",
                "storage.type",
                "storage.modifier",
                "punctuation.definition.keyword"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "entity.name.tag.documentation",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "keyword.operator",
                "punctuation.accessor",
                "punctuation.definition.generic",
                "meta.function.closure punctuation.section.parameters",
                "punctuation.definition.tag",
                "punctuation.separator.key-value"
            ],
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": [
                "entity.name.function",
                "meta.function-call.method",
                "support.function",
                "support.function.misc",
                "variable.function"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "entity.name.class",
                "entity.other.inherited-class",
                "support.class",
                "meta.function-call.constructor",
                "entity.name.struct"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "entity.name.enum",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "meta.enum variable.other.readwrite",
                "variable.other.enummember"
            ],
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "meta.property.object",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": [
                "meta.type",
                "meta.type-alias",
                "support.type",
                "entity.name.type"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "meta.annotation variable.function",
                "meta.annotation variable.annotation.function",
                "meta.annotation punctuation.definition.annotation",
                "meta.decorator",
                "punctuation.decorator"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "variable.parameter",
                "meta.function.parameters"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": [
                "constant.language",
                "support.function.builtin"
            ],
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": "entity.other.attribute-name.documentation",
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": [
                "keyword.control.directive",
                "punctuation.definition.directive"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "punctuation.definition.typeparameters",
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": "entity.name.namespace",
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "support.type.property-name.css",
            "settings": {
                "fontStyle": "",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "variable.language.this",
                "variable.language.this punctuation.definition.variable"
            ],
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": "variable.object.property",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "string.template variable",
                "string variable"
            ],
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "keyword.operator.new",
            "settings": {
                "fontStyle": "bold"
            }
        },
        {
            "scope": "storage.modifier.specifier.extern.cpp",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "entity.name.scope-resolution.template.call.cpp",
                "entity.name.scope-resolution.parameter.cpp",
                "entity.name.scope-resolution.cpp",
                "entity.name.scope-resolution.function.definition.cpp"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "storage.type.class.doxygen",
            "settings": {
                "fontStyle": ""
            }
        },
        {
            "scope": [
                "storage.modifier.reference.cpp"
            ],
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "meta.interpolation.cs",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "comment.block.documentation.cs",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "source.css entity.other.attribute-name.class.css",
                "entity.other.attribute-name.parent-selector.css punctuation.definition.entity.css"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "punctuation.separator.operator.css",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "source.css entity.other.attribute-name.pseudo-class",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "source.css constant.other.unicode-range",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "source.css variable.parameter.url",
            "settings": {
                "fontStyle": "",
                "foreground": "#a6da95"
            }
        },
        {
            "scope": [
                "support.type.vendored.property-name"
            ],
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": [
                "source.css meta.property-value variable",
                "source.css meta.property-value variable.other.less",
                "source.css meta.property-value variable.other.less punctuation.definition.variable.less",
                "meta.definition.variable.scss"
            ],
            "settings": {
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": [
                "source.css meta.property-list variable",
                "meta.property-list variable.other.less",
                "meta.property-list variable.other.less punctuation.definition.variable.less"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": "keyword.other.unit.percentage.css",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "source.css meta.attribute-selector",
            "settings": {
                "foreground": "#a6da95"
            }
        },
        {
            "scope": [
                "keyword.other.definition.ini",
                "punctuation.support.type.property-name.json",
                "support.type.property-name.json",
                "punctuation.support.type.property-name.toml",
                "support.type.property-name.toml",
                "entity.name.tag.yaml",
                "punctuation.support.type.property-name.yaml",
                "support.type.property-name.yaml"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "constant.language.json",
                "constant.language.yaml"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "entity.name.type.anchor.yaml",
                "variable.other.alias.yaml"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "support.type.property-name.table",
                "entity.name.section.group-title.ini"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "constant.other.time.datetime.offset.toml",
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": [
                "punctuation.definition.anchor.yaml",
                "punctuation.definition.alias.yaml"
            ],
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "entity.other.document.begin.yaml",
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "markup.changed.diff",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "meta.diff.header.from-file",
                "meta.diff.header.to-file",
                "punctuation.definition.from-file.diff",
                "punctuation.definition.to-file.diff"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": "markup.inserted.diff",
            "settings": {
                "foreground": "#a6da95"
            }
        },
        {
            "scope": "markup.deleted.diff",
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": [
                "variable.other.env"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "string.quoted variable.other.env"
            ],
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "support.function.builtin.gdscript",
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": "constant.language.gdscript",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "comment meta.annotation.go",
            "settings": {
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": "comment meta.annotation.parameters.go",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "constant.language.go",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "variable.graphql",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "string.unquoted.alias.graphql",
            "settings": {
                "foreground": "#f0c6c6"
            }
        },
        {
            "scope": "constant.character.enum.graphql",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "meta.objectvalues.graphql constant.object.key.graphql string.unquoted.graphql",
            "settings": {
                "foreground": "#f0c6c6"
            }
        },
        {
            "scope": [
                "keyword.other.doctype",
                "meta.tag.sgml.doctype punctuation.definition.tag",
                "meta.tag.metadata.doctype entity.name.tag",
                "meta.tag.metadata.doctype punctuation.definition.tag"
            ],
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "entity.name.tag"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "text.html constant.character.entity",
                "text.html constant.character.entity punctuation",
                "constant.character.entity.xml",
                "constant.character.entity.xml punctuation",
                "constant.character.entity.js.jsx",
                "constant.charactger.entity.js.jsx punctuation",
                "constant.character.entity.tsx",
                "constant.character.entity.tsx punctuation"
            ],
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": [
                "entity.other.attribute-name"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "support.class.component",
                "support.class.component.jsx",
                "support.class.component.tsx",
                "support.class.component.vue"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": [
                "punctuation.definition.annotation",
                "storage.type.annotation"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "constant.other.enum.java",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "storage.modifier.import.java",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "comment.block.javadoc.java keyword.other.documentation.javadoc.java",
            "settings": {
                "fontStyle": ""
            }
        },
        {
            "scope": "meta.export variable.other.readwrite.js",
            "settings": {
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": [
                "variable.other.constant.js",
                "variable.other.constant.ts",
                "variable.other.property.js",
                "variable.other.property.ts"
            ],
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "variable.other.jsdoc",
                "comment.block.documentation variable.other"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": "storage.type.class.jsdoc",
            "settings": {
                "fontStyle": ""
            }
        },
        {
            "scope": "support.type.object.console.js",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "support.constant.node",
                "support.type.object.module.js"
            ],
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "storage.modifier.implements",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "constant.language.null.js",
                "constant.language.null.ts",
                "constant.language.undefined.js",
                "constant.language.undefined.ts",
                "support.type.builtin.ts"
            ],
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "variable.parameter.generic",
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "keyword.declaration.function.arrow.js",
                "storage.type.function.arrow.ts"
            ],
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "punctuation.decorator.ts",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "keyword.operator.expression.in.js",
                "keyword.operator.expression.in.ts",
                "keyword.operator.expression.infer.ts",
                "keyword.operator.expression.instanceof.js",
                "keyword.operator.expression.instanceof.ts",
                "keyword.operator.expression.is",
                "keyword.operator.expression.keyof.ts",
                "keyword.operator.expression.of.js",
                "keyword.operator.expression.of.ts",
                "keyword.operator.expression.typeof.ts"
            ],
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "support.function.macro.julia",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "constant.language.julia",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "constant.other.symbol.julia",
            "settings": {
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": "text.tex keyword.control.preamble",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "text.tex support.function.be",
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": "constant.other.general.math.tex",
            "settings": {
                "foreground": "#f0c6c6"
            }
        },
        {
            "scope": "comment.line.double-dash.documentation.lua storage.type.annotation.lua",
            "settings": {
                "fontStyle": "",
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "comment.line.double-dash.documentation.lua entity.name.variable.lua",
                "comment.line.double-dash.documentation.lua variable.lua"
            ],
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "heading.1.markdown punctuation.definition.heading.markdown",
                "heading.1.markdown",
                "heading.1.quarto punctuation.definition.heading.quarto",
                "heading.1.quarto",
                "markup.heading.atx.1.mdx",
                "markup.heading.atx.1.mdx punctuation.definition.heading.mdx",
                "markup.heading.setext.1.markdown",
                "markup.heading.heading-0.asciidoc"
            ],
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": [
                "heading.2.markdown punctuation.definition.heading.markdown",
                "heading.2.markdown",
                "heading.2.quarto punctuation.definition.heading.quarto",
                "heading.2.quarto",
                "markup.heading.atx.2.mdx",
                "markup.heading.atx.2.mdx punctuation.definition.heading.mdx",
                "markup.heading.setext.2.markdown",
                "markup.heading.heading-1.asciidoc"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "heading.3.markdown punctuation.definition.heading.markdown",
                "heading.3.markdown",
                "heading.3.quarto punctuation.definition.heading.quarto",
                "heading.3.quarto",
                "markup.heading.atx.3.mdx",
                "markup.heading.atx.3.mdx punctuation.definition.heading.mdx",
                "markup.heading.heading-2.asciidoc"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "heading.4.markdown punctuation.definition.heading.markdown",
                "heading.4.markdown",
                "heading.4.quarto punctuation.definition.heading.quarto",
                "heading.4.quarto",
                "markup.heading.atx.4.mdx",
                "markup.heading.atx.4.mdx punctuation.definition.heading.mdx",
                "markup.heading.heading-3.asciidoc"
            ],
            "settings": {
                "foreground": "#a6da95"
            }
        },
        {
            "scope": [
                "heading.5.markdown punctuation.definition.heading.markdown",
                "heading.5.markdown",
                "heading.5.quarto punctuation.definition.heading.quarto",
                "heading.5.quarto",
                "markup.heading.atx.5.mdx",
                "markup.heading.atx.5.mdx punctuation.definition.heading.mdx",
                "markup.heading.heading-4.asciidoc"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "heading.6.markdown punctuation.definition.heading.markdown",
                "heading.6.markdown",
                "heading.6.quarto punctuation.definition.heading.quarto",
                "heading.6.quarto",
                "markup.heading.atx.6.mdx",
                "markup.heading.atx.6.mdx punctuation.definition.heading.mdx",
                "markup.heading.heading-5.asciidoc"
            ],
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "markup.bold",
            "settings": {
                "fontStyle": "bold",
                "foreground": "#ed8796"
            }
        },
        {
            "scope": "markup.italic",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#ed8796"
            }
        },
        {
            "scope": "markup.strikethrough",
            "settings": {
                "fontStyle": "strikethrough",
                "foreground": "#a5adcb"
            }
        },
        {
            "scope": [
                "punctuation.definition.link",
                "markup.underline.link"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "text.html.markdown punctuation.definition.link.title",
                "text.html.quarto punctuation.definition.link.title",
                "string.other.link.title.markdown",
                "string.other.link.title.quarto",
                "markup.link",
                "punctuation.definition.constant.markdown",
                "punctuation.definition.constant.quarto",
                "constant.other.reference.link.markdown",
                "constant.other.reference.link.quarto",
                "markup.substitution.attribute-reference"
            ],
            "settings": {
                "foreground": "#b7bdf8"
            }
        },
        {
            "scope": [
                "punctuation.definition.raw.markdown",
                "punctuation.definition.raw.quarto",
                "markup.inline.raw.string.markdown",
                "markup.inline.raw.string.quarto",
                "markup.raw.block.markdown",
                "markup.raw.block.quarto"
            ],
            "settings": {
                "foreground": "#a6da95"
            }
        },
        {
            "scope": "fenced_code.block.language",
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": [
                "markup.fenced_code.block punctuation.definition",
                "markup.raw support.asciidoc"
            ],
            "settings": {
                "foreground": "#939ab7"
            }
        },
        {
            "scope": [
                "markup.quote",
                "punctuation.definition.quote.begin"
            ],
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "meta.separator.markdown",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": [
                "punctuation.definition.list.begin.markdown",
                "punctuation.definition.list.begin.quarto",
                "markup.list.bullet"
            ],
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "markup.heading.quarto",
            "settings": {
                "fontStyle": "bold"
            }
        },
        {
            "scope": [
                "entity.other.attribute-name.multipart.nix",
                "entity.other.attribute-name.single.nix"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": "variable.parameter.name.nix",
            "settings": {
                "fontStyle": "",
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "meta.embedded variable.parameter.name.nix",
            "settings": {
                "fontStyle": "",
                "foreground": "#b7bdf8"
            }
        },
        {
            "scope": "string.unquoted.path.nix",
            "settings": {
                "fontStyle": "",
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": [
                "support.attribute.builtin",
                "meta.attribute.php"
            ],
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "meta.function.parameters.php punctuation.definition.variable.php",
            "settings": {
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": "constant.language.php",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "text.html.php support.function",
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": "keyword.other.phpdoc.php",
            "settings": {
                "fontStyle": ""
            }
        },
        {
            "scope": [
                "support.variable.magic.python",
                "meta.function-call.arguments.python"
            ],
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "support.function.magic.python"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": [
                "variable.parameter.function.language.special.self.python",
                "variable.language.special.self.python"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#ed8796"
            }
        },
        {
            "scope": [
                "keyword.control.flow.python",
                "keyword.operator.logical.python"
            ],
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "storage.type.function.python",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "support.token.decorator.python",
                "meta.function.decorator.identifier.python"
            ],
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": [
                "meta.function-call.python"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "entity.name.function.decorator.python",
                "punctuation.definition.decorator.python"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "constant.character.format.placeholder.other.python",
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": [
                "support.type.exception.python",
                "support.function.builtin.python"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "support.type.python"
            ],
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "constant.language.python",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": [
                "meta.indexed-name.python",
                "meta.item-access.python"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": "storage.type.string.python",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#a6da95"
            }
        },
        {
            "scope": "meta.function.parameters.python",
            "settings": {
                "fontStyle": ""
            }
        },
        {
            "scope": [
                "string.regexp punctuation.definition.string.begin",
                "string.regexp punctuation.definition.string.end"
            ],
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "keyword.control.anchor.regexp",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "string.regexp.ts",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": [
                "punctuation.definition.group.regexp",
                "keyword.other.back-reference.regexp"
            ],
            "settings": {
                "foreground": "#a6da95"
            }
        },
        {
            "scope": "punctuation.definition.character-class.regexp",
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "constant.other.character-class.regexp",
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "constant.other.character-class.range.regexp",
            "settings": {
                "foreground": "#f4dbd6"
            }
        },
        {
            "scope": "keyword.operator.quantifier.regexp",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "constant.character.numeric.regexp",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "punctuation.definition.group.no-capture.regexp",
                "meta.assertion.look-ahead.regexp",
                "meta.assertion.negative-look-ahead.regexp"
            ],
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "meta.annotation.rust",
                "meta.annotation.rust punctuation",
                "meta.attribute.rust",
                "punctuation.definition.attribute.rust"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": [
                "meta.attribute.rust string.quoted.double.rust",
                "meta.attribute.rust string.quoted.single.char.rust"
            ],
            "settings": {
                "fontStyle": ""
            }
        },
        {
            "scope": [
                "entity.name.function.macro.rules.rust",
                "storage.type.module.rust",
                "storage.modifier.rust",
                "storage.type.struct.rust",
                "storage.type.enum.rust",
                "storage.type.trait.rust",
                "storage.type.union.rust",
                "storage.type.impl.rust",
                "storage.type.rust",
                "storage.type.function.rust",
                "storage.type.type.rust"
            ],
            "settings": {
                "fontStyle": "",
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "entity.name.type.numeric.rust",
            "settings": {
                "fontStyle": "",
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "meta.generic.rust",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "entity.name.impl.rust",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "entity.name.module.rust",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": "entity.name.trait.rust",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "storage.type.source.rust",
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "entity.name.union.rust",
            "settings": {
                "foreground": "#eed49f"
            }
        },
        {
            "scope": "meta.enum.rust storage.type.source.rust",
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": [
                "support.macro.rust",
                "meta.macro.rust support.function.rust",
                "entity.name.function.macro.rust"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": [
                "storage.modifier.lifetime.rust",
                "entity.name.type.lifetime"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": "string.quoted.double.rust constant.other.placeholder.rust",
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "meta.function.return-type.rust meta.generic.rust storage.type.rust",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "meta.function.call.rust",
            "settings": {
                "foreground": "#8aadf4"
            }
        },
        {
            "scope": "punctuation.brackets.angle.rust",
            "settings": {
                "foreground": "#91d7e3"
            }
        },
        {
            "scope": "constant.other.caps.rust",
            "settings": {
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "meta.function.definition.rust variable.other.rust"
            ],
            "settings": {
                "foreground": "#ee99a0"
            }
        },
        {
            "scope": "meta.function.call.rust variable.other.rust",
            "settings": {
                "foreground": "#cad3f5"
            }
        },
        {
            "scope": "variable.language.self.rust",
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": [
                "variable.other.metavariable.name.rust",
                "meta.macro.metavariable.rust keyword.operator.macro.dollar.rust"
            ],
            "settings": {
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": [
                "comment.line.shebang",
                "comment.line.shebang punctuation.definition.comment",
                "comment.line.shebang",
                "punctuation.definition.comment.shebang.shell",
                "meta.shebang.shell"
            ],
            "settings": {
                "fontStyle": "italic",
                "foreground": "#f5bde6"
            }
        },
        {
            "scope": "comment.line.shebang constant.language",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": [
                "meta.function-call.arguments.shell punctuation.definition.variable.shell",
                "meta.function-call.arguments.shell punctuation.section.interpolation",
                "meta.function-call.arguments.shell punctuation.definition.variable.shell",
                "meta.function-call.arguments.shell punctuation.section.interpolation"
            ],
            "settings": {
                "foreground": "#ed8796"
            }
        },
        {
            "scope": "meta.string meta.interpolation.parameter.shell variable.other.readwrite",
            "settings": {
                "fontStyle": "italic",
                "foreground": "#f5a97f"
            }
        },
        {
            "scope": [
                "source.shell punctuation.section.interpolation",
                "punctuation.definition.evaluation.backticks.shell"
            ],
            "settings": {
                "foreground": "#8bd5ca"
            }
        },
        {
            "scope": "entity.name.tag.heredoc.shell",
            "settings": {
                "foreground": "#c6a0f6"
            }
        },
        {
            "scope": "string.quoted.double.shell variable.other.normal.shell",
            "settings": {
                "foreground": "#cad3f5"
            }
        }
    ],
    "type": "dark"
});
;
}}),

};

//# sourceMappingURL=26e20_shiki_dist_themes_catppuccin-macchiato_mjs_f336951b._.js.map