hoistPattern:
  - '*'
hoistedDependencies:
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  split2@4.2.0:
    split2: private
  xtend@4.0.2:
    xtend: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Fri, 11 Jul 2025 15:11:52 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\node_modules\.pnpm
virtualStoreDirMaxLength: 60
