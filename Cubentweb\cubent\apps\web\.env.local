# Server - Updated to development Clerk keys
CLERK_SECRET_KEY="sk_test_Gy9j2NWYXEh5gvztI6EMqo2MZsgfHWYHLwfFTLLOim"
CLERK_WEBHOOK_SECRET="whsec_placeholder_development_only"
RESEND_FROM="<EMAIL>"
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
RESEND_TOKEN="re_5vhEiMML_7bVPRtnLkk3evtKBU28Kw2gZ"
STRIPE_SECRET_KEY="sk_test_51RacKARvLc5HHcqOWimagNKG7tHk1dOG8x4PR8DQu2GKmOS5ok2mIX5A7QqdkifpxPvmKp0lOYwIR0ud0BMA2jZa00IOtQj8Wg"
STRIPE_WEBHOOK_SECRET="whsec_stripe_placeholder_development_only"
BETTERSTACK_API_KEY="************************"
BETTERSTACK_URL=""
FLAGS_SECRET=""
ARCJET_KEY="ajkey_01jxwcpseafd39jw0h25fda53z"
# SVIX_TOKEN="sk_placeholder_development_only"
LIVEBLOCKS_SECRET="sk_test_EkRdbKV0kFsUOMMffwmvf8T2o-ow_o3K-sJzwBGsuT4"
BASEHUB_TOKEN="bshb_pk_zsskbo2563n1hgdhrl47yyttoh3pu7e8orawmx24z9q32rc66tkgw7ifani5l6vi"
VERCEL_PROJECT_PRODUCTION_URL="http://localhost:3001"
KNOCK_API_KEY=""
KNOCK_FEED_CHANNEL_ID=""
KNOCK_SECRET_API_KEY=""

# Client
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_YXdhcmUtZ3JvdXBlci03OS5jbGVyay5hY2NvdW50cy5kZXYk"
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/"
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-PLACEHOLDER123"
NEXT_PUBLIC_POSTHOG_KEY="phc_IIiOB59nWFyFh8azKXcqkOucMA9x5jTUYPTEDx2ccP9"
NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_WEB_URL="http://localhost:3001"
NEXT_PUBLIC_API_URL="http://localhost:3002"
NEXT_PUBLIC_DOCS_URL="https://docs.cubent.dev/"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51RacKARvLc5HHcqOqUhGjV4F4oG97zsJCyQ3Xa7fTHaDXU3IijB7EZtzcSLX2FfACxLc4ZWD9i74UsX6VLlVPyGt00rCPNwoOg"