{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/java.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Java\", \"name\": \"java\", \"patterns\": [{ \"begin\": \"\\\\b(package)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.package.java\" } }, \"contentName\": \"storage.modifier.package.java\", \"end\": \"\\\\s*(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.java\" } }, \"name\": \"meta.package.java\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"(?<=\\\\.)\\\\s*\\\\.|\\\\.(?=\\\\s*;)\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"(?<!_)_(?=\\\\s*(\\\\.|;))|\\\\b\\\\d+|-+\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"[A-Z]+\", \"name\": \"invalid.deprecated.package_name_not_lowercase.java\" }, { \"match\": \"\\\\b(?<!\\\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\\\b\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.java\" }] }, { \"begin\": \"\\\\b(import)\\\\b\\\\s*\\\\b(static)?\\\\b\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.import.java\" }, \"2\": { \"name\": \"storage.modifier.java\" } }, \"contentName\": \"storage.modifier.import.java\", \"end\": \"\\\\s*(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.java\" } }, \"name\": \"meta.import.java\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"(?<=\\\\.)\\\\s*\\\\.|\\\\.(?=\\\\s*;)\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"(?<!\\\\.)\\\\s*\\\\*\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"(?<!_)_(?=\\\\s*(\\\\.|;))|\\\\b\\\\d+|-+\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"\\\\b(?<!\\\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throw|throws|transient|try|void|volatile|while|yield|true|false|null)\\\\b\", \"name\": \"invalid.illegal.character_not_allowed_here.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.java\" }, { \"match\": \"\\\\*\", \"name\": \"variable.language.wildcard.java\" }] }, { \"include\": \"#comments-javadoc\" }, { \"include\": \"#code\" }, { \"include\": \"#module\" }], \"repository\": { \"all-types\": { \"patterns\": [{ \"include\": \"#primitive-arrays\" }, { \"include\": \"#primitive-types\" }, { \"include\": \"#object-types\" }] }, \"annotations\": { \"patterns\": [{ \"begin\": \"((@)\\\\s*([^\\\\s(]+))(\\\\()\", \"beginCaptures\": { \"2\": { \"name\": \"punctuation.definition.annotation.java\" }, \"3\": { \"name\": \"storage.type.annotation.java\" }, \"4\": { \"name\": \"punctuation.definition.annotation-arguments.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.annotation-arguments.end.bracket.round.java\" } }, \"name\": \"meta.declaration.annotation.java\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.other.key.java\" }, \"2\": { \"name\": \"keyword.operator.assignment.java\" } }, \"match\": \"(\\\\w*)\\\\s*(=)\" }, { \"include\": \"#code\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.annotation.java\" }, \"2\": { \"name\": \"storage.modifier.java\" }, \"3\": { \"name\": \"storage.type.annotation.java\" }, \"5\": { \"name\": \"punctuation.definition.annotation.java\" }, \"6\": { \"name\": \"storage.type.annotation.java\" } }, \"match\": \"(@)(interface)\\\\s+(\\\\w*)|((@)\\\\s*(\\\\w+))\", \"name\": \"meta.declaration.annotation.java\" }] }, \"anonymous-block-and-instance-initializer\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.block.begin.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.block.end.bracket.curly.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, \"anonymous-classes-and-new\": { \"begin\": \"\\\\bnew\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.new.java\" } }, \"end\": \"(?=;|\\\\)|\\\\]|\\\\.|,|\\\\?|:|}|\\\\+|-|\\\\*|\\\\/(?!\\\\/|\\\\*)|%|!|&|\\\\||\\\\^|=)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#function-call\" }, { \"include\": \"#all-types\" }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(?=;|\\\\)|\\\\]|\\\\.|,|\\\\?|:|}|\\\\+|-|\\\\*|\\\\/(?!\\\\/|\\\\*)|%|!|&|\\\\||\\\\^|=)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.inner-class.begin.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.inner-class.end.bracket.curly.java\" } }, \"name\": \"meta.inner-class.java\", \"patterns\": [{ \"include\": \"#class-body\" }] }] }, { \"begin\": \"(?<=\\\\])\", \"end\": \"(?=;|\\\\)|\\\\]|\\\\.|,|\\\\?|:|}|\\\\+|-|\\\\*|\\\\/(?!\\\\/|\\\\*)|%|!|&|\\\\||\\\\^|=)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.array-initializer.begin.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.array-initializer.end.bracket.curly.java\" } }, \"name\": \"meta.array-initializer.java\", \"patterns\": [{ \"include\": \"#code\" }] }] }, { \"include\": \"#parens\" }] }, \"assertions\": { \"patterns\": [{ \"begin\": \"\\\\b(assert)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.assert.java\" } }, \"end\": \"$\", \"name\": \"meta.declaration.assertion.java\", \"patterns\": [{ \"match\": \":\", \"name\": \"keyword.operator.assert.expression-separator.java\" }, { \"include\": \"#code\" }] }] }, \"class\": { \"begin\": \"(?=\\\\w?[\\\\w\\\\s-]*\\\\b(?:class|(?<!@)interface|enum)\\\\s+[\\\\w$]+)\", \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.class.end.bracket.curly.java\" } }, \"name\": \"meta.class.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"include\": \"#generics\" }, { \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.java\" }, \"2\": { \"name\": \"entity.name.type.class.java\" } }, \"match\": \"(class|(?<!@)interface|enum)\\\\s+([\\\\w$]+)\", \"name\": \"meta.class.identifier.java\" }, { \"begin\": \"extends\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.extends.java\" } }, \"end\": \"(?={|implements|permits)\", \"name\": \"meta.definition.class.inherited.classes.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"(implements)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.implements.java\" } }, \"end\": \"(?=\\\\s*extends|permits|\\\\{)\", \"name\": \"meta.definition.class.implemented.interfaces.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"(permits)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.permits.java\" } }, \"end\": \"(?=\\\\s*extends|implements|\\\\{)\", \"name\": \"meta.definition.class.permits.classes.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.class.begin.bracket.curly.java\" } }, \"contentName\": \"meta.class.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#class-body\" }] }] }, \"class-body\": { \"patterns\": [{ \"include\": \"#comments-javadoc\" }, { \"include\": \"#comments\" }, { \"include\": \"#enums\" }, { \"include\": \"#class\" }, { \"include\": \"#generics\" }, { \"include\": \"#static-initializer\" }, { \"include\": \"#class-fields-and-methods\" }, { \"include\": \"#annotations\" }, { \"include\": \"#storage-modifiers\" }, { \"include\": \"#member-variables\" }, { \"include\": \"#code\" }] }, \"class-fields-and-methods\": { \"patterns\": [{ \"begin\": \"(?==)\", \"end\": \"(?=;)\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"include\": \"#methods\" }] }, \"code\": { \"patterns\": [{ \"include\": \"#annotations\" }, { \"include\": \"#comments\" }, { \"include\": \"#enums\" }, { \"include\": \"#class\" }, { \"include\": \"#record\" }, { \"include\": \"#anonymous-block-and-instance-initializer\" }, { \"include\": \"#try-catch-finally\" }, { \"include\": \"#assertions\" }, { \"include\": \"#parens\" }, { \"include\": \"#constants-and-special-vars\" }, { \"include\": \"#numbers\" }, { \"include\": \"#anonymous-classes-and-new\" }, { \"include\": \"#lambda-expression\" }, { \"include\": \"#keywords\" }, { \"include\": \"#storage-modifiers\" }, { \"include\": \"#method-call\" }, { \"include\": \"#function-call\" }, { \"include\": \"#variables\" }, { \"include\": \"#variables-local\" }, { \"include\": \"#objects\" }, { \"include\": \"#properties\" }, { \"include\": \"#strings\" }, { \"include\": \"#all-types\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }, { \"match\": \";\", \"name\": \"punctuation.terminator.java\" }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.empty.java\" }, { \"include\": \"#comments-inline\" }] }, \"comments-inline\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.java\" }, { \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.java\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-slash.java\" }] }] }, \"comments-javadoc\": { \"patterns\": [{ \"begin\": \"^\\\\s*(/\\\\*\\\\*)(?!/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.java\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.java\" } }, \"name\": \"comment.block.javadoc.java\", \"patterns\": [{ \"match\": \"@(author|deprecated|return|see|serial|since|version)\\\\b\", \"name\": \"keyword.other.documentation.javadoc.java\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.javadoc.java\" }, \"2\": { \"name\": \"variable.parameter.java\" } }, \"match\": \"(@param)\\\\s+(\\\\S+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.javadoc.java\" }, \"2\": { \"name\": \"entity.name.type.class.java\" } }, \"match\": \"(@(?:exception|throws))\\\\s+(\\\\S+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.documentation.javadoc.java\" }, \"2\": { \"name\": \"entity.name.type.class.java\" }, \"3\": { \"name\": \"variable.parameter.java\" } }, \"match\": \"{(@link)\\\\s+(\\\\S+)?#([\\\\w$]+\\\\s*\\\\([^()]*\\\\)).*?}\" }] }] }, \"constants-and-special-vars\": { \"patterns\": [{ \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.java\" }, { \"match\": \"\\\\bthis\\\\b\", \"name\": \"variable.language.this.java\" }, { \"match\": \"\\\\bsuper\\\\b\", \"name\": \"variable.language.java\" }] }, \"enums\": { \"begin\": \"^\\\\s*([\\\\w\\\\s]*)(enum)\\\\s+(\\\\w+)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#storage-modifiers\" }] }, \"2\": { \"name\": \"storage.modifier.java\" }, \"3\": { \"name\": \"entity.name.type.enum.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.enum.end.bracket.curly.java\" } }, \"name\": \"meta.enum.java\", \"patterns\": [{ \"begin\": \"\\\\b(extends)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.extends.java\" } }, \"end\": \"(?={|\\\\bimplements\\\\b)\", \"name\": \"meta.definition.class.inherited.classes.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"\\\\b(implements)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.implements.java\" } }, \"end\": \"(?={|\\\\bextends\\\\b)\", \"name\": \"meta.definition.class.implemented.interfaces.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.enum.begin.bracket.curly.java\" } }, \"end\": \"(?=})\", \"patterns\": [{ \"begin\": \"(?<={)\", \"end\": \"(?=;|})\", \"patterns\": [{ \"include\": \"#comments-javadoc\" }, { \"include\": \"#comments\" }, { \"begin\": \"\\\\b(\\\\w+)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"constant.other.enum.java\" } }, \"end\": \"(,)|(?=;|})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.delimiter.java\" } }, \"patterns\": [{ \"include\": \"#comments-javadoc\" }, { \"include\": \"#comments\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"patterns\": [{ \"include\": \"#class-body\" }] }] }] }, { \"include\": \"#class-body\" }] }] }, \"function-call\": { \"begin\": \"([A-Za-z_$][\\\\w$]*)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.java\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.function-call.java\", \"patterns\": [{ \"include\": \"#code\" }] }, \"generics\": { \"begin\": \"<\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.angle.java\" } }, \"end\": \">\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.angle.java\" } }, \"patterns\": [{ \"match\": \"\\\\b(extends|super)\\\\b\", \"name\": \"storage.modifier.$1.java\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.java\" } }, \"match\": \"(?<!\\\\.)([a-zA-Z$_][a-zA-Z0-9$_]*)(?=\\\\s*<)\" }, { \"include\": \"#primitive-arrays\" }, { \"match\": \"[a-zA-Z$_][a-zA-Z0-9$_]*\", \"name\": \"storage.type.generic.java\" }, { \"match\": \"\\\\?\", \"name\": \"storage.type.generic.wildcard.java\" }, { \"match\": \"&\", \"name\": \"punctuation.separator.types.java\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }, { \"include\": \"#parens\" }, { \"include\": \"#generics\" }, { \"include\": \"#comments\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\bthrow\\\\b\", \"name\": \"keyword.control.throw.java\" }, { \"match\": \"\\\\?|:\", \"name\": \"keyword.control.ternary.java\" }, { \"match\": \"\\\\b(return|yield|break|case|continue|default|do|while|for|switch|if|else)\\\\b\", \"name\": \"keyword.control.java\" }, { \"match\": \"\\\\b(instanceof)\\\\b\", \"name\": \"keyword.operator.instanceof.java\" }, { \"match\": \"(<<|>>>?|~|\\\\^)\", \"name\": \"keyword.operator.bitwise.java\" }, { \"match\": \"((&|\\\\^|\\\\||<<|>>>?)=)\", \"name\": \"keyword.operator.assignment.bitwise.java\" }, { \"match\": \"(===?|!=|<=|>=|<>|<|>)\", \"name\": \"keyword.operator.comparison.java\" }, { \"match\": \"([+*/%-]=)\", \"name\": \"keyword.operator.assignment.arithmetic.java\" }, { \"match\": \"(=)\", \"name\": \"keyword.operator.assignment.java\" }, { \"match\": \"(--|\\\\+\\\\+)\", \"name\": \"keyword.operator.increment-decrement.java\" }, { \"match\": \"(-|\\\\+|\\\\*|\\\\/|%)\", \"name\": \"keyword.operator.arithmetic.java\" }, { \"match\": \"(!|&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.java\" }, { \"match\": \"(\\\\||&)\", \"name\": \"keyword.operator.bitwise.java\" }, { \"match\": \"\\\\b(const|goto)\\\\b\", \"name\": \"keyword.reserved.java\" }] }, \"lambda-expression\": { \"patterns\": [{ \"match\": \"->\", \"name\": \"storage.type.function.arrow.java\" }] }, \"member-variables\": { \"begin\": \"(?=private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)\", \"end\": \"(?==|;)\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"include\": \"#variables\" }, { \"include\": \"#primitive-arrays\" }, { \"include\": \"#object-types\" }] }, \"method-call\": { \"begin\": \"(\\\\.)\\\\s*([A-Za-z_$][\\\\w$]*)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"entity.name.function.java\" }, \"3\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.method-call.java\", \"patterns\": [{ \"include\": \"#code\" }] }, \"methods\": { \"begin\": \"(?!new)(?=[\\\\w<].*\\\\s+)(?=([^=/]|/(?!/))+\\\\()\", \"end\": \"(})|(?=;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.method.end.bracket.curly.java\" } }, \"name\": \"meta.method.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"begin\": \"(\\\\w+)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.java\" }, \"2\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.method.identifier.java\", \"patterns\": [{ \"include\": \"#parameters\" }, { \"include\": \"#parens\" }, { \"include\": \"#comments\" }] }, { \"include\": \"#generics\" }, { \"begin\": \"(?=\\\\w.*\\\\s+\\\\w+\\\\s*\\\\()\", \"end\": \"(?=\\\\s+\\\\w+\\\\s*\\\\()\", \"name\": \"meta.method.return-type.java\", \"patterns\": [{ \"include\": \"#all-types\" }, { \"include\": \"#parens\" }, { \"include\": \"#comments\" }] }, { \"include\": \"#throws\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.method.begin.bracket.curly.java\" } }, \"contentName\": \"meta.method.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"include\": \"#comments\" }] }, \"module\": { \"begin\": \"((open)\\\\s)?(module)\\\\s+(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.java\" }, \"3\": { \"name\": \"storage.modifier.java\" }, \"4\": { \"name\": \"entity.name.type.module.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.module.end.bracket.curly.java\" } }, \"name\": \"meta.module.java\", \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.module.begin.bracket.curly.java\" } }, \"contentName\": \"meta.module.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#comments-javadoc\" }, { \"match\": \"\\\\b(requires|transitive|exports|opens|to|uses|provides|with)\\\\b\", \"name\": \"keyword.module.java\" }] }] }, \"numbers\": { \"patterns\": [{ \"match\": \"\\\\b(?<!\\\\$)0(x|X)((?<!\\\\.)[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?[Ll]?(?!\\\\.)|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?\\\\.?|([0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)?\\\\.[0-9a-fA-F]([0-9a-fA-F_]*[0-9a-fA-F])?)[Pp][+-]?\\\\d([0-9_]*\\\\d)?[FfDd]?)\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.java\" }, { \"match\": \"\\\\b(?<!\\\\$)0(b|B)[01]([01_]*[01])?[Ll]?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.binary.java\" }, { \"match\": \"\\\\b(?<!\\\\$)0[0-7]([0-7_]*[0-7])?[Ll]?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.octal.java\" }, { \"match\": \"(?<!\\\\$)(\\\\b\\\\d([0-9_]*\\\\d)?\\\\.\\\\B(?!\\\\.)|\\\\b\\\\d([0-9_]*\\\\d)?\\\\.([Ee][+-]?\\\\d([0-9_]*\\\\d)?)[FfDd]?\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?\\\\.([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?\\\\.(\\\\d([0-9_]*\\\\d)?)([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]?\\\\b|(?<!\\\\.)\\\\B\\\\.\\\\d([0-9_]*\\\\d)?([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]?\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?([Ee][+-]?\\\\d([0-9_]*\\\\d)?)[FfDd]?\\\\b|\\\\b\\\\d([0-9_]*\\\\d)?([Ee][+-]?\\\\d([0-9_]*\\\\d)?)?[FfDd]\\\\b|\\\\b(0|[1-9]([0-9_]*\\\\d)?)(?!\\\\.)[Ll]?\\\\b)(?!\\\\$)\", \"name\": \"constant.numeric.decimal.java\" }] }, \"object-types\": { \"patterns\": [{ \"include\": \"#generics\" }, { \"begin\": \"\\\\b((?:[A-Za-z_]\\\\w*\\\\s*\\\\.\\\\s*)*)([A-Z_]\\\\w*)\\\\s*(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"[A-Za-z_]\\\\w*\", \"name\": \"storage.type.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }] }, \"2\": { \"name\": \"storage.type.object.array.java\" } }, \"end\": \"(?!\\\\s*\\\\[)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#parens\" }] }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"[A-Za-z_]\\\\w*\", \"name\": \"storage.type.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }] } }, \"match\": \"\\\\b((?:[A-Za-z_]\\\\w*\\\\s*\\\\.\\\\s*)*[A-Z_]\\\\w*)\\\\s*(?=<)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"[A-Za-z_]\\\\w*\", \"name\": \"storage.type.java\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.separator.period.java\" }] } }, \"match\": \"\\\\b((?:[A-Za-z_]\\\\w*\\\\s*\\\\.\\\\s*)*[A-Z_]\\\\w*)\\\\b((?=\\\\s*[A-Za-z$_\\\\n])|(?=\\\\s*\\\\.\\\\.\\\\.))\" }] }, \"object-types-inherited\": { \"patterns\": [{ \"include\": \"#generics\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" } }, \"match\": \"\\\\b(?:[A-Z]\\\\w*\\\\s*(\\\\.)\\\\s*)*[A-Z]\\\\w*\\\\b\", \"name\": \"entity.other.inherited-class.java\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }] }, \"objects\": { \"match\": \"(?<![\\\\w$])[a-zA-Z_$][\\\\w$]*(?=\\\\s*\\\\.\\\\s*[\\\\w$]+)\", \"name\": \"variable.other.object.java\" }, \"parameters\": { \"patterns\": [{ \"match\": \"\\\\bfinal\\\\b\", \"name\": \"storage.modifier.java\" }, { \"include\": \"#annotations\" }, { \"include\": \"#all-types\" }, { \"include\": \"#strings\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.java\" }, { \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"punctuation.definition.parameters.varargs.java\" }] }, \"parens\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.round.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.square.java\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.square.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.bracket.curly.java\" } }, \"patterns\": [{ \"include\": \"#code\" }] }] }, \"primitive-arrays\": { \"patterns\": [{ \"begin\": \"\\\\b(void|boolean|byte|char|short|int|float|long|double)\\\\b\\\\s*(?=\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.primitive.array.java\" } }, \"end\": \"(?!\\\\s*\\\\[)\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#parens\" }] }] }, \"primitive-types\": { \"match\": \"\\\\b(void|boolean|byte|char|short|int|float|long|double)\\\\b\", \"name\": \"storage.type.primitive.java\" }, \"properties\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"keyword.control.new.java\" } }, \"match\": \"(\\\\.)\\\\s*(new)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"variable.other.object.property.java\" } }, \"match\": \"(\\\\.)\\\\s*([a-zA-Z_$][\\\\w$]*)(?=\\\\s*\\\\.\\\\s*[a-zA-Z_$][\\\\w$]*)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"variable.other.object.property.java\" } }, \"match\": \"(\\\\.)\\\\s*([a-zA-Z_$][\\\\w$]*)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.period.java\" }, \"2\": { \"name\": \"invalid.illegal.identifier.java\" } }, \"match\": \"(\\\\.)\\\\s*(\\\\d[\\\\w$]*)\" }] }, \"record\": { \"begin\": \"(?=\\\\w?[\\\\w\\\\s]*\\\\b(?:record)\\\\s+[\\\\w$]+)\", \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.class.end.bracket.curly.java\" } }, \"name\": \"meta.record.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"include\": \"#generics\" }, { \"include\": \"#comments\" }, { \"begin\": \"(record)\\\\s+([\\\\w$]+)(<[\\\\w$]+>)?(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.java\" }, \"2\": { \"name\": \"entity.name.type.record.java\" }, \"3\": { \"patterns\": [{ \"include\": \"#generics\" }] }, \"4\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"name\": \"meta.record.identifier.java\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"(implements)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.implements.java\" } }, \"end\": \"(?=\\\\s*\\\\{)\", \"name\": \"meta.definition.class.implemented.interfaces.java\", \"patterns\": [{ \"include\": \"#object-types-inherited\" }, { \"include\": \"#comments\" }] }, { \"include\": \"#record-body\" }] }, \"record-body\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.class.begin.bracket.curly.java\" } }, \"end\": \"(?=})\", \"name\": \"meta.record.body.java\", \"patterns\": [{ \"include\": \"#record-constructor\" }, { \"include\": \"#class-body\" }] }, \"record-constructor\": { \"begin\": \"(?!new)(?=[\\\\w<].*\\\\s+)(?=([^(=/]|/(?!/))+(?={))\", \"end\": \"(})|(?=;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.method.end.bracket.curly.java\" } }, \"name\": \"meta.method.java\", \"patterns\": [{ \"include\": \"#storage-modifiers\" }, { \"begin\": \"(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.java\" } }, \"end\": \"(?=\\\\s*{)\", \"name\": \"meta.method.identifier.java\", \"patterns\": [{ \"include\": \"#comments\" }] }, { \"include\": \"#comments\" }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.method.begin.bracket.curly.java\" } }, \"contentName\": \"meta.method.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, \"static-initializer\": { \"patterns\": [{ \"include\": \"#anonymous-block-and-instance-initializer\" }, { \"match\": \"static\", \"name\": \"storage.modifier.java\" }] }, \"storage-modifiers\": { \"match\": \"\\\\b(public|private|protected|static|final|native|synchronized|abstract|threadsafe|transient|volatile|default|strictfp|sealed|non-sealed)\\\\b\", \"name\": \"storage.modifier.java\" }, \"strings\": { \"patterns\": [{ \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.java\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.java\" } }, \"name\": \"string.quoted.triple.java\", \"patterns\": [{ \"match\": '(\\\\\\\\\"\"\")(?!\")|(\\\\\\\\.)', \"name\": \"constant.character.escape.java\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.java\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.java\" } }, \"name\": \"string.quoted.double.java\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.java\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.java\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.java\" } }, \"name\": \"string.quoted.single.java\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.java\" }] }] }, \"throws\": { \"begin\": \"throws\", \"beginCaptures\": { \"0\": { \"name\": \"storage.modifier.java\" } }, \"end\": \"(?={|;)\", \"name\": \"meta.throwables.java\", \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.separator.delimiter.java\" }, { \"match\": \"[a-zA-Z$_][\\\\.a-zA-Z0-9$_]*\", \"name\": \"storage.type.java\" }, { \"include\": \"#comments\" }] }, \"try-catch-finally\": { \"patterns\": [{ \"begin\": \"\\\\btry\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.try.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.try.end.bracket.curly.java\" } }, \"name\": \"meta.try.java\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.try.resources.begin.bracket.round.java\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.try.resources.end.bracket.round.java\" } }, \"name\": \"meta.try.resources.java\", \"patterns\": [{ \"include\": \"#code\" }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.try.begin.bracket.curly.java\" } }, \"contentName\": \"meta.try.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, { \"begin\": \"\\\\b(catch)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.catch.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.catch.end.bracket.curly.java\" } }, \"name\": \"meta.catch.java\", \"patterns\": [{ \"include\": \"#comments\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.begin.bracket.round.java\" } }, \"contentName\": \"meta.catch.parameters.java\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.parameters.end.bracket.round.java\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#storage-modifiers\" }, { \"begin\": \"[a-zA-Z$_][\\\\.a-zA-Z0-9$_]*\", \"beginCaptures\": { \"0\": { \"name\": \"storage.type.java\" } }, \"end\": \"(\\\\|)|(?=\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.catch.separator.java\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"0\": { \"name\": \"variable.parameter.java\" } }, \"match\": \"\\\\w+\" }] }] }, { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.catch.begin.bracket.curly.java\" } }, \"contentName\": \"meta.catch.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }, { \"begin\": \"\\\\bfinally\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.finally.java\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.finally.end.bracket.curly.java\" } }, \"name\": \"meta.finally.java\", \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.finally.begin.bracket.curly.java\" } }, \"contentName\": \"meta.finally.body.java\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#code\" }] }] }] }, \"variables\": { \"begin\": \"(?=\\\\b((void|boolean|byte|char|short|int|float|long|double)|(?>(\\\\w+\\\\.)*[A-Z_]+\\\\w*))\\\\b\\\\s*(<[\\\\w<>,\\\\.?\\\\s\\\\[\\\\]]*>)?\\\\s*((\\\\[\\\\])*)?\\\\s+[A-Za-z_$][\\\\w$]*([\\\\w\\\\[\\\\],$][\\\\w\\\\[\\\\],\\\\s]*)?\\\\s*(=|:|;))\", \"end\": \"(?==|:|;)\", \"name\": \"meta.definition.variable.java\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.definition.java\" } }, \"match\": \"([A-Za-z$_][\\\\w$]*)(?=\\\\s*(\\\\[\\\\])*\\\\s*(;|:|=|,))\" }, { \"include\": \"#all-types\" }, { \"include\": \"#code\" }] }, \"variables-local\": { \"begin\": \"(?=\\\\b(var)\\\\b\\\\s+[A-Za-z_$][\\\\w$]*\\\\s*(=|:|;))\", \"end\": \"(?==|:|;)\", \"name\": \"meta.definition.variable.local.java\", \"patterns\": [{ \"match\": \"\\\\bvar\\\\b\", \"name\": \"storage.type.local.java\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.definition.java\" } }, \"match\": \"([A-Za-z$_][\\\\w$]*)(?=\\\\s*(\\\\[\\\\])*\\\\s*(=|:|;))\" }, { \"include\": \"#code\" }] } }, \"scopeName\": \"source.java\" });\nvar java = [\n  lang\n];\n\nexport { java as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,eAAe;YAAiC,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAAqB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAqD;gBAAG;oBAAE,SAAS;oBAAqZ,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAwC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,eAAe;YAAgC,OAAO;YAAW,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAqZ,QAAQ;gBAAkD;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAkC;aAAE;QAAC;QAAG;YAAE,WAAW;QAAoB;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAU;KAAE;IAAE,cAAc;QAAE,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA4B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAuE;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqE;oBAAE;oBAAG,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA0B;gCAAG,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,SAAS;wBAAgB;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,QAAQ;wBAAyC;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,SAAS;oBAA4C,QAAQ;gBAAmC;aAAE;QAAC;QAAG,4CAA4C;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,6BAA6B;YAAE,SAAS;YAAa,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,OAAO;YAAwE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAAwE,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAyD;4BAAE;4BAAG,QAAQ;4BAAyB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAY,OAAO;oBAAwE,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAiE;4BAAE;4BAAG,OAAO;4BAAK,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA+D;4BAAE;4BAAG,QAAQ;4BAA+B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAK,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,SAAS;4BAAK,QAAQ;wBAAoD;wBAAG;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAkE,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAAmB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;oBAA6C,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAW,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA4B,QAAQ;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAA+B,QAAQ;oBAAqD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAgB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAkC,QAAQ;oBAA8C,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA4B;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,4BAA4B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAS,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,QAAQ;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAS;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA4C;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAA6B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA8B;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAY,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAAqB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8C;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAM,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAO,QAAQ;wBAAiC;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,QAAQ;oBAA8B,YAAY;wBAAC;4BAAE,SAAS;4BAA2D,QAAQ;wBAA2C;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;4BAAE;4BAAG,SAAS;wBAAqB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAA8B;4BAAE;4BAAG,SAAS;wBAAoC;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA2C;gCAAG,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAA0B;4BAAE;4BAAG,SAAS;wBAAoD;qBAAE;gBAAC;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAyB;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAoC,iBAAiB;gBAAE,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAkD;YAAE;YAAG,QAAQ;YAAkB,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAA0B,QAAQ;oBAAgD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAsB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAuB,QAAQ;oBAAqD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoD;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,SAAS;4BAAU,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAoB;gCAAG;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,SAAS;oCAAgB,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAA2B;oCAAE;oCAAG,OAAO;oCAAe,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAuC;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,WAAW;wCAAoB;wCAAG;4CAAE,WAAW;wCAAY;wCAAG;4CAAE,SAAS;4CAAO,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,OAAO;4CAAO,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,WAAW;gDAAQ;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAK,iBAAiB;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,OAAO;4CAAK,eAAe;gDAAE,KAAK;oDAAE,QAAQ;gDAAiC;4CAAE;4CAAG,YAAY;gDAAC;oDAAE,WAAW;gDAAc;6CAAE;wCAAC;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAgC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,YAAY;gBAAC;oBAAE,SAAS;oBAAyB,QAAQ;gBAA2B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;oBAAE;oBAAG,SAAS;gBAA8C;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAqC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAoC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAgF,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAA8C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAsB,QAAQ;gBAAwB;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAM,QAAQ;gBAAmC;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAA+F,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAyC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAA6D;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA2D;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAiD,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA4B,OAAO;oBAAuB,QAAQ;oBAAgC,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,eAAe;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAkC,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,eAAe;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAoB;wBAAG;4BAAE,SAAS;4BAAmE,QAAQ;wBAAsB;qBAAE;gBAAC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2P,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAqD,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAmD,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAA4d,QAAQ;gBAAgC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA6D,iBAAiB;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiB,QAAQ;gCAAoB;gCAAG;oCAAE,SAAS;oCAAO,QAAQ;gCAAoC;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiB,QAAQ;gCAAoB;gCAAG;oCAAE,SAAS;oCAAO,QAAQ;gCAAoC;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAAwD;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAiB,QAAQ;gCAAoB;gCAAG;oCAAE,SAAS;oCAAO,QAAQ;gCAAoC;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAA2F;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA8C,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAsD,QAAQ;QAA6B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAe,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAAa,QAAQ;gBAAiD;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,OAAO;oBAAe,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAU;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAA8D,QAAQ;QAA8B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAiB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA+D;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;gBAA+B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;oBAAE;oBAAG,SAAS;gBAAwB;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAA6C,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAmD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAA0C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA+B;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAY;6BAAE;wBAAC;wBAAG,KAAK;4BAAE,QAAQ;wBAA6D;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAA2D;oBAAE;oBAAG,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAe,QAAQ;oBAAqD,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,eAAe;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAqD;YAAE;YAAG,OAAO;YAAS,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAoD,OAAO;YAAa,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoD;YAAE;YAAG,QAAQ;YAAoB,YAAY;gBAAC;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAU,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,OAAO;oBAAa,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsD;oBAAE;oBAAG,eAAe;oBAAyB,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA4C;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAwB;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAA+I,QAAQ;QAAwB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAA0B,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2C;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAyC;oBAAE;oBAAG,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAS,QAAQ;wBAAiC;qBAAE;gBAAC;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAU,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,OAAO;YAAW,QAAQ;YAAwB,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiD;oBAAE;oBAAG,QAAQ;oBAAiB,YAAY;wBAAC;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6D;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,QAAQ;4BAA2B,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmD;4BAAE;4BAAG,eAAe;4BAAsB,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAmD;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA6D;4BAAE;4BAAG,eAAe;4BAA8B,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAA2D;4BAAE;4BAAG,YAAY;gCAAC;oCAAE,WAAW;gCAAY;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,SAAS;oCAA+B,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAoB;oCAAE;oCAAG,OAAO;oCAAiB,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,YAAY;wCAAC;4CAAE,WAAW;wCAAY;wCAAG;4CAAE,YAAY;gDAAE,KAAK;oDAAE,QAAQ;gDAA0B;4CAAE;4CAAG,SAAS;wCAAO;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqD;4BAAE;4BAAG,eAAe;4BAAwB,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA+B;oBAAE;oBAAG,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqD;oBAAE;oBAAG,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,SAAS;4BAAK,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAuD;4BAAE;4BAAG,eAAe;4BAA0B,OAAO;4BAAS,YAAY;gCAAC;oCAAE,WAAW;gCAAQ;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAA6M,OAAO;YAAa,QAAQ;YAAiC,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAoD;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;QAAG,mBAAmB;YAAE,SAAS;YAAmD,OAAO;YAAa,QAAQ;YAAuC,YAAY;gBAAC;oBAAE,SAAS;oBAAa,QAAQ;gBAA0B;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAkD;gBAAG;oBAAE,WAAW;gBAAQ;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AAC594B,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/xml.mjs"], "sourcesContent": ["import java from './java.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"XML\", \"name\": \"xml\", \"patterns\": [{ \"begin\": \"(<\\\\?)\\\\s*([-_a-zA-Z0-9]+)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.xml\" } }, \"end\": \"(\\\\?>)\", \"name\": \"meta.tag.preprocessor.xml\", \"patterns\": [{ \"match\": \" ([a-zA-Z-]+)\", \"name\": \"entity.other.attribute-name.xml\" }, { \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] }, { \"begin\": \"(<!)(DOCTYPE)\\\\s+([:a-zA-Z_][:a-zA-Z0-9_.-]*)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"keyword.other.doctype.xml\" }, \"3\": { \"name\": \"variable.language.documentroot.xml\" } }, \"end\": \"\\\\s*(>)\", \"name\": \"meta.tag.sgml.doctype.xml\", \"patterns\": [{ \"include\": \"#internalSubset\" }] }, { \"include\": \"#comments\" }, { \"begin\": \"(<)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(?=(\\\\s[^>]*)?></\\\\2>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.xml\" }, \"3\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(>)(</)((?:([-_a-zA-Z0-9]+)(:))?([-_a-zA-Z0-9:]+))(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"punctuation.definition.tag.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"5\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"6\": { \"name\": \"entity.name.tag.localname.xml\" }, \"7\": { \"name\": \"punctuation.definition.tag.xml\" } }, \"name\": \"meta.tag.no-content.xml\", \"patterns\": [{ \"include\": \"#tagStuff\" }] }, { \"begin\": \"(</?)(?:([-\\\\w\\\\.]+)((:)))?([-\\\\w\\\\.:]+)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(/?>)\", \"name\": \"meta.tag.xml\", \"patterns\": [{ \"include\": \"#tagStuff\" }] }, { \"include\": \"#entity\" }, { \"include\": \"#bare-ampersand\" }, { \"begin\": \"<%@\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.xml\" } }, \"end\": \"%>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.xml\" } }, \"name\": \"source.java-props.embedded.xml\", \"patterns\": [{ \"match\": \"page|include|taglib\", \"name\": \"keyword.other.page-props.xml\" }] }, { \"begin\": \"<%[!=]?(?!--)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.xml\" } }, \"end\": \"(?!--)%>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.xml\" } }, \"name\": \"source.java.embedded.xml\", \"patterns\": [{ \"include\": \"source.java\" }] }, { \"begin\": \"<!\\\\[CDATA\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"]]>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.unquoted.cdata.xml\" }], \"repository\": { \"EntityDecl\": { \"begin\": \"(<!)(ENTITY)\\\\s+(%\\\\s+)?([:a-zA-Z_][:a-zA-Z0-9_.-]*)(\\\\s+(?:SYSTEM|PUBLIC)\\\\s+)?\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"keyword.other.entity.xml\" }, \"3\": { \"name\": \"punctuation.definition.entity.xml\" }, \"4\": { \"name\": \"variable.language.entity.xml\" }, \"5\": { \"name\": \"keyword.other.entitytype.xml\" } }, \"end\": \"(>)\", \"patterns\": [{ \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] }, \"bare-ampersand\": { \"match\": \"&\", \"name\": \"invalid.illegal.bad-ampersand.xml\" }, \"comments\": { \"patterns\": [{ \"begin\": \"<%--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.xml\" }, \"end\": \"--%>\", \"name\": \"comment.block.xml\" } }, { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.xml\" } }, \"end\": \"-->\", \"name\": \"comment.block.xml\", \"patterns\": [{ \"begin\": \"--(?!>)\", \"captures\": { \"0\": { \"name\": \"invalid.illegal.bad-comments-or-CDATA.xml\" } } }] }] }, \"doublequotedString\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.double.xml\", \"patterns\": [{ \"include\": \"#entity\" }, { \"include\": \"#bare-ampersand\" }] }, \"entity\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" }, \"3\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"match\": \"(&)([:a-zA-Z_][:a-zA-Z0-9_.-]*|#\\\\d+|#x[0-9a-fA-F]+)(;)\", \"name\": \"constant.character.entity.xml\" }, \"internalSubset\": { \"begin\": \"(\\\\[)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"end\": \"(\\\\])\", \"name\": \"meta.internalsubset.xml\", \"patterns\": [{ \"include\": \"#EntityDecl\" }, { \"include\": \"#parameterEntity\" }, { \"include\": \"#comments\" }] }, \"parameterEntity\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.xml\" }, \"3\": { \"name\": \"punctuation.definition.constant.xml\" } }, \"match\": \"(%)([:a-zA-Z_][:a-zA-Z0-9_.-]*)(;)\", \"name\": \"constant.character.parameter-entity.xml\" }, \"singlequotedString\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.single.xml\", \"patterns\": [{ \"include\": \"#entity\" }, { \"include\": \"#bare-ampersand\" }] }, \"tagStuff\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.xml\" }, \"2\": { \"name\": \"entity.other.attribute-name.xml\" }, \"3\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"4\": { \"name\": \"entity.other.attribute-name.localname.xml\" } }, \"match\": \"(?:^|\\\\s+)(?:([-\\\\w.]+)((:)))?([-\\\\w.:]+)\\\\s*=\" }, { \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] } }, \"scopeName\": \"text.xml\", \"embeddedLangs\": [\"java\"] });\nvar xml = [\n  ...java,\n  lang\n];\n\nexport { xml as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAA8B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,OAAO;YAAU,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,SAAS;oBAAiB,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiD,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAqC;YAAE;YAAG,OAAO;YAAW,QAAQ;YAA6B,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,SAAS;YAAwE,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAyD,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAiC;YAAE;YAAG,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,SAAS;YAA4C,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAS,QAAQ;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAM,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAAkC,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,QAAQ;gBAA+B;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAyC;YAAE;YAAG,OAAO;YAAY,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG;YAAE,SAAS;YAAiB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA4B;KAAE;IAAE,cAAc;QAAE,cAAc;YAAE,SAAS;YAAoF,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;gBAAG,KAAK;oBAAE,QAAQ;gBAAoC;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,OAAO;YAAO,YAAY;gBAAC;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAK,QAAQ;QAAoC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;wBAAG,OAAO;wBAAQ,QAAQ;oBAAoB;gBAAE;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,OAAO;oBAAO,QAAQ;oBAAqB,YAAY;wBAAC;4BAAE,SAAS;4BAAW,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAA4C;4BAAE;wBAAE;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAA2D,QAAQ;QAAgC;QAAG,kBAAkB;YAAE,SAAS;YAAS,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,OAAO;YAAS,QAAQ;YAA2B,YAAY;gBAAC;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAsC,QAAQ;QAA0C;QAAG,sBAAsB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;YAA4B,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAkB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;gBAAiD;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;IAAE;IAAG,aAAa;IAAY,iBAAiB;QAAC;KAAO;AAAC;AAC1tL,IAAI,MAAM;OACL,kMAAA,CAAA,UAAI;IACP;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/xsl.mjs"], "sourcesContent": ["import xml from './xml.mjs';\nimport './java.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"XSL\", \"name\": \"xsl\", \"patterns\": [{ \"begin\": \"(<)(xsl)((:))(template)\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.xml\" }, \"2\": { \"name\": \"entity.name.tag.namespace.xml\" }, \"3\": { \"name\": \"entity.name.tag.xml\" }, \"4\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"5\": { \"name\": \"entity.name.tag.localname.xml\" } }, \"end\": \"(>)\", \"name\": \"meta.tag.xml.template\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.namespace.xml\" }, \"2\": { \"name\": \"entity.other.attribute-name.xml\" }, \"3\": { \"name\": \"punctuation.separator.namespace.xml\" }, \"4\": { \"name\": \"entity.other.attribute-name.localname.xml\" } }, \"match\": \" (?:([-_a-zA-Z0-9]+)((:)))?([a-zA-Z-]+)\" }, { \"include\": \"#doublequotedString\" }, { \"include\": \"#singlequotedString\" }] }, { \"include\": \"text.xml\" }], \"repository\": { \"doublequotedString\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.double.xml\" }, \"singlequotedString\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.xml\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.xml\" } }, \"name\": \"string.quoted.single.xml\" } }, \"scopeName\": \"text.xml.xsl\", \"embeddedLangs\": [\"xml\"] });\nvar xsl = [\n  ...xml,\n  lang\n];\n\nexport { xsl as default };\n"], "names": [], "mappings": ";;;AAAA;;;AAGA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,QAAQ;IAAO,YAAY;QAAC;YAAE,SAAS;YAA2B,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;gBAAG,KAAK;oBAAE,QAAQ;gBAAsC;gBAAG,KAAK;oBAAE,QAAQ;gBAAgC;YAAE;YAAG,OAAO;YAAO,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;wBAAG,KAAK;4BAAE,QAAQ;wBAAkC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,SAAS;gBAA0C;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,sBAAsB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA2B;QAAG,sBAAsB;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA0C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAwC;YAAE;YAAG,QAAQ;QAA2B;IAAE;IAAG,aAAa;IAAgB,iBAAiB;QAAC;KAAM;AAAC;AACv4C,IAAI,MAAM;OACL,iMAAA,CAAA,UAAG;IACN;CACD", "ignoreList": [0], "debugId": null}}]}