{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/codeowners.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"CODEOWNERS\", \"name\": \"codeowners\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pattern\" }, { \"include\": \"#owner\" }], \"repository\": { \"comment\": { \"patterns\": [{ \"begin\": \"^\\\\s*#\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.codeowners\" } }, \"end\": \"$\", \"name\": \"comment.line.codeowners\" }] }, \"owner\": { \"match\": \"\\\\S*@\\\\S+\", \"name\": \"storage.type.function.codeowners\" }, \"pattern\": { \"match\": \"^\\\\s*(\\\\S+)\", \"name\": \"variable.other.codeowners\" } }, \"scopeName\": \"text.codeowners\" });\nvar codeowners = [\n  lang\n];\n\nexport { codeowners as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAc,QAAQ;IAAc,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAS;KAAE;IAAE,cAAc;QAAE,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAU,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4C;oBAAE;oBAAG,OAAO;oBAAK,QAAQ;gBAA0B;aAAE;QAAC;QAAG,SAAS;YAAE,SAAS;YAAa,QAAQ;QAAmC;QAAG,WAAW;YAAE,SAAS;YAAe,QAAQ;QAA4B;IAAE;IAAG,aAAa;AAAkB;AAC1iB,IAAI,aAAa;IACf;CACD", "ignoreList": [0], "debugId": null}}]}