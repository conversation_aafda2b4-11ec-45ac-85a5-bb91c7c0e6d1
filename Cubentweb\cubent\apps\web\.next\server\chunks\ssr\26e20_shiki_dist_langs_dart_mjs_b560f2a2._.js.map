{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/dart.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Dart\", \"name\": \"dart\", \"patterns\": [{ \"match\": \"^(#!.*)$\", \"name\": \"meta.preprocessor.script.dart\" }, { \"begin\": \"^\\\\w*\\\\b(augment\\\\s+library|library|import\\\\s+augment|import|part\\\\s+of|part|export)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.import.dart\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.dart\" } }, \"name\": \"meta.declaration.dart\", \"patterns\": [{ \"include\": \"#strings\" }, { \"include\": \"#comments\" }, { \"match\": \"\\\\b(as|show|hide)\\\\b\", \"name\": \"keyword.other.import.dart\" }, { \"match\": \"\\\\b(if)\\\\b\", \"name\": \"keyword.control.dart\" }] }, { \"include\": \"#comments\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#annotations\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants-and-special-vars\" }, { \"include\": \"#operators\" }, { \"include\": \"#strings\" }], \"repository\": { \"annotations\": { \"patterns\": [{ \"match\": \"@[a-zA-Z]+\", \"name\": \"storage.type.annotation.dart\" }] }, \"class-identifier\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\b(bool|num|int|double|dynamic)\\\\b(?!\\\\$)\", \"name\": \"support.class.dart\" }, { \"match\": \"(?<!\\\\$)\\\\bvoid\\\\b(?!\\\\$)\", \"name\": \"storage.type.primitive.dart\" }, { \"begin\": \"(?<![a-zA-Z0-9_$])([_$]*[A-Z][a-zA-Z0-9_$]*)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"support.class.dart\" } }, \"end\": \"(?!<)\", \"patterns\": [{ \"include\": \"#type-args\" }] }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.dart\" } }, \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.empty.dart\" }, { \"include\": \"#comments-doc-oldschool\" }, { \"include\": \"#comments-doc\" }, { \"include\": \"#comments-inline\" }] }, \"comments-block\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.dart\", \"patterns\": [{ \"include\": \"#comments-block\" }] }] }, \"comments-doc\": { \"patterns\": [{ \"begin\": \"///\", \"name\": \"comment.block.documentation.dart\", \"patterns\": [{ \"include\": \"#dartdoc\" }], \"while\": \"^\\\\s*///\" }] }, \"comments-doc-oldschool\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.dart\", \"patterns\": [{ \"include\": \"#comments-doc-oldschool\" }, { \"include\": \"#comments-block\" }, { \"include\": \"#dartdoc\" }] }] }, \"comments-inline\": { \"patterns\": [{ \"include\": \"#comments-block\" }, { \"captures\": { \"1\": { \"name\": \"comment.line.double-slash.dart\" } }, \"match\": \"((//).*)$\" }] }, \"constants-and-special-vars\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\b(true|false|null)\\\\b(?!\\\\$)\", \"name\": \"constant.language.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(this|super|augmented)\\\\b(?!\\\\$)\", \"name\": \"variable.language.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b((0(x|X)[0-9a-fA-F][0-9a-fA-F_]*)|((\\\\d[0-9_]*\\\\.?[0-9_]*)|(\\\\.\\\\d[0-9_]*))((e|E)(\\\\+|-)?\\\\d[0-9_]*)?)\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.dart\" }, { \"include\": \"#class-identifier\" }, { \"include\": \"#function-identifier\" }] }, \"dartdoc\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"variable.name.source.dart\" } }, \"match\": \"(\\\\[.*?\\\\])\" }, { \"captures\": { \"0\": { \"name\": \"variable.name.source.dart\" } }, \"match\": \"^ {4,}(?![ \\\\*]).*\" }, { \"begin\": \"```.*?$\", \"contentName\": \"variable.other.source.dart\", \"end\": \"```\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.source.dart\" } }, \"match\": \"(`[^`]+?`)\" }, { \"captures\": { \"2\": { \"name\": \"variable.other.source.dart\" } }, \"match\": \"(\\\\* ((    ).*))$\" }] }, \"expression\": { \"patterns\": [{ \"include\": \"#constants-and-special-vars\" }, { \"include\": \"#strings\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.dart\" }, { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"function-identifier\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.dart\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-args\" }] } }, \"match\": \"([_$]*[a-z][a-zA-Z0-9_$]*)(<(?:[a-zA-Z0-9_$<>?]|,\\\\s*|\\\\s+extends\\\\s+)+>)?[!?]?\\\\(\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\bas\\\\b(?!\\\\$)\", \"name\": \"keyword.cast.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(try|on|catch|finally|throw|rethrow)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.catch-exception.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(break|case|continue|default|do|else|for|if|in|switch|while|when)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(sync(\\\\*)?|async(\\\\*)?|await|yield(\\\\*)?)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.dart\" }, { \"match\": \"(?<!\\\\$)\\\\bassert\\\\b(?!\\\\$)\", \"name\": \"keyword.control.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(new)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.new.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(return)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.return.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(abstract|sealed|base|interface|class|enum|extends|extension\\\\s+type|extension|external|factory|implements|get(?![(<])|mixin|native|operator|set(?![(<])|typedef|with|covariant)\\\\b(?!\\\\$)\", \"name\": \"keyword.declaration.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(macro|augment|static|final|const|required|late)\\\\b(?!\\\\$)\", \"name\": \"storage.modifier.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(?:void|var)\\\\b(?!\\\\$)\", \"name\": \"storage.type.primitive.dart\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\b(is!?)\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.dart\" }, { \"match\": \"\\\\?|:\", \"name\": \"keyword.operator.ternary.dart\" }, { \"match\": \"(<<|>>>?|~|\\\\^|\\\\||&)\", \"name\": \"keyword.operator.bitwise.dart\" }, { \"match\": \"((&|\\\\^|\\\\||<<|>>>?)=)\", \"name\": \"keyword.operator.assignment.bitwise.dart\" }, { \"match\": \"(=>)\", \"name\": \"keyword.operator.closure.dart\" }, { \"match\": \"(==|!=|<=?|>=?)\", \"name\": \"keyword.operator.comparison.dart\" }, { \"match\": \"(([+*/%-]|\\\\~)=)\", \"name\": \"keyword.operator.assignment.arithmetic.dart\" }, { \"match\": \"(=)\", \"name\": \"keyword.operator.assignment.dart\" }, { \"match\": \"(--|\\\\+\\\\+)\", \"name\": \"keyword.operator.increment-decrement.dart\" }, { \"match\": \"(-|\\\\+|\\\\*|\\\\/|\\\\~\\\\/|%)\", \"name\": \"keyword.operator.arithmetic.dart\" }, { \"match\": \"(!|&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.dart\" }] }, \"punctuation\": { \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.comma.dart\" }, { \"match\": \";\", \"name\": \"punctuation.terminator.dart\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.dot.dart\" }] }, \"string-interp\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.dart\" } }, \"match\": \"\\\\$(\\\\w+)\", \"name\": \"meta.embedded.expression.dart\" }, { \"begin\": \"\\\\$\\\\{\", \"end\": \"\\\\}\", \"name\": \"meta.embedded.expression.dart\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.dart\" }] }, \"strings\": { \"patterns\": [{ \"begin\": '(?<!r)\"\"\"', \"end\": '\"\"\"(?!\")', \"name\": \"string.interpolated.triple.double.dart\", \"patterns\": [{ \"include\": \"#string-interp\" }] }, { \"begin\": \"(?<!r)'''\", \"end\": \"'''(?!')\", \"name\": \"string.interpolated.triple.single.dart\", \"patterns\": [{ \"include\": \"#string-interp\" }] }, { \"begin\": 'r\"\"\"', \"end\": '\"\"\"(?!\")', \"name\": \"string.quoted.triple.double.dart\" }, { \"begin\": \"r'''\", \"end\": \"'''(?!')\", \"name\": \"string.quoted.triple.single.dart\" }, { \"begin\": '(?<!\\\\|r)\"', \"end\": '\"', \"name\": \"string.interpolated.double.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }, { \"include\": \"#string-interp\" }] }, { \"begin\": 'r\"', \"end\": '\"', \"name\": \"string.quoted.double.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }] }, { \"begin\": \"(?<!\\\\|r)'\", \"end\": \"'\", \"name\": \"string.interpolated.single.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }, { \"include\": \"#string-interp\" }] }, { \"begin\": \"r'\", \"end\": \"'\", \"name\": \"string.quoted.single.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }] }] }, \"type-args\": { \"begin\": \"(<)\", \"beginCaptures\": { \"1\": { \"name\": \"other.source.dart\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"other.source.dart\" } }, \"patterns\": [{ \"include\": \"#class-identifier\" }, { \"match\": \",\" }, { \"match\": \"extends\", \"name\": \"keyword.declaration.dart\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.dart\" });\nvar dart = [\n  lang\n];\n\nexport { dart as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,QAAQ;IAAQ,YAAY;QAAC;YAAE,SAAS;YAAY,QAAQ;QAAgC;QAAG;YAAE,SAAS;YAA2F,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;YAAE;YAAG,QAAQ;YAAyB,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAA4B;gBAAG;oBAAE,SAAS;oBAAc,QAAQ;gBAAuB;aAAE;QAAC;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAA8B;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAW;KAAE;IAAE,cAAc;QAAE,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAc,QAAQ;gBAA+B;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsD,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAmD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAa;qBAAE;gBAAC;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,SAAS;oBAAY,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAmB;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,OAAO;oBAAQ,QAAQ;oBAAsB,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;qBAAE;gBAAC;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAO,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAAW;qBAAE;oBAAE,SAAS;gBAAW;aAAE;QAAC;QAAG,0BAA0B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,OAAO;oBAAQ,QAAQ;oBAAoC,YAAY;wBAAC;4BAAE,WAAW;wBAA0B;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,mBAAmB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;gBAAY;aAAE;QAAC;QAAG,8BAA8B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0C,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAA+C,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAA+H,QAAQ;gBAAwB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAc;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAqB;gBAAG;oBAAE,SAAS;oBAAW,eAAe;oBAA8B,OAAO;gBAAM;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;gBAAa;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;gBAAoB;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAO,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,YAAY;gCAAC;oCAAE,WAAW;gCAAa;6BAAE;wBAAC;oBAAE;oBAAG,SAAS;gBAAqF;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,QAAQ;gBAAoB;gBAAG;oBAAE,SAAS;oBAA6D,QAAQ;gBAAuC;gBAAG;oBAAE,SAAS;oBAA0F,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAmE,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAA+B,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAA8B,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAiC,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAyM,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAyE,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAqC,QAAQ;gBAA8B;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+B,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAyB,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAA0B,QAAQ;gBAA2C;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAoB,QAAQ;gBAA8C;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAA4C;gBAAG;oBAAE,SAAS;oBAA4B,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAgC;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;oBAAa,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAU,OAAO;oBAAO,QAAQ;oBAAiC,YAAY;wBAAC;4BAAE,WAAW;wBAAc;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAiC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAa,OAAO;oBAAY,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAa,OAAO;oBAAY,QAAQ;oBAA0C,YAAY;wBAAC;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,OAAO;oBAAY,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAQ,OAAO;oBAAY,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAc,OAAO;oBAAK,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAAyB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,OAAO;oBAAK,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAAyB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAc,OAAO;oBAAK,QAAQ;oBAAmC,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAAyB;wBAAG;4BAAE,WAAW;wBAAiB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,OAAO;oBAAK,QAAQ;oBAA6B,YAAY;wBAAC;4BAAE,SAAS;4BAAO,QAAQ;wBAAyB;qBAAE;gBAAC;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAAoB;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,SAAS;gBAAI;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA2B;gBAAG;oBAAE,WAAW;gBAAY;aAAE;QAAC;IAAE;IAAG,aAAa;AAAc;AACnrP,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}