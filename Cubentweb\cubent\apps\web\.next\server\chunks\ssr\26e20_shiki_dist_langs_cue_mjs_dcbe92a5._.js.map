{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/cue.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"CUE\", \"fileTypes\": [\"cue\"], \"name\": \"cue\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.package\" }, \"2\": { \"name\": \"entity.name.namespace\" } }, \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(package)[ \\\\t]+([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*)(?![\\\\p{L}\\\\p{Nd}_$#])\" }, { \"patterns\": [{ \"begin\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(import)[ \\\\t]+(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.import\" }, \"2\": { \"name\": \"punctuation.section.parens.begin\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end\" } }, \"name\": \"meta.imports\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.namespace\" }, \"2\": { \"name\": \"punctuation.definition.string.begin\" }, \"3\": { \"name\": \"string.quoted.double-import\" }, \"4\": { \"name\": \"punctuation.colon\" }, \"5\": { \"name\": \"entity.name\" }, \"6\": { \"name\": \"punctuation.definition.string.end\" } }, \"match\": '(?:([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*)[ \\\\t]+)?(\")([^:\"]+)(?:(:)([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*))?(\")', \"name\": \"meta.import-spec\" }, { \"match\": \";\", \"name\": \"punctuation.separator\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.other.import\" }, \"2\": { \"name\": \"entity.name.namespace\" }, \"3\": { \"name\": \"punctuation.definition.string.begin\" }, \"4\": { \"name\": \"string.quoted.double-import\" }, \"5\": { \"name\": \"punctuation.colon\" }, \"6\": { \"name\": \"entity.name\" }, \"7\": { \"name\": \"punctuation.definition.string.end\" } }, \"match\": '(?<![\\\\p{L}\\\\p{Nd}_$#])(import)[ \\\\t]+(?:([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*)[ \\\\t]+)?(\")([^:\"]+)(?:(:)([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*))?(\")', \"name\": \"meta.import\" }] }, { \"include\": \"#punctuation_comma\" }, { \"include\": \"#declaration\" }, { \"include\": \"#invalid_in_braces\" }], \"repository\": { \"attribute_element\": { \"patterns\": [{ \"begin\": \"([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)(=)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other\" }, \"2\": { \"name\": \"punctuation.bind\" } }, \"end\": \"(?=[,)])\", \"patterns\": [{ \"include\": \"#attribute_string\" }] }, { \"begin\": \"([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other\" }, \"2\": { \"name\": \"punctuation.attribute-elements.begin\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.attribute-elements.end\" } }, \"patterns\": [{ \"include\": \"#punctuation_comma\" }, { \"include\": \"#attribute_element\" }] }, { \"include\": \"#attribute_string\" }] }, \"attribute_string\": { \"patterns\": [{ \"include\": \"#string\" }, { \"match\": `[^\\\\n,\"'#=()]+`, \"name\": \"string.unquoted\" }, { \"match\": \"[^,)]+\", \"name\": \"invalid\" }] }, \"comment\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block\" }] }, \"declaration\": { \"patterns\": [{ \"begin\": \"(@)([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.annotation\" }, \"2\": { \"name\": \"variable.annotation\" }, \"3\": { \"name\": \"punctuation.attribute-elements.begin\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.attribute-elements.end\" } }, \"name\": \"meta.annotation\", \"patterns\": [{ \"include\": \"#punctuation_comma\" }, { \"include\": \"#attribute_element\" }] }, { \"match\": \"(?<!:)::(?!:)\", \"name\": \"punctuation.isa\" }, { \"include\": \"#punctuation_colon\" }, { \"match\": \"\\\\?\", \"name\": \"punctuation.option\" }, { \"match\": \"(?<![=!><])=(?![=~])\", \"name\": \"punctuation.bind\" }, { \"match\": \"<-\", \"name\": \"punctuation.arrow\" }, { \"include\": \"#expression\" }] }, \"expression\": { \"patterns\": [{ \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.for\" }, \"2\": { \"name\": \"variable.other\" }, \"3\": { \"name\": \"punctuation.separator\" }, \"4\": { \"name\": \"variable.other\" }, \"5\": { \"name\": \"keyword.control.in\" } }, \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(for)[ \\\\t]+([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)(?:[ \\\\t]*(,)[ \\\\t]*([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+))?[ \\\\t]+(in)(?![\\\\p{L}\\\\p{Nd}_$#])\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])if(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"keyword.control.conditional\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.let\" }, \"2\": { \"name\": \"variable.other\" }, \"3\": { \"name\": \"punctuation.bind\" } }, \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(let)[ \\\\t]+([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)[ \\\\t]*(=)(?![=])\" }] }, { \"patterns\": [{ \"match\": \"[+\\\\-\\\\*]|/(?![/*])\", \"name\": \"keyword.operator\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(?:div|mod|quo|rem)(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"keyword.operator.word\" }, { \"match\": \"=[=~]|![=~]|<=|>=|[<](?![-=])|[>](?![=])\", \"name\": \"keyword.operator.comparison\" }, { \"match\": \"&{2}|\\\\|{2}|!(?![=~])\", \"name\": \"keyword.operator.logical\" }, { \"match\": \"&(?!&)|\\\\|(?!\\\\|)\", \"name\": \"keyword.operator.set\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor\" }, \"2\": { \"name\": \"variable.other.member\" } }, \"match\": \"(?<!\\\\.)(\\\\.)([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)(?![\\\\p{L}\\\\p{Nd}_$#])\" }, { \"patterns\": [{ \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])_(?!\\\\|)(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"constant.language.top\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])_\\\\|_(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"constant.language.bottom\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])null(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"constant.language.null\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(?:true|false)(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"constant.language.bool\" }, { \"patterns\": [{ \"patterns\": [{ \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])\\\\d(?:_?\\\\d)*\\\\.(?:\\\\d(?:_?\\\\d)*)?(?:[eE][+\\\\-]?\\\\d(?:_?\\\\d)*)?(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.float.decimal\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])\\\\d(?:_?\\\\d)*[eE][+\\\\-]?\\\\d(?:_?\\\\d)*(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.float.decimal\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])\\\\.\\\\d(?:_?\\\\d)*(?:[eE][+\\\\-]?\\\\d(?:_?\\\\d)*)?(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.float.decimal\" }] }, { \"patterns\": [{ \"patterns\": [{ \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])(?:0|[1-9](?:_?\\\\d)*)(?:\\\\.\\\\d(?:_?\\\\d)*)?(?:[KMGTPEYZ]i?)(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.integer.other\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])\\\\.\\\\d(?:_?\\\\d)*(?:[KMGTPEYZ]i?)(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.integer.other\" }] }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])(?:0|[1-9](?:_?\\\\d)*)(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.integer.decimal\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])0b[0-1](?:_?[0-1])*(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.integer.binary\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])0[xX][0-9a-fA-F](?:_?[0-9a-fA-F])*(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.integer.hexadecimal\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_\\\\.])0o?[0-7](?:_?[0-7])*(?![\\\\p{L}\\\\p{Nd}_\\\\.])\", \"name\": \"constant.numeric.integer.octal\" }] }] }, { \"include\": \"#string\" }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(?:bool|u?int(?:8|16|32|64|128)?|float(?:32|64)?|string|bytes|number|rune)(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"support.type\" }, { \"patterns\": [{ \"begin\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(len|close|and|or)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function\" }, \"2\": { \"name\": \"punctuation.section.parens.begin\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end\" } }, \"name\": \"meta.function-call\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#punctuation_comma\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"begin\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*)(\\\\.)(\\\\p{Lu}[\\\\p{L}\\\\p{Nd}_$#]*)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.module\" }, \"2\": { \"name\": \"punctuation\" }, \"3\": { \"name\": \"support.function\" }, \"4\": { \"name\": \"punctuation.section.parens.begin\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end\" } }, \"name\": \"meta.function-call\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#punctuation_comma\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }] }, { \"match\": \"(?<![\\\\p{L}\\\\p{Nd}_$#])(?:[\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)(?![\\\\p{L}\\\\p{Nd}_$#])\", \"name\": \"variable.other\" }, { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.struct.begin\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.struct.end\" } }, \"name\": \"meta.struct\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#punctuation_comma\" }, { \"include\": \"#punctuation_ellipsis\" }, { \"include\": \"#declaration\" }, { \"include\": \"#invalid_in_braces\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.begin\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end\" } }, \"name\": \"meta.brackets\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#punctuation_colon\" }, { \"include\": \"#punctuation_comma\" }, { \"include\": \"#punctuation_ellipsis\" }, { \"captures\": { \"1\": { \"name\": \"variable.other\" }, \"2\": { \"name\": \"punctuation.alias\" } }, \"match\": \"([\\\\p{L}$#][\\\\p{L}\\\\p{Nd}_$#]*|_[\\\\p{L}\\\\p{Nd}_$#]+)[ \\\\t]*(=)\" }, { \"include\": \"#expression\" }, { \"match\": \"[^\\\\]]+\", \"name\": \"invalid\" }] }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end\" } }, \"name\": \"meta.parens\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#punctuation_comma\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }] }] }, \"invalid_in_braces\": { \"match\": \"[^}]+\", \"name\": \"invalid\" }, \"invalid_in_parens\": { \"match\": \"[^)]+\", \"name\": \"invalid\" }, \"punctuation_colon\": { \"match\": \"(?<!:):(?!:)\", \"name\": \"punctuation.colon\" }, \"punctuation_comma\": { \"match\": \",\", \"name\": \"punctuation.separator\" }, \"punctuation_ellipsis\": { \"match\": \"(?<!\\\\.)\\\\.{3}(?!\\\\.)\", \"name\": \"punctuation.ellipsis\" }, \"string\": { \"patterns\": [{ \"begin\": '#\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.double-multiline\", \"end\": '\"\"\"#', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": '\\\\\\\\#(?:\"\"\"|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\#(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"invalid.illegal\" }, { \"begin\": \"\\\\\\\\#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\#.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": '#\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.double\", \"end\": '\"#', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": '\\\\\\\\#(?:\"|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\#(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"invalid.illegal\" }, { \"begin\": \"\\\\\\\\#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\#.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": \"#'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.single-multiline\", \"end\": \"'''#\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": \"\\\\\\\\#(?:'''|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\", \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\#(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"constant.character.escape\" }, { \"begin\": \"\\\\\\\\#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\#.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": \"#'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.single\", \"end\": \"'#\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": \"\\\\\\\\#(?:'|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\", \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\#(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"constant.character.escape\" }, { \"begin\": \"\\\\\\\\#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\#.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.double-multiline\", \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": '\\\\\\\\(?:\"\"\"|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"invalid.illegal\" }, { \"begin\": \"\\\\\\\\\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.double\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": '\\\\\\\\(?:\"|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})', \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"invalid.illegal\" }, { \"begin\": \"\\\\\\\\\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": \"'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.single-multiline\", \"end\": \"'''\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": \"\\\\\\\\(?:'''|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\", \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"constant.character.escape\" }, { \"begin\": \"\\\\\\\\\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.single\", \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\", \"patterns\": [{ \"match\": \"\\\\\\\\(?:'|/|\\\\\\\\|[abfnrtv]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\", \"name\": \"constant.character.escape\" }, { \"match\": \"\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2})\", \"name\": \"constant.character.escape\" }, { \"begin\": \"\\\\\\\\\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.begin\" } }, \"contentName\": \"source.cue.embedded\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.interpolation.end\" } }, \"name\": \"meta.interpolation\", \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#expression\" }, { \"include\": \"#invalid_in_parens\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal\" }] }, { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin\" } }, \"contentName\": \"string.quoted.backtick\", \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end\" } }, \"name\": \"meta.string\" }] }, \"whitespace\": { \"match\": \"[ \\\\t\\\\r\\\\n]+\" } }, \"scopeName\": \"source.cue\" });\nvar cue = [\n  lang\n];\n\nexport { cue as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAO,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAO,YAAY;QAAC;YAAE,WAAW;QAAc;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,SAAS;QAA+F;QAAG;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA+C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,QAAQ;oBAAgB,YAAY;wBAAC;4BAAE,WAAW;wBAAc;wBAAG;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAwB;gCAAG,KAAK;oCAAE,QAAQ;gCAAsC;gCAAG,KAAK;oCAAE,QAAQ;gCAA8B;gCAAG,KAAK;oCAAE,QAAQ;gCAAoB;gCAAG,KAAK;oCAAE,QAAQ;gCAAc;gCAAG,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,SAAS;4BAAoG,QAAQ;wBAAmB;wBAAG;4BAAE,SAAS;4BAAK,QAAQ;wBAAwB;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAsC;wBAAG,KAAK;4BAAE,QAAQ;wBAA8B;wBAAG,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAc;wBAAG,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,SAAS;oBAA0I,QAAQ;gBAAc;aAAE;QAAC;QAAG;YAAE,WAAW;QAAqB;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAqB;KAAE;IAAE,cAAc;QAAE,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,OAAO;oBAAY,YAAY;wBAAC;4BAAE,WAAW;wBAAoB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAA6D,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiB;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS,CAAC,cAAc,CAAC;oBAAE,QAAQ;gBAAkB;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAAU;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,SAAS;oBAAe,QAAQ;gBAAe;gBAAG;oBAAE,SAAS;oBAAQ,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;oBAAE;oBAAG,OAAO;oBAAQ,QAAQ;gBAAgB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgE,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAuC;oBAAE;oBAAG,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAqC;oBAAE;oBAAG,QAAQ;oBAAmB,YAAY;wBAAC;4BAAE,WAAW;wBAAqB;wBAAG;4BAAE,WAAW;wBAAqB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAkB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAqB;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAAmB;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAoB;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAC;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAiB;gCAAG,KAAK;oCAAE,QAAQ;gCAAwB;gCAAG,KAAK;oCAAE,QAAQ;gCAAiB;gCAAG,KAAK;oCAAE,QAAQ;gCAAqB;4BAAE;4BAAG,SAAS;wBAAqM;wBAAG;4BAAE,SAAS;4BAAmD,QAAQ;wBAA8B;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAsB;gCAAG,KAAK;oCAAE,QAAQ;gCAAiB;gCAAG,KAAK;oCAAE,QAAQ;gCAAmB;4BAAE;4BAAG,SAAS;wBAA2G;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAuB,QAAQ;wBAAmB;wBAAG;4BAAE,SAAS;4BAAoE,QAAQ;wBAAwB;wBAAG;4BAAE,SAAS;4BAA4C,QAAQ;wBAA8B;wBAAG;4BAAE,SAAS;4BAAyB,QAAQ;wBAA2B;wBAAG;4BAAE,SAAS;4BAAqB,QAAQ;wBAAuB;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;gBAA0F;gBAAG;oBAAE,YAAY;wBAAC;4BAAE,SAAS;4BAAyD,QAAQ;wBAAwB;wBAAG;4BAAE,SAAS;4BAAsD,QAAQ;wBAA2B;wBAAG;4BAAE,SAAS;4BAAqD,QAAQ;wBAAyB;wBAAG;4BAAE,SAAS;4BAA+D,QAAQ;wBAAyB;wBAAG;4BAAE,YAAY;gCAAC;oCAAE,YAAY;wCAAC;4CAAE,SAAS;4CAAkH,QAAQ;wCAAiC;wCAAG;4CAAE,SAAS;4CAAwF,QAAQ;wCAAiC;wCAAG;4CAAE,SAAS;4CAAgG,QAAQ;wCAAiC;qCAAE;gCAAC;gCAAG;oCAAE,YAAY;wCAAC;4CAAE,YAAY;gDAAC;oDAAE,SAAS;oDAA6G,QAAQ;gDAAiC;gDAAG;oDAAE,SAAS;oDAAmF,QAAQ;gDAAiC;6CAAE;wCAAC;wCAAG;4CAAE,SAAS;4CAAwE,QAAQ;wCAAmC;wCAAG;4CAAE,SAAS;4CAAsE,QAAQ;wCAAkC;wCAAG;4CAAE,SAAS;4CAAqF,QAAQ;wCAAuC;wCAAG;4CAAE,SAAS;4CAAuE,QAAQ;wCAAiC;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,SAAS;4BAA2H,QAAQ;wBAAe;wBAAG;4BAAE,YAAY;gCAAC;oCAAE,SAAS;oCAAkD,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAmB;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;oCAAE;oCAAG,QAAQ;oCAAsB,YAAY;wCAAC;4CAAE,WAAW;wCAAc;wCAAG;4CAAE,WAAW;wCAAW;wCAAG;4CAAE,WAAW;wCAAqB;wCAAG;4CAAE,WAAW;wCAAc;wCAAG;4CAAE,WAAW;wCAAqB;qCAAE;gCAAC;gCAAG;oCAAE,SAAS;oCAAgG,iBAAiB;wCAAE,KAAK;4CAAE,QAAQ;wCAAiB;wCAAG,KAAK;4CAAE,QAAQ;wCAAc;wCAAG,KAAK;4CAAE,QAAQ;wCAAmB;wCAAG,KAAK;4CAAE,QAAQ;wCAAmC;oCAAE;oCAAG,OAAO;oCAAO,eAAe;wCAAE,KAAK;4CAAE,QAAQ;wCAAiC;oCAAE;oCAAG,QAAQ;oCAAsB,YAAY;wCAAC;4CAAE,WAAW;wCAAc;wCAAG;4CAAE,WAAW;wCAAW;wCAAG;4CAAE,WAAW;wCAAqB;wCAAG;4CAAE,WAAW;wCAAc;wCAAG;4CAAE,WAAW;wCAAqB;qCAAE;gCAAC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAuG,QAAQ;wBAAiB;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAsC;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAoC;4BAAE;4BAAG,QAAQ;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,WAAW;gCAAwB;gCAAG;oCAAE,WAAW;gCAAe;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAqC;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,QAAQ;4BAAiB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,WAAW;gCAAwB;gCAAG;oCAAE,YAAY;wCAAE,KAAK;4CAAE,QAAQ;wCAAiB;wCAAG,KAAK;4CAAE,QAAQ;wCAAoB;oCAAE;oCAAG,SAAS;gCAAiE;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,SAAS;oCAAW,QAAQ;gCAAU;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAO,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAAmC;4BAAE;4BAAG,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAiC;4BAAE;4BAAG,QAAQ;4BAAe,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAW;gCAAG;oCAAE,WAAW;gCAAqB;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;qBAAE;gBAAC;aAAE;QAAC;QAAG,qBAAqB;YAAE,SAAS;YAAS,QAAQ;QAAU;QAAG,qBAAqB;YAAE,SAAS;YAAS,QAAQ;QAAU;QAAG,qBAAqB;YAAE,SAAS;YAAgB,QAAQ;QAAoB;QAAG,qBAAqB;YAAE,SAAS;YAAK,QAAQ;QAAwB;QAAG,wBAAwB;YAAE,SAAS;YAAyB,QAAQ;QAAuB;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAkC,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAAiE,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAAkB;wBAAG;4BAAE,SAAS;4BAAY,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAM,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAA+D,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAAkB;wBAAG;4BAAE,SAAS;4BAAY,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAQ,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAkC,OAAO;oBAAQ,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAAiE,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAY,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAM,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAM,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAA+D,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAqC,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAY,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAU,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAkC,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAAgE,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAAkB;wBAAG;4BAAE,SAAS;4BAAW,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAA8D,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAAkB;wBAAG;4BAAE,SAAS;4BAAW,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAkC,OAAO;oBAAO,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAAgE,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAW,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAAwB,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;oBAAe,YAAY;wBAAC;4BAAE,SAAS;4BAA8D,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAoC,QAAQ;wBAA4B;wBAAG;4BAAE,SAAS;4BAAW,iBAAiB;gCAAE,KAAK;oCAAE,QAAQ;gCAA0C;4BAAE;4BAAG,eAAe;4BAAuB,OAAO;4BAAO,eAAe;gCAAE,KAAK;oCAAE,QAAQ;gCAAwC;4BAAE;4BAAG,QAAQ;4BAAsB,YAAY;gCAAC;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAc;gCAAG;oCAAE,WAAW;gCAAqB;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAS,QAAQ;wBAAkB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAK,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsC;oBAAE;oBAAG,eAAe;oBAA0B,OAAO;oBAAK,eAAe;wBAAE,KAAK;4BAAE,QAAQ;wBAAoC;oBAAE;oBAAG,QAAQ;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,SAAS;QAAgB;IAAE;IAAG,aAAa;AAAa;AAChtiB,IAAI,MAAM;IACR;CACD", "ignoreList": [0], "debugId": null}}]}