{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/tasl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Tasl\", \"fileTypes\": [\"tasl\"], \"name\": \"tasl\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#namespace\" }, { \"include\": \"#type\" }, { \"include\": \"#class\" }, { \"include\": \"#edge\" }], \"repository\": { \"class\": { \"begin\": \"(?:^\\\\s*)(class)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tasl.class\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#key\" }, { \"include\": \"#export\" }, { \"include\": \"#expression\" }] }, \"comment\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.tasl\" } }, \"match\": \"(#).*$\", \"name\": \"comment.line.number-sign.tasl\" }, \"component\": { \"begin\": \"->\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.tasl.component\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"coproduct\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.coproduct\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.coproduct\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#term\" }, { \"include\": \"#option\" }] }, \"datatype\": { \"match\": \"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\", \"name\": \"string.regexp\" }, \"edge\": { \"begin\": \"(?:^\\\\s*)(edge)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tasl.edge\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#key\" }, { \"include\": \"#export\" }, { \"match\": \"=/\", \"name\": \"punctuation.separator.tasl.edge.source\" }, { \"match\": \"/=>\", \"name\": \"punctuation.separator.tasl.edge.target\" }, { \"match\": \"=>\", \"name\": \"punctuation.separator.tasl.edge\" }, { \"include\": \"#expression\" }] }, \"export\": { \"match\": \"::\", \"name\": \"keyword.operator.tasl.export\" }, \"expression\": { \"patterns\": [{ \"include\": \"#literal\" }, { \"include\": \"#uri\" }, { \"include\": \"#product\" }, { \"include\": \"#coproduct\" }, { \"include\": \"#reference\" }, { \"include\": \"#optional\" }, { \"include\": \"#identifier\" }] }, \"identifier\": { \"captures\": { \"1\": { \"name\": \"variable\" } }, \"match\": \"([a-zA-Z][a-zA-Z0-9]*)\\\\b\" }, \"key\": { \"match\": \"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\", \"name\": \"markup.bold entity.name.class\" }, \"literal\": { \"patterns\": [{ \"include\": \"#datatype\" }] }, \"namespace\": { \"captures\": { \"1\": { \"name\": \"keyword.control.tasl.namespace\" }, \"2\": { \"patterns\": [{ \"include\": \"#namespaceURI\" }, { \"match\": \"[a-zA-Z][a-zA-Z0-9]*\\\\b\", \"name\": \"entity.name\" }] } }, \"match\": \"(?:^\\\\s*)(namespace)\\\\b(.*)\" }, \"namespaceURI\": { \"match\": \"[a-z]+:[a-zA-Z0-9-._~:\\\\/?#\\\\[\\\\]@!$&'()*+,;%=]+\", \"name\": \"markup.underline.link\" }, \"option\": { \"begin\": \"<-\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.tasl.option\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"optional\": { \"begin\": \"\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"product\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.product\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.product\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#term\" }, { \"include\": \"#component\" }] }, \"reference\": { \"captures\": { \"1\": { \"name\": \"markup.bold keyword.operator\" }, \"2\": { \"patterns\": [{ \"include\": \"#key\" }] } }, \"match\": \"(\\\\*)\\\\s*(.*)\" }, \"term\": { \"match\": \"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\", \"name\": \"entity.other.tasl.key\" }, \"type\": { \"begin\": \"(?:^\\\\s*)(type)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tasl.type\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"uri\": { \"match\": \"<>\", \"name\": \"variable.other.constant\" } }, \"scopeName\": \"source.tasl\" });\nvar tasl = [\n  lang\n];\n\nexport { tasl as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAQ,aAAa;QAAC;KAAO;IAAE,QAAQ;IAAQ,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAQ;QAAG;YAAE,WAAW;QAAS;QAAG;YAAE,WAAW;QAAQ;KAAE;IAAE,cAAc;QAAE,SAAS;YAAE,SAAS;YAAuB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAsC;YAAE;YAAG,SAAS;YAAU,QAAQ;QAAgC;QAAG,aAAa;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAuC;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,aAAa;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,OAAO;YAAO,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA8C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAA8E,QAAQ;QAAgB;QAAG,QAAQ;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAU;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAyC;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAkC;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,UAAU;YAAE,SAAS;YAAM,QAAQ;QAA+B;QAAG,cAAc;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAO;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,cAAc;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAW;YAAE;YAAG,SAAS;QAA4B;QAAG,OAAO;YAAE,SAAS;YAA8E,QAAQ;QAAgC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAY;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAiC;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,SAAS;4BAA2B,QAAQ;wBAAc;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAA8B;QAAG,gBAAgB;YAAE,SAAS;YAAoD,QAAQ;QAAwB;QAAG,UAAU;YAAE,SAAS;YAAM,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAoC;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAO,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAAmB;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAK,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,OAAO;YAAK,eAAe;gBAAE,KAAK;oBAAE,QAAQ;gBAA4C;YAAE;YAAG,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAQ;gBAAG;oBAAE,WAAW;gBAAa;aAAE;QAAC;QAAG,aAAa;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA+B;gBAAG,KAAK;oBAAE,YAAY;wBAAC;4BAAE,WAAW;wBAAO;qBAAE;gBAAC;YAAE;YAAG,SAAS;QAAgB;QAAG,QAAQ;YAAE,SAAS;YAA8E,QAAQ;QAAwB;QAAG,QAAQ;YAAE,SAAS;YAAsB,iBAAiB;gBAAE,KAAK;oBAAE,QAAQ;gBAA4B;YAAE;YAAG,OAAO;YAAK,YAAY;gBAAC;oBAAE,WAAW;gBAAc;aAAE;QAAC;QAAG,OAAO;YAAE,SAAS;YAAM,QAAQ;QAA0B;IAAE;IAAG,aAAa;AAAc;AAC7oH,IAAI,OAAO;IACT;CACD", "ignoreList": [0], "debugId": null}}]}