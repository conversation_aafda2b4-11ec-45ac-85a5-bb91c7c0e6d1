{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/solidity.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Solidity\", \"fileTypes\": [\"sol\"], \"name\": \"solidity\", \"patterns\": [{ \"include\": \"#natspec\" }, { \"include\": \"#declaration-userType\" }, { \"include\": \"#comment\" }, { \"include\": \"#operator\" }, { \"include\": \"#global\" }, { \"include\": \"#control\" }, { \"include\": \"#constant\" }, { \"include\": \"#primitive\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#type-modifier-extended-scope\" }, { \"include\": \"#declaration\" }, { \"include\": \"#function-call\" }, { \"include\": \"#assembly\" }, { \"include\": \"#punctuation\" }], \"repository\": { \"assembly\": { \"patterns\": [{ \"match\": \"\\\\b(assembly)\\\\b\", \"name\": \"keyword.control.assembly\" }, { \"match\": \"\\\\b(let)\\\\b\", \"name\": \"storage.type.assembly\" }] }, \"comment\": { \"patterns\": [{ \"include\": \"#comment-line\" }, { \"include\": \"#comment-block\" }] }, \"comment-block\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block\", \"patterns\": [{ \"include\": \"#comment-todo\" }] }, \"comment-line\": { \"begin\": \"(?<!tp:)//\", \"end\": \"$\", \"name\": \"comment.line\", \"patterns\": [{ \"include\": \"#comment-todo\" }] }, \"comment-todo\": { \"match\": \"(?i)\\\\b(FIXME|TODO|CHANGED|XXX|IDEA|HACK|NOTE|REVIEW|NB|BUG|QUESTION|COMBAK|TEMP|SUPPRESS|LINT|\\\\w+-disable|\\\\w+-suppress)\\\\b(?-i)\", \"name\": \"keyword.comment.todo\" }, \"constant\": { \"patterns\": [{ \"include\": \"#constant-boolean\" }, { \"include\": \"#constant-time\" }, { \"include\": \"#constant-currency\" }] }, \"constant-boolean\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean\" }, \"constant-currency\": { \"match\": \"\\\\b(ether|wei|gwei|finney|szabo)\\\\b\", \"name\": \"constant.language.currency\" }, \"constant-time\": { \"match\": \"\\\\b(seconds|minutes|hours|days|weeks|years)\\\\b\", \"name\": \"constant.language.time\" }, \"control\": { \"patterns\": [{ \"include\": \"#control-flow\" }, { \"include\": \"#control-using\" }, { \"include\": \"#control-import\" }, { \"include\": \"#control-pragma\" }, { \"include\": \"#control-underscore\" }, { \"include\": \"#control-unchecked\" }, { \"include\": \"#control-other\" }] }, \"control-flow\": { \"patterns\": [{ \"match\": \"\\\\b(if|else|for|while|do|break|continue|try|catch|finally|throw|return|global)\\\\b\", \"name\": \"keyword.control.flow\" }, { \"begin\": \"\\\\b(returns)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.flow.return\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declaration-function-parameters\" }] }] }, \"control-import\": { \"patterns\": [{ \"begin\": \"\\\\b(import)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.import\" } }, \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"begin\": \"((?=\\\\{))\", \"end\": \"((?=\\\\}))\", \"patterns\": [{ \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.type.interface\" }] }, { \"match\": \"\\\\b(from)\\\\b\", \"name\": \"keyword.control.import.from\" }, { \"include\": \"#string\" }, { \"include\": \"#punctuation\" }] }, { \"match\": \"\\\\b(import)\\\\b\", \"name\": \"keyword.control.import\" }] }, \"control-other\": { \"match\": \"\\\\b(new|delete|emit)\\\\b\", \"name\": \"keyword.control\" }, \"control-pragma\": { \"captures\": { \"1\": { \"name\": \"keyword.control.pragma\" }, \"2\": { \"name\": \"entity.name.tag.pragma\" }, \"3\": { \"name\": \"constant.other.pragma\" } }, \"match\": \"\\\\b(pragma)(?:\\\\s+([A-Za-z_]\\\\w+)\\\\s+([^\\\\s]+))?\\\\b\" }, \"control-unchecked\": { \"match\": \"\\\\b(unchecked)\\\\b\", \"name\": \"keyword.control.unchecked\" }, \"control-underscore\": { \"match\": \"\\\\b(_)\\\\b\", \"name\": \"constant.other.underscore\" }, \"control-using\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.using\" }, \"2\": { \"name\": \"entity.name.type.library\" }, \"3\": { \"name\": \"keyword.control.for\" }, \"4\": { \"name\": \"entity.name.type\" } }, \"match\": \"\\\\b(using)\\\\b\\\\s+\\\\b([A-Za-z\\\\d_]+)\\\\b\\\\s+\\\\b(for)\\\\b\\\\s+\\\\b([A-Za-z\\\\d_]+)\" }, { \"match\": \"\\\\b(using)\\\\b\", \"name\": \"keyword.control.using\" }] }, \"declaration\": { \"patterns\": [{ \"include\": \"#declaration-contract\" }, { \"include\": \"#declaration-userType\" }, { \"include\": \"#declaration-interface\" }, { \"include\": \"#declaration-library\" }, { \"include\": \"#declaration-function\" }, { \"include\": \"#declaration-modifier\" }, { \"include\": \"#declaration-constructor\" }, { \"include\": \"#declaration-event\" }, { \"include\": \"#declaration-storage\" }, { \"include\": \"#declaration-error\" }] }, \"declaration-constructor\": { \"patterns\": [{ \"begin\": \"\\\\b(constructor)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.constructor\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"begin\": \"\\\\G\\\\s*(?=\\\\()\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declaration-function-parameters\" }] }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#type-modifier-access\" }, { \"include\": \"#function-call\" }] }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.constructor\" } }, \"match\": \"\\\\b(constructor)\\\\b\" }] }, \"declaration-contract\": { \"patterns\": [{ \"begin\": \"\\\\b(contract)\\\\b\\\\s+(\\\\w+)\\\\b\\\\s+\\\\b(is)\\\\b\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.contract\" }, \"2\": { \"name\": \"entity.name.type.contract\" }, \"3\": { \"name\": \"storage.modifier.is\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.type.contract.extend\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.contract\" }, \"2\": { \"name\": \"entity.name.type.contract\" } }, \"match\": \"\\\\b(contract)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }] }, \"declaration-enum\": { \"patterns\": [{ \"begin\": \"\\\\b(enum)\\\\s+(\\\\w+)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.enum\" }, \"2\": { \"name\": \"entity.name.type.enum\" } }, \"end\": \"(?=\\\\})\", \"patterns\": [{ \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"variable.other.enummember\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#comment\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.enum\" }, \"3\": { \"name\": \"entity.name.type.enum\" } }, \"match\": \"\\\\b(enum)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }] }, \"declaration-error\": { \"captures\": { \"1\": { \"name\": \"storage.type.error\" }, \"3\": { \"name\": \"entity.name.type.error\" } }, \"match\": \"\\\\b(error)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }, \"declaration-event\": { \"patterns\": [{ \"begin\": \"\\\\b(event)\\\\b(?:\\\\s+(\\\\w+)\\\\b)?\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.event\" }, \"2\": { \"name\": \"entity.name.type.event\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#type-primitive\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.modifier.indexed\" }, \"2\": { \"name\": \"variable.parameter.event\" } }, \"match\": \"\\\\b(?:(indexed)\\\\s)?(\\\\w+)(?:,\\\\s*|)\" }, { \"include\": \"#punctuation\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.event\" }, \"3\": { \"name\": \"entity.name.type.event\" } }, \"match\": \"\\\\b(event)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }] }, \"declaration-function\": { \"patterns\": [{ \"begin\": \"\\\\b(function)\\\\s+(\\\\w+)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function\" }, \"2\": { \"name\": \"entity.name.function\" } }, \"end\": \"(?=\\\\{|;)\", \"patterns\": [{ \"include\": \"#natspec\" }, { \"include\": \"#global\" }, { \"include\": \"#declaration-function-parameters\" }, { \"include\": \"#type-modifier-access\" }, { \"include\": \"#type-modifier-payable\" }, { \"include\": \"#type-modifier-immutable\" }, { \"include\": \"#type-modifier-extended-scope\" }, { \"include\": \"#control-flow\" }, { \"include\": \"#function-call\" }, { \"include\": \"#modifier-call\" }, { \"include\": \"#punctuation\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.function\" }, \"2\": { \"name\": \"entity.name.function\" } }, \"match\": \"\\\\b(function)\\\\s+([A-Za-z_]\\\\w*)\\\\b\" }] }, \"declaration-function-parameters\": { \"begin\": \"\\\\G\\\\s*(?=\\\\()\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#type-primitive\" }, { \"include\": \"#type-modifier-extended-scope\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.struct\" } }, \"match\": \"\\\\b([A-Z]\\\\w*)\\\\b\" }, { \"include\": \"#variable\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#comment\" }] }, \"declaration-interface\": { \"patterns\": [{ \"begin\": \"\\\\b(interface)\\\\b\\\\s+(\\\\w+)\\\\b\\\\s+\\\\b(is)\\\\b\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.interface\" }, \"2\": { \"name\": \"entity.name.type.interface\" }, \"3\": { \"name\": \"storage.modifier.is\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.type.interface.extend\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.interface\" }, \"2\": { \"name\": \"entity.name.type.interface\" } }, \"match\": \"\\\\b(interface)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }] }, \"declaration-library\": { \"captures\": { \"1\": { \"name\": \"storage.type.library\" }, \"3\": { \"name\": \"entity.name.type.library\" } }, \"match\": \"\\\\b(library)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }, \"declaration-modifier\": { \"patterns\": [{ \"begin\": \"\\\\b(modifier)\\\\b\\\\s*(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.function.modifier\" }, \"2\": { \"name\": \"entity.name.function.modifier\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#declaration-function-parameters\" }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#declaration-function-parameters\" }, { \"include\": \"#type-modifier-access\" }, { \"include\": \"#type-modifier-payable\" }, { \"include\": \"#type-modifier-immutable\" }, { \"include\": \"#type-modifier-extended-scope\" }, { \"include\": \"#function-call\" }, { \"include\": \"#modifier-call\" }, { \"include\": \"#control-flow\" }] }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.modifier\" }, \"3\": { \"name\": \"entity.name.function\" } }, \"match\": \"\\\\b(modifier)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }] }, \"declaration-storage\": { \"patterns\": [{ \"include\": \"#declaration-storage-mapping\" }, { \"include\": \"#declaration-struct\" }, { \"include\": \"#declaration-enum\" }, { \"include\": \"#declaration-storage-field\" }] }, \"declaration-storage-field\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#control\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#type-modifier-access\" }, { \"include\": \"#type-modifier-immutable\" }, { \"include\": \"#type-modifier-extend-scope\" }, { \"include\": \"#type-modifier-payable\" }, { \"include\": \"#type-modifier-constant\" }, { \"include\": \"#primitive\" }, { \"include\": \"#constant\" }, { \"include\": \"#operator\" }, { \"include\": \"#punctuation\" }] }, \"declaration-storage-mapping\": { \"patterns\": [{ \"begin\": \"\\\\b(mapping)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.mapping\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declaration-storage-mapping\" }, { \"include\": \"#type-primitive\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#operator\" }] }, { \"match\": \"\\\\b(mapping)\\\\b\", \"name\": \"storage.type.mapping\" }] }, \"declaration-struct\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.struct\" }, \"3\": { \"name\": \"entity.name.type.struct\" } }, \"match\": \"\\\\b(struct)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }, { \"begin\": \"\\\\b(struct)\\\\b\\\\s*(\\\\w+)?\\\\b\\\\s*(?=\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.struct\" }, \"2\": { \"name\": \"entity.name.type.struct\" } }, \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#type-primitive\" }, { \"include\": \"#variable\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#comment\" }] }] }, \"declaration-userType\": { \"captures\": { \"1\": { \"name\": \"storage.type.userType\" }, \"2\": { \"name\": \"entity.name.type.userType\" }, \"3\": { \"name\": \"storage.modifier.is\" } }, \"match\": \"\\\\b(type)\\\\b\\\\s+(\\\\w+)\\\\b\\\\s+\\\\b(is)\\\\b\" }, \"function-call\": { \"captures\": { \"1\": { \"name\": \"entity.name.function\" }, \"2\": { \"name\": \"punctuation.parameters.begin\" } }, \"match\": \"\\\\b([A-Za-z_]\\\\w*)\\\\s*(\\\\()\" }, \"global\": { \"patterns\": [{ \"include\": \"#global-variables\" }, { \"include\": \"#global-functions\" }] }, \"global-functions\": { \"patterns\": [{ \"match\": \"\\\\b(require|assert|revert)\\\\b\", \"name\": \"keyword.control.exceptions\" }, { \"match\": \"\\\\b(selfdestruct|suicide)\\\\b\", \"name\": \"keyword.control.contract\" }, { \"match\": \"\\\\b(addmod|mulmod|keccak256|sha256|sha3|ripemd160|ecrecover)\\\\b\", \"name\": \"support.function.math\" }, { \"match\": \"\\\\b(unicode)\\\\b\", \"name\": \"support.function.string\" }, { \"match\": \"\\\\b(blockhash|gasleft)\\\\b\", \"name\": \"variable.language.transaction\" }, { \"match\": \"\\\\b(type)\\\\b\", \"name\": \"variable.language.type\" }] }, \"global-variables\": { \"patterns\": [{ \"match\": \"\\\\b(this)\\\\b\", \"name\": \"variable.language.this\" }, { \"match\": \"\\\\b(super)\\\\b\", \"name\": \"variable.language.super\" }, { \"match\": \"\\\\b(abi)\\\\b\", \"name\": \"variable.language.builtin.abi\" }, { \"match\": \"\\\\b(msg\\\\.sender|msg|block|tx|now)\\\\b\", \"name\": \"variable.language.transaction\" }, { \"match\": \"\\\\b(tx\\\\.origin|tx\\\\.gasprice|msg\\\\.data|msg\\\\.sig|msg\\\\.value)\\\\b\", \"name\": \"variable.language.transaction\" }] }, \"modifier-call\": { \"patterns\": [{ \"include\": \"#function-call\" }, { \"match\": \"\\\\b(\\\\w+)\\\\b\", \"name\": \"entity.name.function.modifier\" }] }, \"natspec\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation\", \"patterns\": [{ \"include\": \"#natspec-tags\" }] }, { \"begin\": \"///\", \"end\": \"$\", \"name\": \"comment.block.documentation\", \"patterns\": [{ \"include\": \"#natspec-tags\" }] }] }, \"natspec-tag-author\": { \"match\": \"(@author)\\\\b\", \"name\": \"storage.type.author.natspec\" }, \"natspec-tag-custom\": { \"match\": \"(@custom:\\\\w*)\\\\b\", \"name\": \"storage.type.dev.natspec\" }, \"natspec-tag-dev\": { \"match\": \"(@dev)\\\\b\", \"name\": \"storage.type.dev.natspec\" }, \"natspec-tag-inheritdoc\": { \"match\": \"(@inheritdoc)\\\\b\", \"name\": \"storage.type.author.natspec\" }, \"natspec-tag-notice\": { \"match\": \"(@notice)\\\\b\", \"name\": \"storage.type.dev.natspec\" }, \"natspec-tag-param\": { \"captures\": { \"1\": { \"name\": \"storage.type.param.natspec\" }, \"3\": { \"name\": \"variable.other.natspec\" } }, \"match\": \"(@param)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }, \"natspec-tag-return\": { \"captures\": { \"1\": { \"name\": \"storage.type.return.natspec\" }, \"3\": { \"name\": \"variable.other.natspec\" } }, \"match\": \"(@return)(\\\\s+([A-Za-z_]\\\\w*))?\\\\b\" }, \"natspec-tag-title\": { \"match\": \"(@title)\\\\b\", \"name\": \"storage.type.title.natspec\" }, \"natspec-tags\": { \"patterns\": [{ \"include\": \"#comment-todo\" }, { \"include\": \"#natspec-tag-title\" }, { \"include\": \"#natspec-tag-author\" }, { \"include\": \"#natspec-tag-notice\" }, { \"include\": \"#natspec-tag-dev\" }, { \"include\": \"#natspec-tag-param\" }, { \"include\": \"#natspec-tag-return\" }, { \"include\": \"#natspec-tag-custom\" }, { \"include\": \"#natspec-tag-inheritdoc\" }] }, \"number\": { \"patterns\": [{ \"include\": \"#number-decimal\" }, { \"include\": \"#number-hex\" }, { \"include\": \"#number-scientific\" }] }, \"number-decimal\": { \"match\": \"\\\\b([0-9_]+(\\\\.[0-9_]+)?)\\\\b\", \"name\": \"constant.numeric.decimal\" }, \"number-hex\": { \"match\": \"\\\\b(0[xX][a-fA-F0-9]+)\\\\b\", \"name\": \"constant.numeric.hexadecimal\" }, \"number-scientific\": { \"match\": \"\\\\b(?:0\\\\.(?:0\\\\d|\\\\d[0-9_]?)|\\\\d[0-9_]*(?:\\\\.\\\\d{1,2})?)(?:e[+-]?[0-9_]+)?\", \"name\": \"constant.numeric.scientific\" }, \"operator\": { \"patterns\": [{ \"include\": \"#operator-logic\" }, { \"include\": \"#operator-mapping\" }, { \"include\": \"#operator-arithmetic\" }, { \"include\": \"#operator-binary\" }, { \"include\": \"#operator-assignment\" }] }, \"operator-arithmetic\": { \"match\": \"(\\\\+|-|\\\\/|\\\\*)\", \"name\": \"keyword.operator.arithmetic\" }, \"operator-assignment\": { \"match\": \"(:?=)\", \"name\": \"keyword.operator.assignment\" }, \"operator-binary\": { \"match\": \"(\\\\^|\\\\&|\\\\||<<|>>)\", \"name\": \"keyword.operator.binary\" }, \"operator-logic\": { \"match\": \"(==|!=|<(?!<)|<=|>(?!>)|>=|\\\\&\\\\&|\\\\|\\\\||:(?!=)|\\\\?|!)\", \"name\": \"keyword.operator.logic\" }, \"operator-mapping\": { \"match\": \"(=>)\", \"name\": \"keyword.operator.mapping\" }, \"primitive\": { \"patterns\": [{ \"include\": \"#number-decimal\" }, { \"include\": \"#number-hex\" }, { \"include\": \"#number-scientific\" }, { \"include\": \"#string\" }] }, \"punctuation\": { \"patterns\": [{ \"match\": \";\", \"name\": \"punctuation.terminator.statement\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.accessor\" }, { \"match\": \",\", \"name\": \"punctuation.separator\" }, { \"match\": \"\\\\{\", \"name\": \"punctuation.brace.curly.begin\" }, { \"match\": \"\\\\}\", \"name\": \"punctuation.brace.curly.end\" }, { \"match\": \"\\\\[\", \"name\": \"punctuation.brace.square.begin\" }, { \"match\": \"\\\\]\", \"name\": \"punctuation.brace.square.end\" }, { \"match\": \"\\\\(\", \"name\": \"punctuation.parameters.begin\" }, { \"match\": \"\\\\)\", \"name\": \"punctuation.parameters.end\" }] }, \"string\": { \"patterns\": [{ \"match\": '\\\\\"(?:\\\\\\\\\"|[^\\\\\"])*\\\\\"', \"name\": \"string.quoted.double\" }, { \"match\": \"\\\\'(?:\\\\\\\\'|[^\\\\'])*\\\\'\", \"name\": \"string.quoted.single\" }] }, \"type-modifier-access\": { \"match\": \"\\\\b(internal|external|private|public)\\\\b\", \"name\": \"storage.type.modifier.access\" }, \"type-modifier-constant\": { \"match\": \"\\\\b(constant)\\\\b\", \"name\": \"storage.type.modifier.readonly\" }, \"type-modifier-extended-scope\": { \"match\": \"\\\\b(pure|view|inherited|indexed|storage|memory|virtual|calldata|override|abstract)\\\\b\", \"name\": \"storage.type.modifier.extendedscope\" }, \"type-modifier-immutable\": { \"match\": \"\\\\b(immutable)\\\\b\", \"name\": \"storage.type.modifier.readonly\" }, \"type-modifier-payable\": { \"match\": \"\\\\b(nonpayable|payable)\\\\b\", \"name\": \"storage.type.modifier.payable\" }, \"type-primitive\": { \"patterns\": [{ \"begin\": \"\\\\b(address|string\\\\d*|bytes\\\\d*|int\\\\d*|uint\\\\d*|bool|hash\\\\d*)\\\\b(?:\\\\[\\\\])(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.type.primitive\" } }, \"end\": \"(\\\\))\", \"patterns\": [{ \"include\": \"#primitive\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#global\" }, { \"include\": \"#variable\" }] }, { \"match\": \"\\\\b(address|string\\\\d*|bytes\\\\d*|int\\\\d*|uint\\\\d*|bool|hash\\\\d*)\\\\b\", \"name\": \"support.type.primitive\" }] }, \"variable\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.function\" } }, \"match\": \"\\\\b(_\\\\w+)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"support.variable.property\" } }, \"match\": \"(?:\\\\.)(\\\\w+)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.other\" } }, \"match\": \"\\\\b(\\\\w+)\\\\b\" }] } }, \"scopeName\": \"source.solidity\" });\nvar solidity = [\n  lang\n];\n\nexport { solidity as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAY,aAAa;QAAC;KAAM;IAAE,QAAQ;IAAY,YAAY;QAAC;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAwB;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAa;QAAG;YAAE,WAAW;QAAkB;QAAG;YAAE,WAAW;QAAgC;QAAG;YAAE,WAAW;QAAe;QAAG;YAAE,WAAW;QAAiB;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAe;KAAE;IAAE,cAAc;QAAE,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAwB;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAAQ,OAAO;YAAQ,QAAQ;YAAiB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAc,OAAO;YAAK,QAAQ;YAAgB,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAAsI,QAAQ;QAAuB;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,oBAAoB;YAAE,SAAS;YAAsB,QAAQ;QAA4B;QAAG,qBAAqB;YAAE,SAAS;YAAuC,QAAQ;QAA6B;QAAG,iBAAiB;YAAE,SAAS;YAAkD,QAAQ;QAAyB;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAiB;aAAE;QAAC;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAqF,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAmC;qBAAE;gBAAC;aAAE;QAAC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAkB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAa,OAAO;4BAAa,YAAY;gCAAC;oCAAE,SAAS;oCAAgB,QAAQ;gCAA6B;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAgB,QAAQ;wBAA8B;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAkB,QAAQ;gBAAyB;aAAE;QAAC;QAAG,iBAAiB;YAAE,SAAS;YAA2B,QAAQ;QAAkB;QAAG,kBAAkB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;gBAAG,KAAK;oBAAE,QAAQ;gBAAwB;YAAE;YAAG,SAAS;QAAsD;QAAG,qBAAqB;YAAE,SAAS;YAAqB,QAAQ;QAA4B;QAAG,sBAAsB;YAAE,SAAS;YAAa,QAAQ;QAA4B;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA2B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAAmB;oBAAE;oBAAG,SAAS;gBAA8E;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAAwB;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,2BAA2B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAuB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAkB,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAmC;6BAAE;wBAAC;wBAAG;4BAAE,SAAS;4BAAY,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAwB;gCAAG;oCAAE,WAAW;gCAAiB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAsB;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAgB,QAAQ;wBAAmC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAyC;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA0B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAgB,QAAQ;wBAA4B;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAoB;wBAAG,KAAK;4BAAE,QAAQ;wBAAwB;oBAAE;oBAAG,SAAS;gBAAqC;aAAE;QAAC;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAqB;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,SAAS;QAAsC;QAAG,qBAAqB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmC,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,YAAY;gCAAE,KAAK;oCAAE,QAAQ;gCAAgC;gCAAG,KAAK;oCAAE,QAAQ;gCAA2B;4BAAE;4BAAG,SAAS;wBAAuC;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAqB;wBAAG,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,SAAS;gBAAsC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAa,YAAY;wBAAC;4BAAE,WAAW;wBAAW;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAmC;wBAAG;4BAAE,WAAW;wBAAwB;wBAAG;4BAAE,WAAW;wBAAyB;wBAAG;4BAAE,WAAW;wBAA2B;wBAAG;4BAAE,WAAW;wBAAgC;wBAAG;4BAAE,WAAW;wBAAgB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAiB;wBAAG;4BAAE,WAAW;wBAAe;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAAsC;aAAE;QAAC;QAAG,mCAAmC;YAAE,SAAS;YAAkB,OAAO;YAAW,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAgC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,SAAS;gBAAoB;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;gBAAG;oBAAE,WAAW;gBAAW;aAAE;QAAC;QAAG,yBAAyB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAoD,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;wBAAG,KAAK;4BAAE,QAAQ;wBAAsB;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,SAAS;4BAAgB,QAAQ;wBAAoC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;wBAAG,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,SAAS;gBAA0C;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA2B;YAAE;YAAG,SAAS;QAAwC;QAAG,wBAAwB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA8B,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAiC;wBAAG,KAAK;4BAAE,QAAQ;wBAAgC;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAmC;wBAAG;4BAAE,SAAS;4BAAY,OAAO;4BAAW,YAAY;gCAAC;oCAAE,WAAW;gCAAmC;gCAAG;oCAAE,WAAW;gCAAwB;gCAAG;oCAAE,WAAW;gCAAyB;gCAAG;oCAAE,WAAW;gCAA2B;gCAAG;oCAAE,WAAW;gCAAgC;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAiB;gCAAG;oCAAE,WAAW;gCAAgB;6BAAE;wBAAC;qBAAE;gBAAC;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAwB;wBAAG,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,SAAS;gBAAyC;aAAE;QAAC;QAAG,uBAAuB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAA+B;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAA6B;aAAE;QAAC;QAAG,6BAA6B;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAwB;gBAAG;oBAAE,WAAW;gBAA2B;gBAAG;oBAAE,WAAW;gBAA8B;gBAAG;oBAAE,WAAW;gBAAyB;gBAAG;oBAAE,WAAW;gBAA0B;gBAAG;oBAAE,WAAW;gBAAa;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAY;gBAAG;oBAAE,WAAW;gBAAe;aAAE;QAAC;QAAG,+BAA+B;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAmB,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAuB;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAA+B;wBAAG;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAuB;aAAE;QAAC;QAAG,sBAAsB;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,SAAS;gBAAuC;gBAAG;oBAAE,SAAS;oBAA2C,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAsB;wBAAG,KAAK;4BAAE,QAAQ;wBAA0B;oBAAE;oBAAG,OAAO;oBAAW,YAAY;wBAAC;4BAAE,WAAW;wBAAkB;wBAAG;4BAAE,WAAW;wBAAY;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAW;qBAAE;gBAAC;aAAE;QAAC;QAAG,wBAAwB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAwB;gBAAG,KAAK;oBAAE,QAAQ;gBAA4B;gBAAG,KAAK;oBAAE,QAAQ;gBAAsB;YAAE;YAAG,SAAS;QAA0C;QAAG,iBAAiB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAAuB;gBAAG,KAAK;oBAAE,QAAQ;gBAA+B;YAAE;YAAG,SAAS;QAA8B;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAoB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAiC,QAAQ;gBAA6B;gBAAG;oBAAE,SAAS;oBAAgC,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAmE,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAyB;aAAE;QAAC;QAAG,oBAAoB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgB,QAAQ;gBAAyB;gBAAG;oBAAE,SAAS;oBAAiB,QAAQ;gBAA0B;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAyC,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAsE,QAAQ;gBAAgC;aAAE;QAAC;QAAG,iBAAiB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,SAAS;oBAAgB,QAAQ;gBAAgC;aAAE;QAAC;QAAG,WAAW;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAW,OAAO;oBAAQ,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAO,OAAO;oBAAK,QAAQ;oBAA+B,YAAY;wBAAC;4BAAE,WAAW;wBAAgB;qBAAE;gBAAC;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAgB,QAAQ;QAA8B;QAAG,sBAAsB;YAAE,SAAS;YAAqB,QAAQ;QAA2B;QAAG,mBAAmB;YAAE,SAAS;YAAa,QAAQ;QAA2B;QAAG,0BAA0B;YAAE,SAAS;YAAoB,QAAQ;QAA8B;QAAG,sBAAsB;YAAE,SAAS;YAAgB,QAAQ;QAA2B;QAAG,qBAAqB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA6B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,SAAS;QAAoC;QAAG,sBAAsB;YAAE,YAAY;gBAAE,KAAK;oBAAE,QAAQ;gBAA8B;gBAAG,KAAK;oBAAE,QAAQ;gBAAyB;YAAE;YAAG,SAAS;QAAqC;QAAG,qBAAqB;YAAE,SAAS;YAAe,QAAQ;QAA6B;QAAG,gBAAgB;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAA0B;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAgC,QAAQ;QAA2B;QAAG,cAAc;YAAE,SAAS;YAA6B,QAAQ;QAA+B;QAAG,qBAAqB;YAAE,SAAS;YAA+E,QAAQ;QAA8B;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAoB;gBAAG;oBAAE,WAAW;gBAAuB;gBAAG;oBAAE,WAAW;gBAAmB;gBAAG;oBAAE,WAAW;gBAAuB;aAAE;QAAC;QAAG,uBAAuB;YAAE,SAAS;YAAmB,QAAQ;QAA8B;QAAG,uBAAuB;YAAE,SAAS;YAAS,QAAQ;QAA8B;QAAG,mBAAmB;YAAE,SAAS;YAAuB,QAAQ;QAA0B;QAAG,kBAAkB;YAAE,SAAS;YAA0D,QAAQ;QAAyB;QAAG,oBAAoB;YAAE,SAAS;YAAQ,QAAQ;QAA2B;QAAG,aAAa;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAc;gBAAG;oBAAE,WAAW;gBAAqB;gBAAG;oBAAE,WAAW;gBAAU;aAAE;QAAC;QAAG,eAAe;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAK,QAAQ;gBAAmC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA6B;aAAE;QAAC;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAA2B,QAAQ;gBAAuB;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAAuB;aAAE;QAAC;QAAG,wBAAwB;YAAE,SAAS;YAA4C,QAAQ;QAA+B;QAAG,0BAA0B;YAAE,SAAS;YAAoB,QAAQ;QAAiC;QAAG,gCAAgC;YAAE,SAAS;YAAyF,QAAQ;QAAsC;QAAG,2BAA2B;YAAE,SAAS;YAAqB,QAAQ;QAAiC;QAAG,yBAAyB;YAAE,SAAS;YAA8B,QAAQ;QAAgC;QAAG,kBAAkB;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAsF,iBAAiB;wBAAE,KAAK;4BAAE,QAAQ;wBAAyB;oBAAE;oBAAG,OAAO;oBAAS,YAAY;wBAAC;4BAAE,WAAW;wBAAa;wBAAG;4BAAE,WAAW;wBAAe;wBAAG;4BAAE,WAAW;wBAAU;wBAAG;4BAAE,WAAW;wBAAY;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAuE,QAAQ;gBAAyB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA8B;oBAAE;oBAAG,SAAS;gBAAgB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA4B;oBAAE;oBAAG,SAAS;gBAAmB;gBAAG;oBAAE,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA2B;oBAAE;oBAAG,SAAS;gBAAe;aAAE;QAAC;IAAE;IAAG,aAAa;AAAkB;AACp0hB,IAAI,WAAW;IACb;CACD", "ignoreList": [0], "debugId": null}}]}