{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/shiki%401.17.7/node_modules/shiki/dist/langs/gleam.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Gleam\", \"name\": \"gleam\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#strings\" }, { \"include\": \"#constant\" }, { \"include\": \"#entity\" }, { \"include\": \"#discards\" }], \"repository\": { \"binary_number\": { \"match\": \"\\\\b0[bB]0*1[01_]*\\\\b\", \"name\": \"constant.numeric.binary.gleam\", \"patterns\": [] }, \"comments\": { \"patterns\": [{ \"match\": \"//.*\", \"name\": \"comment.line.gleam\" }] }, \"constant\": { \"patterns\": [{ \"include\": \"#binary_number\" }, { \"include\": \"#octal_number\" }, { \"include\": \"#hexadecimal_number\" }, { \"include\": \"#decimal_number\" }, { \"include\": \"#boolean\" }, { \"match\": \"[A-Z][0-9A-Za-z]*\", \"name\": \"entity.name.type.gleam\" }] }, \"decimal_number\": { \"match\": \"\\\\b(0*[1-9][0-9_]*|0)(\\\\.(0*[1-9][0-9_]*|0)?(e-?0*[1-9]\\\\d*)?)?\\\\b\", \"name\": \"constant.numeric.decimal.gleam\", \"patterns\": [] }, \"discards\": { \"match\": \"\\\\b_(?:[\\\\w]+)?\\\\b\", \"name\": \"comment.unused.gleam\" }, \"entity\": { \"patterns\": [{ \"begin\": \"\\\\b([a-z][\\\\w]*)\\\\b[\\\\s]*\\\\(\", \"captures\": { \"1\": { \"name\": \"entity.name.function.gleam\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": \"\\\\b([a-z][\\\\w]*):\\\\s\", \"name\": \"variable.parameter.gleam\" }, { \"match\": \"\\\\b([a-z][\\\\w]*):\", \"name\": \"entity.name.namespace.gleam\" }] }, \"hexadecimal_number\": { \"match\": \"\\\\b0[xX]0*[1-9a-zA-Z][0-9a-zA-Z]*\\\\b\", \"name\": \"constant.numeric.hexadecimal.gleam\", \"patterns\": [] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(as|use|case|if|fn|import|let|assert|pub|type|opaque|const|todo|panic|else|try)\\\\b\", \"name\": \"keyword.control.gleam\" }, { \"match\": \"(<-|->)\", \"name\": \"keyword.operator.arrow.gleam\" }, { \"match\": \"\\\\|>\", \"name\": \"keyword.operator.pipe.gleam\" }, { \"match\": \"\\\\.\\\\.\", \"name\": \"keyword.operator.splat.gleam\" }, { \"match\": \"(==|!=)\", \"name\": \"keyword.operator.comparison.gleam\" }, { \"match\": \"(<=\\\\.|>=\\\\.|<\\\\.|>\\\\.)\", \"name\": \"keyword.operator.comparison.float.gleam\" }, { \"match\": \"(<=|>=|<|>)\", \"name\": \"keyword.operator.comparison.int.gleam\" }, { \"match\": \"(&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.gleam\" }, { \"match\": \"<>\", \"name\": \"keyword.operator.string.gleam\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.other.gleam\" }, { \"match\": \"(\\\\+\\\\.|-\\\\.|/\\\\.|\\\\*\\\\.)\", \"name\": \"keyword.operator.arithmetic.float.gleam\" }, { \"match\": \"(\\\\+|-|/|\\\\*|%)\", \"name\": \"keyword.operator.arithmetic.int.gleam\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.gleam\" }] }, \"octal_number\": { \"match\": \"\\\\b0[oO]0*[1-7][0-7]*\\\\b\", \"name\": \"constant.numeric.octal.gleam\", \"patterns\": [] }, \"strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.gleam\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.gleam\" }] } }, \"scopeName\": \"source.gleam\" });\nvar gleam = [\n  lang\n];\n\nexport { gleam as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC;IAAE,eAAe;IAAS,QAAQ;IAAS,YAAY;QAAC;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAW;QAAG;YAAE,WAAW;QAAY;QAAG;YAAE,WAAW;QAAU;QAAG;YAAE,WAAW;QAAY;KAAE;IAAE,cAAc;QAAE,iBAAiB;YAAE,SAAS;YAAwB,QAAQ;YAAiC,YAAY,EAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAQ,QAAQ;gBAAqB;aAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,WAAW;gBAAiB;gBAAG;oBAAE,WAAW;gBAAgB;gBAAG;oBAAE,WAAW;gBAAsB;gBAAG;oBAAE,WAAW;gBAAkB;gBAAG;oBAAE,WAAW;gBAAW;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAAyB;aAAE;QAAC;QAAG,kBAAkB;YAAE,SAAS;YAAsE,QAAQ;YAAkC,YAAY,EAAE;QAAC;QAAG,YAAY;YAAE,SAAS;YAAsB,QAAQ;QAAuB;QAAG,UAAU;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAgC,YAAY;wBAAE,KAAK;4BAAE,QAAQ;wBAA6B;oBAAE;oBAAG,OAAO;oBAAO,YAAY;wBAAC;4BAAE,WAAW;wBAAQ;qBAAE;gBAAC;gBAAG;oBAAE,SAAS;oBAAwB,QAAQ;gBAA2B;gBAAG;oBAAE,SAAS;oBAAqB,QAAQ;gBAA8B;aAAE;QAAC;QAAG,sBAAsB;YAAE,SAAS;YAAwC,QAAQ;YAAsC,YAAY,EAAE;QAAC;QAAG,YAAY;YAAE,YAAY;gBAAC;oBAAE,SAAS;oBAAyF,QAAQ;gBAAwB;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAQ,QAAQ;gBAA8B;gBAAG;oBAAE,SAAS;oBAAU,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAAW,QAAQ;gBAAoC;gBAAG;oBAAE,SAAS;oBAA2B,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAe,QAAQ;gBAAiC;gBAAG;oBAAE,SAAS;oBAAM,QAAQ;gBAAgC;gBAAG;oBAAE,SAAS;oBAAO,QAAQ;gBAA+B;gBAAG;oBAAE,SAAS;oBAA6B,QAAQ;gBAA0C;gBAAG;oBAAE,SAAS;oBAAmB,QAAQ;gBAAwC;gBAAG;oBAAE,SAAS;oBAAK,QAAQ;gBAAoC;aAAE;QAAC;QAAG,gBAAgB;YAAE,SAAS;YAA4B,QAAQ;YAAgC,YAAY,EAAE;QAAC;QAAG,WAAW;YAAE,SAAS;YAAK,OAAO;YAAK,QAAQ;YAA8B,YAAY;gBAAC;oBAAE,SAAS;oBAAS,QAAQ;gBAAkC;aAAE;QAAC;IAAE;IAAG,aAAa;AAAe;AACzrF,IAAI,QAAQ;IACV;CACD", "ignoreList": [0], "debugId": null}}]}